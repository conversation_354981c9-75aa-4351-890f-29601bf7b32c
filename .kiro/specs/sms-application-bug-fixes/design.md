# Design Document

## Overview

This design document outlines the technical approach for implementing bug fixes and improvements to the SMS application. The solution addresses Unicode detection, SMS character counting, multi-level impersonation, access control, UI cleanup, and admin notice functionality. The design follows Laravel best practices and maintains the existing application architecture.

## Architecture

The solution maintains the existing MVC architecture with the following components:
- **Controllers**: Handle HTTP requests and business logic
- **Models**: Represent data structures and relationships
- **Views**: Present user interfaces with Blade templates
- **JavaScript**: Provide client-side functionality and real-time updates
- **Middleware**: Handle authentication and authorization
- **Database**: Store notices and impersonation session data

## Components and Interfaces

### 1. Unicode Detection System

**Frontend Component (JavaScript)**
- Location: `public/js/script.message.js` (enhanced)
- Function: `detectUnicodeAndUpdateMessageType()`
- Triggers: Real-time on textarea input events
- Logic: Uses regex `/[^\x00-\x7F]/` to detect non-ASCII characters

**Implementation Approach:**
```javascript
function detectUnicodeAndUpdateMessageType(textareaElement) {
    const content = textareaElement.value;
    const hasUnicode = /[^\x00-\x7F]/.test(content);
    const messageTypeRadios = document.querySelectorAll('input[name="sms_type"]');
    
    if (hasUnicode) {
        // Auto-select unicode or flash_unicode based on current selection
        const currentFlash = document.querySelector('input[name="sms_type"][value="flash"]:checked');
        const targetValue = currentFlash ? 'flash_unicode' : 'unicode';
        document.querySelector(`input[name="sms_type"][value="${targetValue}"]`).checked = true;
    } else {
        // Revert to text or flash if currently unicode
        const currentUnicode = document.querySelector('input[name="sms_type"][value*="unicode"]:checked');
        if (currentUnicode) {
            const targetValue = currentUnicode.value.includes('flash') ? 'flash' : 'text';
            document.querySelector(`input[name="sms_type"][value="${targetValue}"]`).checked = true;
        }
    }
}
```

### 2. SMS Character Count Display Fix

**Frontend Component (JavaScript)**
- Location: `public/js/script.message.js` (enhanced)
- Target: Dynamic SMS page character count display
- Fix: Ensure `smsCountInfo` element is properly updated

**Implementation Approach:**
```javascript
// Enhanced updateCharacterCount function for all SMS forms
function updateCharacterCount(textareaId, countInfoId) {
    const textarea = document.getElementById(textareaId);
    const countInfo = document.getElementById(countInfoId);
    
    if (!textarea || !countInfo) return;
    
    const content = textarea.value;
    const isUnicode = /[^\x00-\x7F]/.test(content);
    const charLimit = isUnicode ? 70 : 160;
    const maxChars = isUnicode ? 670 : 1530;
    
    const currentLength = content.length;
    const smsCount = Math.ceil(currentLength / charLimit);
    const remainingChars = maxChars - currentLength;
    
    countInfo.textContent = `${currentLength} Characters | ${remainingChars} Characters Left | ${smsCount} SMS (${charLimit} Char./SMS)`;
}
```

### 3. Multi-Level Impersonation System

**Backend Components:**

**Enhanced ImpersonationController**
- New methods: `startMultiLevel()`, `stopToLevel()`, `getImpersonationChain()`
- Session management: Store impersonation chain as array
- Security: Restrict to super-admin only

**Session Structure:**
```php
// Session data structure for multi-level impersonation
'impersonation_chain' => [
    ['user_id' => 1, 'user_name' => 'Super Admin', 'level' => 0],
    ['user_id' => 5, 'user_name' => 'Master Reseller', 'level' => 1],
    ['user_id' => 10, 'user_name' => 'Reseller', 'level' => 2],
]
```

**Enhanced ImpersonationLog Model**
- New fields: `level`, `chain_data`
- Methods: `logMultiLevelStart()`, `logMultiLevelStop()`

### 4. Support Tickets Access Control

**Middleware Enhancement**
- Location: `app/Http/Middleware/` (new middleware or route-specific)
- Logic: Check for super-admin role before allowing access
- Implementation: Route-specific middleware in `routes/admin.php`

**Route Protection:**
```php
Route::prefix('support-tickets')->name('support-tickets.')
    ->middleware(['role:super-admin'])
    ->group(function () {
        // Support ticket routes
    });
```

### 5. Admin Notice System

**Database Schema:**
```sql
CREATE TABLE notices (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    created_by BIGINT UNSIGNED NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP NULL DEFAULT NULL,
    updated_at TIMESTAMP NULL DEFAULT NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
);
```

**Model Structure:**
```php
class Notice extends Model
{
    protected $fillable = ['title', 'description', 'created_by', 'is_active'];
    
    public function creator() {
        return $this->belongsTo(User::class, 'created_by');
    }
    
    public function scopeActive($query) {
        return $query->where('is_active', true);
    }
}
```

**Controller Structure:**
```php
class NoticeController extends Controller
{
    // Admin methods: index, create, store, edit, update, destroy
    // User methods: publicIndex (view-only)
}
```

### 6. Sidebar Menu Cleanup

**View Modification**
- Location: `resources/views/layouts/aside.blade.php`
- Action: Comment out or conditionally hide specific menu items
- Items to hide: "My Statements", "Language switcher"

**Implementation Approach:**
```blade
{{-- Commented out menu items --}}
{{-- 
<li class="menu-item">
    <a class="menu-link" href="#">My Statements</a>
</li>
<li class="menu-item">
    <a class="menu-link" href="#">Language switcher</a>
</li>
--}}
```

## Data Models

### Notice Model
```php
class Notice extends Model
{
    protected $fillable = [
        'title',
        'description', 
        'created_by',
        'is_active'
    ];
    
    protected $casts = [
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];
    
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
}
```

### Enhanced ImpersonationLog Model
```php
class ImpersonationLog extends Model
{
    protected $fillable = [
        'impersonator_id',
        'impersonated_id', 
        'action',
        'ip_address',
        'user_agent',
        'level',
        'chain_data'
    ];
    
    protected $casts = [
        'chain_data' => 'array'
    ];
}
```

## Error Handling

### Unicode Detection
- **Client-side**: Graceful fallback if JavaScript fails
- **Server-side**: Validation to ensure message type matches content

### Multi-Level Impersonation
- **Session validation**: Check chain integrity before each impersonation action
- **User existence**: Verify all users in chain still exist
- **Permission validation**: Ensure super-admin status throughout chain

### Notice System
- **Input validation**: Sanitize title and description
- **Authorization**: Verify admin permissions for CRUD operations
- **Database errors**: Handle constraint violations gracefully

## Testing Strategy

### Unit Tests
1. **Unicode Detection**: Test regex patterns with various character sets
2. **Character Counting**: Verify SMS count calculations for different content types
3. **Impersonation Logic**: Test session chain management
4. **Notice CRUD**: Test model operations and relationships

### Integration Tests
1. **SMS Forms**: Test complete flow from input to submission
2. **Impersonation Flow**: Test multi-level impersonation scenarios
3. **Access Control**: Test support ticket access restrictions
4. **Notice Display**: Test notice visibility across user roles

### Feature Tests
1. **SMS Sending**: End-to-end SMS creation with Unicode detection
2. **User Management**: Complete impersonation workflows
3. **Admin Functions**: Notice management and support ticket access
4. **UI Interactions**: JavaScript functionality and real-time updates

### Browser Tests
1. **Cross-browser**: Test JavaScript functionality across browsers
2. **Responsive**: Ensure UI changes work on different screen sizes
3. **Accessibility**: Verify keyboard navigation and screen reader compatibility

## Security Considerations

### Multi-Level Impersonation
- **Role verification**: Continuous super-admin role checking
- **Session security**: Encrypted session data for impersonation chain
- **Audit logging**: Comprehensive logging of all impersonation actions

### Access Control
- **Route protection**: Middleware-based access control
- **Role-based permissions**: Leverage existing Spatie permissions
- **CSRF protection**: Maintain CSRF tokens for all forms

### Notice System
- **Input sanitization**: Prevent XSS attacks in notice content
- **Authorization checks**: Verify admin permissions for all operations
- **SQL injection prevention**: Use Eloquent ORM for database operations

## Performance Considerations

### JavaScript Optimizations
- **Debounced input**: Prevent excessive Unicode detection calls
- **Efficient DOM queries**: Cache frequently accessed elements
- **Memory management**: Clean up event listeners properly

### Database Optimizations
- **Indexes**: Add indexes on frequently queried notice fields
- **Pagination**: Implement pagination for notice listings
- **Caching**: Cache active notices for better performance

### Session Management
- **Minimal data**: Store only essential impersonation chain data
- **Cleanup**: Regular cleanup of expired impersonation sessions