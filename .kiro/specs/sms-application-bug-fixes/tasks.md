# Implementation Plan

- [x] 1. Set up database structure for admin notices
  - Create migration for notices table with title, description, created_by, is_active fields
  - Add foreign key constraint to users table
  - Run migration to create the table structure
  - _Requirements: 6.2, 6.6_

- [x] 2. Create Notice model and relationships
  - Implement Notice model with fillable fields and casts
  - Add creator relationship to User model
  - Add active scope for filtering active notices
  - Write unit tests for Notice model functionality
  - _Requirements: 6.2, 6.7_

- [x] 3. Implement Notice controller with CRUD operations
  - Create NoticeController with admin methods (index, create, store, edit, update, destroy)
  - Add public index method for user view-only access
  - Implement proper authorization checks for admin operations
  - Add validation rules for notice creation and updates
  - _Requirements: 6.1, 6.5_

- [x] 4. Create notice management views
  - Design admin notice management interface with create/edit forms
  - Create public notice viewing interface for all users
  - Implement responsive design consistent with existing UI
  - Add proper form validation and error handling
  - _Requirements: 6.6, 6.7_

- [x] 5. Add notice routes and sidebar menu
  - Add notice routes to admin.php and web.php
  - Update sidebar navigation to include notices menu for all users
  - Ensure proper route protection for admin operations
  - Test route accessibility for different user roles
  - _Requirements: 6.4, 6.5_

- [x] 6. Enhance JavaScript for Unicode detection in SMS forms
  - Create detectUnicodeAndUpdateMessageType function in script.message.js
  - Add event listeners to all SMS content textareas (SMS Recipient, Group Contact, Dynamic SMS)
  - Implement automatic message type selection based on Unicode detection
  - Test Unicode detection with various character sets and languages
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5_

- [x] 7. Fix SMS character count display in Dynamic SMS page
  - Locate and fix the smsCountInfo element display issue in send-dynamic-sms.blade.php
  - Ensure character count updates properly below the sms_content textarea
  - Implement real-time character counting with Unicode support
  - Test character count accuracy for both ASCII and Unicode content
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5_

- [x] 8. Enhance impersonation system for multi-level support
  - Modify ImpersonationController to support impersonation chains
  - Update session management to store impersonation chain as array
  - Implement startMultiLevel and stopToLevel methods
  - Add proper session validation and cleanup
  - _Requirements: 3.1, 3.2, 3.3, 3.4_

- [x] 9. Update impersonation logging for multi-level tracking
  - Add level and chain_data fields to impersonation_logs table migration
  - Enhance ImpersonationLog model with new fields and methods
  - Implement comprehensive logging for multi-level impersonation actions
  - Update existing logging calls to include level information
  - _Requirements: 3.4, 3.5_

- [x] 10. Implement access control for support tickets
  - Add role-based middleware to support ticket routes in admin.php to restrict access to super-admin only
  - Update sidebar to conditionally show support tickets menu only for super-admin users
  - Test access control with different user roles to ensure proper restriction
  - _Requirements: 4.1, 4.2, 4.3, 4.5_

- [x] 11. Clean up sidebar menu items
  - Comment out or hide "My Statements" menu item in aside_menu.blade.php
  - Comment out or hide "Language switcher" menu item in aside_menu.blade.php
  - Ensure sidebar layout remains visually consistent
  - Verify hidden menu items are not accessible through any routes
  - _Requirements: 5.1, 5.2, 5.3, 5.4, 5.5_

- [x] 12. Create Notice policy for authorization
  - Create NoticePolicy with proper authorization methods (viewAny, view, create, update, delete)
  - Register the policy in AuthServiceProvider
  - Ensure only admin users can manage notices while all users can view them
  - _Requirements: 6.1, 6.5_

- [x] 13. Add notice routes to web.php
  - Add public notice routes to web.php for user access
  - Add admin notice routes to admin.php with proper middleware protection
  - Ensure route names are consistent with controller methods
  - _Requirements: 6.4, 6.5_

- [x] 14. Write comprehensive tests for all new functionality
  - Create unit tests for Notice model and relationships
  - Write feature tests for notice CRUD operations
  - Add tests for Unicode detection JavaScript functionality
  - Create tests for multi-level impersonation scenarios
  - Test access control for support tickets and notices
  - _Requirements: All requirements validation_

- [x] 15. Create database seeders for testing
  - Create NoticeSeeder with sample notices for testing
  - Update existing seeders to include test data for impersonation scenarios
  - Ensure test data covers all user roles and permission levels
  - Add factory classes for Notice model
  - _Requirements: 6.7, 6.8_

- [x] 16. Final integration testing and bug fixes
  - Test complete SMS sending workflow with Unicode detection
  - Verify multi-level impersonation works across all user hierarchies
  - Test notice system with different user roles and permissions
  - Perform end-to-end testing of all modified functionality
  - Fix any bugs discovered during integration testing
  - _Requirements: All requirements final validation_