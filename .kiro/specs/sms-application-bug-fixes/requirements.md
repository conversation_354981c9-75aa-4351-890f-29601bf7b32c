# Requirements Document

## Introduction

This document outlines the requirements for implementing bug fixes and improvements to the SMS application. The application currently has issues with Unicode detection, SMS character counting, impersonation functionality, support ticket access control, sidebar menu cleanup, and lacks an admin notice feature. These improvements will enhance user experience, fix existing bugs, and add essential administrative functionality.

## Requirements

### Requirement 1

**User Story:** As a user sending SMS messages, I want the system to automatically detect Unicode characters and select the appropriate message type, so that my messages are sent with the correct encoding without manual intervention.

#### Acceptance Criteria

1. WH<PERSON> a user types any message content in the SMS Recipient form THEN the system SHALL automatically detect if the content contains Unicode characters
2. WHEN Unicode characters are detected THEN the system SHALL automatically select the "Unicode" message type radio button
3. WHEN the message content is changed to non-Unicode THEN the system SHALL revert to the default "Text" message type
4. WHEN a user types any message content in the Group Contact form THEN the system SHALL automatically detect Unicode and select appropriate message type
5. WHEN a user types any message content in the Dynamic SMS form THEN the system SHALL automatically detect Unicode and select appropriate message type

### Requirement 2

**User Story:** As a user sending Dynamic SMS, I want to see the SMS character count information below the message content textarea, so that I can monitor my message length and cost.

#### Acceptance Criteria

1. WHEN a user accesses the Dynamic SMS page THEN the SMS count information SHALL be displayed below the sms_content textarea
2. WHEN a user types in the sms_content textarea THEN the character count SHALL update in real-time
3. WHEN the message contains Unicode characters THEN the count SHALL reflect Unicode SMS limits (70 chars per SMS)
4. WHEN the message contains only ASCII characters THEN the count SHALL reflect standard SMS limits (160 chars per SMS)
5. WHEN the character count exceeds single SMS limits THEN the system SHALL show the number of SMS segments required

### Requirement 3

**User Story:** As a super admin, I want to perform multi-level impersonation (admin -> master reseller -> reseller -> user), so that I can troubleshoot issues at any level of the user hierarchy.

#### Acceptance Criteria

1. WHEN a super admin impersonates a master reseller THEN the admin SHALL be able to further impersonate users under that master reseller
2. WHEN a super admin is impersonating a master reseller THEN the admin SHALL be able to impersonate resellers under that master reseller
3. WHEN a super admin is impersonating through multiple levels THEN the admin SHALL be able to return to the original admin account
4. WHEN multi-level impersonation is active THEN the system SHALL maintain a proper session chain to allow returning to previous levels
5. IF the user is not a super admin THEN multi-level impersonation SHALL NOT be available

### Requirement 4

**User Story:** As a super admin, I want exclusive access to the support tickets management page, so that sensitive customer support information is properly secured.

#### Acceptance Criteria

1. WHEN a super admin accesses /admin/support-tickets THEN the page SHALL load successfully
2. WHEN a non-super admin user attempts to access /admin/support-tickets THEN the system SHALL deny access with appropriate error message
3. WHEN accessing support tickets THEN only users with super-admin role SHALL be permitted
4. WHEN the support tickets page loads THEN it SHALL display all support tickets with proper management functionality
5. WHEN non-super admin users view the sidebar THEN the support tickets menu item SHALL NOT be visible

### Requirement 5

**User Story:** As a user, I want a clean sidebar interface without unnecessary menu items, so that I can focus on the essential features I need.

#### Acceptance Criteria

1. WHEN any user views the sidebar THEN the "My Statements" menu item SHALL be hidden or commented out
2. WHEN any user views the sidebar THEN the "Language switcher" menu item SHALL be hidden or commented out
3. WHEN the sidebar loads THEN it SHALL maintain all other existing functionality
4. WHEN menu items are hidden THEN the sidebar layout SHALL remain visually consistent
5. WHEN users navigate the application THEN the hidden menu items SHALL NOT be accessible through any route

### Requirement 6

**User Story:** As an admin, I want to create and manage important notices that all users can see, so that I can communicate system updates, maintenance schedules, and important announcements effectively.

#### Acceptance Criteria

1. WHEN an admin accesses the notices section THEN the admin SHALL be able to create, edit, and delete notices
2. WHEN a notice is created THEN it SHALL have a title, description, and creation timestamp
3. WHEN any user logs into the system THEN they SHALL be able to view all active notices
4. WHEN the notices menu is added to the sidebar THEN it SHALL be visible to all users
5. WHEN non-admin users access notices THEN they SHALL only be able to view notices, not modify them
6. WHEN notices are displayed THEN they SHALL show the title, description, and creation date in a user-friendly format
7. WHEN multiple notices exist THEN they SHALL be ordered by creation date (newest first)
8. WHEN a notice is deleted THEN it SHALL be immediately removed from all user views