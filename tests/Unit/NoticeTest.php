<?php

namespace Tests\Unit;

use App\Models\Notice;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class NoticeTest extends TestCase
{
    use RefreshDatabase;

    public function test_notice_can_be_created()
    {
        $user = User::factory()->create();
        
        $notice = Notice::create([
            'title' => 'Test Notice',
            'description' => 'This is a test notice description.',
            'created_by' => $user->id,
            'is_active' => true,
        ]);

        $this->assertInstanceOf(Notice::class, $notice);
        $this->assertEquals('Test Notice', $notice->title);
        $this->assertEquals('This is a test notice description.', $notice->description);
        $this->assertEquals($user->id, $notice->created_by);
        $this->assertTrue($notice->is_active);
    }

    public function test_notice_belongs_to_creator()
    {
        $user = User::factory()->create();
        $notice = Notice::factory()->create(['created_by' => $user->id]);

        $this->assertInstanceOf(User::class, $notice->creator);
        $this->assertEquals($user->id, $notice->creator->id);
    }

    public function test_user_has_many_notices()
    {
        $user = User::factory()->create();
        $notices = Notice::factory()->count(3)->create(['created_by' => $user->id]);

        $this->assertCount(3, $user->notices);
        $this->assertInstanceOf(Notice::class, $user->notices->first());
    }

    public function test_active_scope_filters_active_notices()
    {
        $user = User::factory()->create();
        Notice::factory()->create(['created_by' => $user->id, 'is_active' => true]);
        Notice::factory()->create(['created_by' => $user->id, 'is_active' => false]);

        $activeNotices = Notice::active()->get();
        
        $this->assertCount(1, $activeNotices);
        $this->assertTrue($activeNotices->first()->is_active);
    }

    public function test_notice_casts_is_active_to_boolean()
    {
        $user = User::factory()->create();
        $notice = Notice::factory()->create(['created_by' => $user->id, 'is_active' => 1]);

        $this->assertIsBool($notice->is_active);
        $this->assertTrue($notice->is_active);
    }

    public function test_notice_fillable_attributes()
    {
        $notice = new Notice();
        $fillable = $notice->getFillable();

        $expectedFillable = ['title', 'description', 'created_by', 'is_active'];
        
        $this->assertEquals($expectedFillable, $fillable);
    }
}