<?php

namespace Tests\Unit;

use App\Http\Services\PaymentGateways\Gateways\Ssl;
use App\Models\Gateway;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Tests\TestCase;

class SslGatewayIntegrationTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function ssl_gateway_uses_database_test_mode_for_api_domain()
    {
        // Create a Gateway model with test mode enabled
        $gateway = Gateway::create([
            'user_id' => 1,
            'company_id' => 1,
            'name' => 'SSLCommerz Test',
            'class_name' => 'Ssl',
            'username' => 'test_store_id',
            'password' => 'test_store_password',
            'test_mode' => 'yes',
            'status' => 'active',
        ]);

        // Mock config values
        config([
            'sslcommerz.apiCredentials.store_id' => 'config_store_id',
            'sslcommerz.apiCredentials.store_password' => 'config_store_password',
            'sslcommerz.apiDomain' => 'https://securepay.sslcommerz.com', // Production in config
            'sslcommerz.apiUrl.make_payment' => '/gwprocess/v4/api.php',
        ]);

        $request = new Request();
        $ssl = new Ssl($request, $gateway);

        // Use reflection to access the SslCommerceNotification instance
        $reflection = new \ReflectionClass($ssl);
        $sslProperty = $reflection->getProperty('ssl');
        $sslProperty->setAccessible(true);
        $sslCommerce = $sslProperty->getValue($ssl);

        // Use reflection to access the getApiDomain method
        $sslReflection = new \ReflectionClass($sslCommerce);
        $getApiDomain = $sslReflection->getMethod('getApiDomain');
        $getApiDomain->setAccessible(true);

        // Should use sandbox domain because test_mode is 'yes'
        $apiDomain = $getApiDomain->invoke($sslCommerce);
        $this->assertEquals('https://sandbox.sslcommerz.com', $apiDomain);
    }

    /** @test */
    public function ssl_gateway_uses_production_domain_when_test_mode_is_no()
    {
        // Create a Gateway model with test mode disabled
        $gateway = Gateway::create([
            'user_id' => 1,
            'company_id' => 1,
            'name' => 'SSLCommerz Production',
            'class_name' => 'Ssl',
            'username' => 'prod_store_id',
            'password' => 'prod_store_password',
            'test_mode' => 'no',
            'status' => 'active',
        ]);

        // Mock config values
        config([
            'sslcommerz.apiCredentials.store_id' => 'config_store_id',
            'sslcommerz.apiCredentials.store_password' => 'config_store_password',
            'sslcommerz.apiDomain' => 'https://sandbox.sslcommerz.com', // Sandbox in config
            'sslcommerz.apiUrl.make_payment' => '/gwprocess/v4/api.php',
        ]);

        $request = new Request();
        $ssl = new Ssl($request, $gateway);

        // Use reflection to access the SslCommerceNotification instance
        $reflection = new \ReflectionClass($ssl);
        $sslProperty = $reflection->getProperty('ssl');
        $sslProperty->setAccessible(true);
        $sslCommerce = $sslProperty->getValue($ssl);

        // Use reflection to access the getApiDomain method
        $sslReflection = new \ReflectionClass($sslCommerce);
        $getApiDomain = $sslReflection->getMethod('getApiDomain');
        $getApiDomain->setAccessible(true);

        // Should use production domain because test_mode is 'no'
        $apiDomain = $getApiDomain->invoke($sslCommerce);
        $this->assertEquals('https://securepay.sslcommerz.com', $apiDomain);
    }

    /** @test */
    public function ssl_gateway_uses_database_credentials_over_config()
    {
        // Create a Gateway model
        $gateway = Gateway::create([
            'user_id' => 1,
            'company_id' => 1,
            'name' => 'SSLCommerz Test',
            'class_name' => 'Ssl',
            'username' => 'db_store_id',
            'password' => 'db_store_password',
            'test_mode' => 'yes',
            'status' => 'active',
        ]);

        // Mock config values (different from database)
        config([
            'sslcommerz.apiCredentials.store_id' => 'config_store_id',
            'sslcommerz.apiCredentials.store_password' => 'config_store_password',
        ]);

        $request = new Request();
        $ssl = new Ssl($request, $gateway);

        // Use reflection to access the SslCommerceNotification instance
        $reflection = new \ReflectionClass($ssl);
        $sslProperty = $reflection->getProperty('ssl');
        $sslProperty->setAccessible(true);
        $sslCommerce = $sslProperty->getValue($ssl);

        // Use reflection to access the credential getters
        $sslReflection = new \ReflectionClass($sslCommerce);
        $getStoreId = $sslReflection->getMethod('getStoreId');
        $getStoreId->setAccessible(true);
        $getStorePassword = $sslReflection->getMethod('getStorePassword');
        $getStorePassword->setAccessible(true);

        // Should use database credentials, not config
        $this->assertEquals('db_store_id', $getStoreId->invoke($sslCommerce));
        $this->assertEquals('db_store_password', $getStorePassword->invoke($sslCommerce));
    }
}
