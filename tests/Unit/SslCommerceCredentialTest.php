<?php

namespace Tests\Unit;

use App\Library\SslCommerce\SslCommerceNotification;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SslCommerceCredentialTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_uses_database_credentials_when_set()
    {
        // Mock config values
        config([
            'sslcommerz.apiCredentials.store_id' => 'config_store_id',
            'sslcommerz.apiCredentials.store_password' => 'config_store_password',
        ]);

        $ssl = new SslCommerceNotification();
        
        // Set database credentials
        $ssl->setCredentials('db_store_id', 'db_store_password');
        
        // Use reflection to access protected methods
        $reflection = new \ReflectionClass($ssl);
        $getStoreId = $reflection->getMethod('getStoreId');
        $getStoreId->setAccessible(true);
        $getStorePassword = $reflection->getMethod('getStorePassword');
        $getStorePassword->setAccessible(true);
        
        // Should use database credentials, not config
        $this->assertEquals('db_store_id', $getStoreId->invoke($ssl));
        $this->assertEquals('db_store_password', $getStorePassword->invoke($ssl));
    }

    /** @test */
    public function it_falls_back_to_config_credentials_when_database_credentials_not_set()
    {
        // Mock config values
        config([
            'sslcommerz.apiCredentials.store_id' => 'config_store_id',
            'sslcommerz.apiCredentials.store_password' => 'config_store_password',
        ]);

        $ssl = new SslCommerceNotification();
        
        // Don't set database credentials
        
        // Use reflection to access protected methods
        $reflection = new \ReflectionClass($ssl);
        $getStoreId = $reflection->getMethod('getStoreId');
        $getStoreId->setAccessible(true);
        $getStorePassword = $reflection->getMethod('getStorePassword');
        $getStorePassword->setAccessible(true);
        
        // Should use config credentials as fallback
        $this->assertEquals('config_store_id', $getStoreId->invoke($ssl));
        $this->assertEquals('config_store_password', $getStorePassword->invoke($ssl));
    }

    /** @test */
    public function it_handles_empty_database_credentials_gracefully()
    {
        // Mock config values
        config([
            'sslcommerz.apiCredentials.store_id' => 'config_store_id',
            'sslcommerz.apiCredentials.store_password' => 'config_store_password',
        ]);

        $ssl = new SslCommerceNotification();
        
        // Set empty database credentials
        $ssl->setCredentials('', '');
        
        // Use reflection to access protected methods
        $reflection = new \ReflectionClass($ssl);
        $getStoreId = $reflection->getMethod('getStoreId');
        $getStoreId->setAccessible(true);
        $getStorePassword = $reflection->getMethod('getStorePassword');
        $getStorePassword->setAccessible(true);
        
        // Should fall back to config credentials when database credentials are empty
        $this->assertEquals('config_store_id', $getStoreId->invoke($ssl));
        $this->assertEquals('config_store_password', $getStorePassword->invoke($ssl));
    }

    /** @test */
    public function it_handles_missing_config_credentials()
    {
        // Don't set any config values (or set them to null)
        config([
            'sslcommerz.apiCredentials.store_id' => null,
            'sslcommerz.apiCredentials.store_password' => null,
        ]);

        $ssl = new SslCommerceNotification();
        
        // Use reflection to access protected methods
        $reflection = new \ReflectionClass($ssl);
        $getStoreId = $reflection->getMethod('getStoreId');
        $getStoreId->setAccessible(true);
        $getStorePassword = $reflection->getMethod('getStorePassword');
        $getStorePassword->setAccessible(true);
        
        // Should return empty string when no credentials are available
        $this->assertEquals('', $getStoreId->invoke($ssl));
        $this->assertEquals('', $getStorePassword->invoke($ssl));
    }

    /** @test */
    public function it_prioritizes_database_over_config_even_when_config_exists()
    {
        // Mock config values
        config([
            'sslcommerz.apiCredentials.store_id' => 'config_store_id',
            'sslcommerz.apiCredentials.store_password' => 'config_store_password',
        ]);

        $ssl = new SslCommerceNotification();
        
        // First set database credentials
        $ssl->setCredentials('db_store_id', 'db_store_password');
        
        // Use reflection to access protected methods
        $reflection = new \ReflectionClass($ssl);
        $getStoreId = $reflection->getMethod('getStoreId');
        $getStoreId->setAccessible(true);
        $getStorePassword = $reflection->getMethod('getStorePassword');
        $getStorePassword->setAccessible(true);
        
        // Should still use database credentials
        $this->assertEquals('db_store_id', $getStoreId->invoke($ssl));
        $this->assertEquals('db_store_password', $getStorePassword->invoke($ssl));
        
        // Now change database credentials
        $ssl->setCredentials('new_db_store_id', 'new_db_store_password');
        
        // Should use the new database credentials
        $this->assertEquals('new_db_store_id', $getStoreId->invoke($ssl));
        $this->assertEquals('new_db_store_password', $getStorePassword->invoke($ssl));
    }

    /** @test */
    public function it_sets_sandbox_api_domain_when_test_mode_is_yes()
    {
        // Mock config values
        config([
            'sslcommerz.apiCredentials.store_id' => 'config_store_id',
            'sslcommerz.apiCredentials.store_password' => 'config_store_password',
            'sslcommerz.apiDomain' => 'https://securepay.sslcommerz.com',
        ]);

        $ssl = new SslCommerceNotification();

        // Set credentials with test mode 'yes'
        $ssl->setCredentials('db_store_id', 'db_store_password', 'yes');

        // Use reflection to access private method
        $reflection = new \ReflectionClass($ssl);
        $getApiDomain = $reflection->getMethod('getApiDomain');
        $getApiDomain->setAccessible(true);

        // Should use sandbox domain
        $this->assertEquals('https://sandbox.sslcommerz.com', $getApiDomain->invoke($ssl));
    }

    /** @test */
    public function it_sets_production_api_domain_when_test_mode_is_no()
    {
        // Mock config values
        config([
            'sslcommerz.apiCredentials.store_id' => 'config_store_id',
            'sslcommerz.apiCredentials.store_password' => 'config_store_password',
            'sslcommerz.apiDomain' => 'https://sandbox.sslcommerz.com',
        ]);

        $ssl = new SslCommerceNotification();

        // Set credentials with test mode 'no'
        $ssl->setCredentials('db_store_id', 'db_store_password', 'no');

        // Use reflection to access private method
        $reflection = new \ReflectionClass($ssl);
        $getApiDomain = $reflection->getMethod('getApiDomain');
        $getApiDomain->setAccessible(true);

        // Should use production domain
        $this->assertEquals('https://securepay.sslcommerz.com', $getApiDomain->invoke($ssl));
    }

    /** @test */
    public function it_handles_boolean_test_mode_values()
    {
        $ssl = new SslCommerceNotification();

        // Use reflection to access private method
        $reflection = new \ReflectionClass($ssl);
        $getApiDomain = $reflection->getMethod('getApiDomain');
        $getApiDomain->setAccessible(true);

        // Test with boolean true
        $ssl->setCredentials('db_store_id', 'db_store_password', true);
        $this->assertEquals('https://sandbox.sslcommerz.com', $getApiDomain->invoke($ssl));

        // Test with boolean false
        $ssl->setCredentials('db_store_id', 'db_store_password', false);
        $this->assertEquals('https://securepay.sslcommerz.com', $getApiDomain->invoke($ssl));

        // Test with string '1'
        $ssl->setCredentials('db_store_id', 'db_store_password', '1');
        $this->assertEquals('https://sandbox.sslcommerz.com', $getApiDomain->invoke($ssl));
    }

    /** @test */
    public function it_falls_back_to_config_api_domain_when_test_mode_not_provided()
    {
        // Mock config values
        config([
            'sslcommerz.apiCredentials.store_id' => 'config_store_id',
            'sslcommerz.apiCredentials.store_password' => 'config_store_password',
            'sslcommerz.apiDomain' => 'https://sandbox.sslcommerz.com',
        ]);

        $ssl = new SslCommerceNotification();

        // Set credentials without test mode
        $ssl->setCredentials('db_store_id', 'db_store_password');

        // Use reflection to access private method
        $reflection = new \ReflectionClass($ssl);
        $getApiDomain = $reflection->getMethod('getApiDomain');
        $getApiDomain->setAccessible(true);

        // Should fall back to config domain
        $this->assertEquals('https://sandbox.sslcommerz.com', $getApiDomain->invoke($ssl));
    }

    /** @test */
    public function it_uses_production_domain_as_final_fallback()
    {
        // Don't set any config values
        config([
            'sslcommerz.apiCredentials.store_id' => null,
            'sslcommerz.apiCredentials.store_password' => null,
            'sslcommerz.apiDomain' => null,
        ]);

        $ssl = new SslCommerceNotification();

        // Don't set any credentials

        // Use reflection to access private method
        $reflection = new \ReflectionClass($ssl);
        $getApiDomain = $reflection->getMethod('getApiDomain');
        $getApiDomain->setAccessible(true);

        // Should use production domain as final fallback
        $this->assertEquals('https://securepay.sslcommerz.com', $getApiDomain->invoke($ssl));
    }

    /** @test */
    public function it_uses_correct_api_domain_in_make_payment_url()
    {
        // Mock config values
        config([
            'sslcommerz.apiCredentials.store_id' => 'test_store_id',
            'sslcommerz.apiCredentials.store_password' => 'test_store_password',
            'sslcommerz.apiUrl.make_payment' => '/gwprocess/v4/api.php',
        ]);

        $ssl = new SslCommerceNotification();

        // Set credentials with test mode 'yes' (sandbox)
        $ssl->setCredentials('db_store_id', 'db_store_password', 'yes');

        // Use reflection to access protected method
        $reflection = new \ReflectionClass($ssl);
        $setApiUrl = $reflection->getMethod('setApiUrl');
        $setApiUrl->setAccessible(true);
        $getApiUrl = $reflection->getMethod('getApiUrl');
        $getApiUrl->setAccessible(true);

        // Simulate what happens in makePayment method
        $getApiDomain = $reflection->getMethod('getApiDomain');
        $getApiDomain->setAccessible(true);
        $apiDomain = $getApiDomain->invoke($ssl);
        $config = $reflection->getProperty('config');
        $config->setAccessible(true);
        $configValue = $config->getValue($ssl);

        $expectedUrl = $apiDomain . $configValue['apiUrl']['make_payment'];
        $setApiUrl->invoke($ssl, $expectedUrl);

        // Verify the URL contains the sandbox domain
        $actualUrl = $getApiUrl->invoke($ssl);
        $this->assertStringContainsString('https://sandbox.sslcommerz.com', $actualUrl);
        $this->assertStringContainsString('/gwprocess/v4/api.php', $actualUrl);
    }
}
