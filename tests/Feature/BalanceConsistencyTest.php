<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Company;
use App\Models\Role;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class BalanceConsistencyTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create roles
        Role::create(['name' => 'super-admin', 'guard_name' => 'web']);
        Role::create(['name' => 'client', 'guard_name' => 'web']);
    }

    /** @test */
    public function user_balance_is_consistent_between_datatable_and_details_page()
    {
        // Create a company with a specific balance
        $company = Company::create([
            'name' => 'Test Company',
            'current_balance' => 1234.56,
            'balance_expired' => now()->addYear(),
            'minimum_recharge_amount' => 10,
            'api_key' => 'test-api-key',
        ]);

        // Create a user linked to this company
        $user = User::factory()->create([
            'company_id' => $company->id,
        ]);
        $user->assignRole('client');

        // Create a super admin to access the data
        $admin = User::factory()->create();
        $admin->assignRole('super-admin');

        // Test 1: Check that the user model returns the correct balance
        $this->assertEquals(1234.56, $user->company->current_balance);

        // Test 2: Simulate DataTable balance calculation
        $datatableBalance = $user->company ? $user->company->current_balance : 0;
        $this->assertEquals(1234.56, $datatableBalance);

        // Test 3: Check formatted balance (as used in DataTable)
        $formattedBalance = '$' . number_format($datatableBalance, 2);
        $this->assertEquals('$1,234.56', $formattedBalance);

        // Test 4: Check details page balance calculation
        $detailsPageBalance = $user->company->current_balance ?? 0;
        $this->assertEquals(1234.56, $detailsPageBalance);

        // Test 5: Check formatted balance (as used in details page)
        $detailsFormattedBalance = '$' . number_format($detailsPageBalance, 2);
        $this->assertEquals('$1,234.56', $detailsFormattedBalance);

        // Test 6: Verify both calculations return the same result
        $this->assertEquals($datatableBalance, $detailsPageBalance);
        $this->assertEquals($formattedBalance, $detailsFormattedBalance);
    }

    /** @test */
    public function user_without_company_shows_zero_balance()
    {
        // Create a user without a company
        $user = User::factory()->create([
            'company_id' => null,
        ]);
        $user->assignRole('client');

        // Test DataTable balance calculation
        $datatableBalance = $user->company ? $user->company->current_balance : 0;
        $this->assertEquals(0, $datatableBalance);

        // Test details page balance calculation
        $detailsPageBalance = $user->company->current_balance ?? 0;
        $this->assertEquals(0, $detailsPageBalance);

        // Verify both return the same result
        $this->assertEquals($datatableBalance, $detailsPageBalance);
    }

    /** @test */
    public function balance_formatting_is_consistent()
    {
        // Test various balance amounts
        $testBalances = [
            0 => '$0.00',
            1 => '$1.00',
            1234.56 => '$1,234.56',
            1000000 => '$1,000,000.00',
            0.01 => '$0.01',
            999.99 => '$999.99',
        ];

        foreach ($testBalances as $balance => $expectedFormat) {
            $company = Company::create([
                'name' => "Test Company {$balance}",
                'current_balance' => $balance,
                'balance_expired' => now()->addYear(),
                'minimum_recharge_amount' => 10,
                'api_key' => "test-api-key-{$balance}",
            ]);

            $user = User::factory()->create([
                'company_id' => $company->id,
            ]);
            $user->assignRole('client');

            // Test DataTable formatting
            $datatableFormatted = '$' . number_format($user->company->current_balance, 2);
            
            // Test details page formatting
            $detailsFormatted = '$' . number_format($user->company->current_balance ?? 0, 2);

            $this->assertEquals($expectedFormat, $datatableFormatted, "DataTable formatting failed for balance: {$balance}");
            $this->assertEquals($expectedFormat, $detailsFormatted, "Details page formatting failed for balance: {$balance}");
            $this->assertEquals($datatableFormatted, $detailsFormatted, "Formatting inconsistency for balance: {$balance}");
        }
    }

    /** @test */
    public function user_model_loads_company_relationship_by_default()
    {
        // Create a company
        $company = Company::create([
            'name' => 'Test Company',
            'current_balance' => 500.00,
            'balance_expired' => now()->addYear(),
            'minimum_recharge_amount' => 10,
            'api_key' => 'test-api-key',
        ]);

        // Create a user linked to this company
        $user = User::factory()->create([
            'company_id' => $company->id,
        ]);
        $user->assignRole('client');

        // Fetch user fresh from database
        $freshUser = User::find($user->id);

        // Check that company relationship is loaded
        $this->assertTrue($freshUser->relationLoaded('company'));
        
        // Check that we can access balance without additional queries
        $this->assertEquals(500.00, $freshUser->company->current_balance);
    }
}
