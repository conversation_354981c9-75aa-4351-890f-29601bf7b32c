<?php

namespace Tests\Feature;

use App\Models\Notice;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class NoticeControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create roles
        Role::create(['name' => 'super-admin']);
        Role::create(['name' => 'admin']);
        Role::create(['name' => 'user']);
    }

    public function test_admin_can_view_notices_index()
    {
        $admin = User::factory()->create();
        $admin->assignRole('admin');
        
        Notice::factory()->count(3)->create();

        $response = $this->actingAs($admin)->get(route('admin.notices.index'));

        $response->assertStatus(200);
        $response->assertViewIs('admin.notices.index');
        $response->assertViewHas('notices');
    }

    public function test_regular_user_cannot_access_admin_notices()
    {
        $user = User::factory()->create();
        $user->assignRole('user');

        $response = $this->actingAs($user)->get(route('admin.notices.index'));

        $response->assertStatus(403);
    }

    public function test_admin_can_create_notice()
    {
        $admin = User::factory()->create();
        $admin->assignRole('admin');

        $noticeData = [
            'title' => 'Test Notice',
            'description' => 'This is a test notice.',
            'is_active' => true,
        ];

        $response = $this->actingAs($admin)
            ->withSession(['_token' => 'test-token'])
            ->post(route('admin.notices.store'), array_merge($noticeData, ['_token' => 'test-token']));

        $response->assertRedirect(route('admin.notices.index'));
        $response->assertSessionHas('success');
        
        $this->assertDatabaseHas('notices', [
            'title' => 'Test Notice',
            'description' => 'This is a test notice.',
            'created_by' => $admin->id,
            'is_active' => true,
        ]);
    }

    public function test_admin_can_update_notice()
    {
        $admin = User::factory()->create();
        $admin->assignRole('admin');
        
        $notice = Notice::factory()->create(['created_by' => $admin->id]);

        $updateData = [
            'title' => 'Updated Notice',
            'description' => 'Updated description.',
            // is_active is not included, so it should be false (unchecked checkbox)
        ];

        $response = $this->actingAs($admin)
            ->withSession(['_token' => 'test-token'])
            ->put(route('admin.notices.update', $notice), array_merge($updateData, ['_token' => 'test-token']));

        $response->assertRedirect(route('admin.notices.index'));
        $response->assertSessionHas('success');
        
        $notice->refresh();
        $this->assertEquals('Updated Notice', $notice->title);
        $this->assertEquals('Updated description.', $notice->description);
        $this->assertFalse($notice->is_active);
    }

    public function test_admin_can_delete_notice()
    {
        $admin = User::factory()->create();
        $admin->assignRole('admin');
        
        $notice = Notice::factory()->create(['created_by' => $admin->id]);

        $response = $this->actingAs($admin)
            ->withSession(['_token' => 'test-token'])
            ->delete(route('admin.notices.destroy', $notice), ['_token' => 'test-token']);

        $response->assertRedirect(route('admin.notices.index'));
        $response->assertSessionHas('success');
        
        $this->assertDatabaseMissing('notices', ['id' => $notice->id]);
    }

    public function test_all_users_can_view_public_notices()
    {
        $user = User::factory()->create();
        $user->assignRole('user');
        
        Notice::factory()->count(2)->active()->create();
        Notice::factory()->inactive()->create();

        $response = $this->actingAs($user)->get(route('notices.index'));

        $response->assertStatus(200);
        $response->assertViewIs('notices.index');
        $response->assertViewHas('notices');
        
        $notices = $response->viewData('notices');
        $this->assertCount(2, $notices); // Only active notices should be shown
    }

    public function test_notice_validation_requires_title_and_description()
    {
        $admin = User::factory()->create();
        $admin->assignRole('admin');

        $response = $this->actingAs($admin)
            ->withSession(['_token' => 'test-token'])
            ->post(route('admin.notices.store'), ['_token' => 'test-token']);

        $response->assertSessionHasErrors(['title', 'description']);
    }

    public function test_notice_title_has_max_length_validation()
    {
        $admin = User::factory()->create();
        $admin->assignRole('admin');

        $longTitle = str_repeat('a', 256); // Exceeds 255 character limit

        $response = $this->actingAs($admin)
            ->withSession(['_token' => 'test-token'])
            ->post(route('admin.notices.store'), [
                'title' => $longTitle,
                'description' => 'Valid description',
                '_token' => 'test-token'
            ]);

        $response->assertSessionHasErrors(['title']);
    }
}