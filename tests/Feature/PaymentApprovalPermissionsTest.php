<?php

namespace Tests\Feature;

use App\Helpers\Traits\HierarchicalPermissions;
use App\Models\Company;
use App\Models\Payment;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PaymentApprovalPermissionsTest extends TestCase
{
    use RefreshDatabase, HierarchicalPermissions;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create roles
        $this->artisan('db:seed', ['--class' => 'RoleSeeder']);
    }

    /** @test */
    public function super_admin_can_approve_all_payments()
    {
        // Create hierarchy
        $company = Company::factory()->create();
        
        $superAdmin = User::factory()->create();
        $superAdmin->assignRole('super-admin');
        
        $client = User::factory()->create(['company_id' => $company->id]);
        $client->assignRole('client');
        
        // Create payment for client
        $payment = Payment::factory()->create([
            'user_id' => $client->id,
            'company_id' => $company->id,
            'payment_status' => 'pending'
        ]);
        
        // Load relationships
        $payment->load('user.parent');
        
        // Super admin should be able to approve any payment
        $this->assertTrue($this->canApprovePayment($superAdmin, $payment));
    }

    /** @test */
    public function master_reseller_can_approve_payments_in_hierarchy()
    {
        // Create hierarchy
        $company = Company::factory()->create();
        
        $masterReseller = User::factory()->create(['company_id' => $company->id]);
        $masterReseller->assignRole('master-reseller');
        
        $reseller = User::factory()->create([
            'company_id' => $company->id,
            'parent_id' => $masterReseller->id
        ]);
        $reseller->assignRole('reseller');
        
        $client = User::factory()->create([
            'company_id' => $company->id,
            'parent_id' => $reseller->id
        ]);
        $client->assignRole('client');
        
        // Create payment for client
        $payment = Payment::factory()->create([
            'user_id' => $client->id,
            'company_id' => $company->id,
            'payment_status' => 'pending'
        ]);
        
        // Load relationships
        $payment->load('user.parent');
        
        // Master reseller should be able to approve payment for user in their hierarchy
        $this->assertTrue($this->canApprovePayment($masterReseller, $payment));
    }

    /** @test */
    public function reseller_can_approve_direct_client_payments_only()
    {
        // Create hierarchy
        $company = Company::factory()->create();
        
        $reseller = User::factory()->create(['company_id' => $company->id]);
        $reseller->assignRole('reseller');
        
        $directClient = User::factory()->create([
            'company_id' => $company->id,
            'parent_id' => $reseller->id
        ]);
        $directClient->assignRole('client');
        
        $otherClient = User::factory()->create(['company_id' => $company->id]);
        $otherClient->assignRole('client');
        
        // Create payments
        $directClientPayment = Payment::factory()->create([
            'user_id' => $directClient->id,
            'company_id' => $company->id,
            'payment_status' => 'pending'
        ]);
        
        $otherClientPayment = Payment::factory()->create([
            'user_id' => $otherClient->id,
            'company_id' => $company->id,
            'payment_status' => 'pending'
        ]);
        
        // Load relationships
        $directClientPayment->load('user.parent');
        $otherClientPayment->load('user.parent');
        
        // Reseller should approve direct client payment
        $this->assertTrue($this->canApprovePayment($reseller, $directClientPayment));
        
        // Reseller should NOT approve other client payment
        $this->assertFalse($this->canApprovePayment($reseller, $otherClientPayment));
    }

    /** @test */
    public function client_cannot_approve_any_payments()
    {
        // Create hierarchy
        $company = Company::factory()->create();
        
        $client = User::factory()->create(['company_id' => $company->id]);
        $client->assignRole('client');
        
        $otherClient = User::factory()->create(['company_id' => $company->id]);
        $otherClient->assignRole('client');
        
        // Create payment
        $payment = Payment::factory()->create([
            'user_id' => $otherClient->id,
            'company_id' => $company->id,
            'payment_status' => 'pending'
        ]);
        
        // Load relationships
        $payment->load('user.parent');
        
        // Client should NOT be able to approve any payment
        $this->assertFalse($this->canApprovePayment($client, $payment));
    }

    /** @test */
    public function permission_check_handles_null_payment_user_gracefully()
    {
        $superAdmin = User::factory()->create();
        $superAdmin->assignRole('super-admin');
        
        // Create payment without user
        $payment = Payment::factory()->create([
            'user_id' => null,
            'payment_status' => 'pending'
        ]);
        
        // Should return false for payment without user
        $this->assertFalse($this->canApprovePayment($superAdmin, $payment));
    }

    /** @test */
    public function approval_button_visibility_in_recharge_controller()
    {
        // Create test data
        $company = Company::factory()->create();
        
        $superAdmin = User::factory()->create();
        $superAdmin->assignRole('super-admin');
        
        $client = User::factory()->create(['company_id' => $company->id]);
        $client->assignRole('client');
        
        $payment = Payment::factory()->create([
            'user_id' => $client->id,
            'company_id' => $company->id,
            'payment_status' => 'pending',
            'gateway' => 'manual'
        ]);
        
        // Test as super admin
        $response = $this->actingAs($superAdmin)
            ->get(route('datatables.account-recharge.list'));

        $response->assertStatus(200);

        // The response should contain the approval button for super admin
        $responseData = $response->json();
        $this->assertNotEmpty($responseData['data']);

        // Check that the action column contains the approval button
        $actionColumn = $responseData['data'][0]['action'] ?? '';
        $this->assertStringContainsString('Approved', $actionColumn);
    }
}
