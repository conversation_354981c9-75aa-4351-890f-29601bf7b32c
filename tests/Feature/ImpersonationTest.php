<?php

namespace Tests\Feature;

use App\Models\ImpersonationLog;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Session;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class ImpersonationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create roles
        Role::create(['name' => 'super-admin']);
        Role::create(['name' => 'admin']);
        Role::create(['name' => 'user']);
    }

    public function test_super_admin_can_start_impersonation()
    {
        $superAdmin = User::factory()->create();
        $superAdmin->assignRole('super-admin');
        
        $targetUser = User::factory()->create();
        $targetUser->assignRole('user');

        $response = $this->actingAs($superAdmin)
            ->withSession(['_token' => 'test-token'])
            ->post(route('impersonate.start', $targetUser), ['_token' => 'test-token']);

        $response->assertRedirect(route('home'));
        $response->assertSessionHas('message');
        
        // Check session data
        $this->assertTrue(Session::get('impersonating'));
        $this->assertEquals($superAdmin->id, Session::get('impersonator_id'));
        
        // Check impersonation chain
        $chain = Session::get('impersonation_chain');
        $this->assertIsArray($chain);
        $this->assertCount(1, $chain);
        $this->assertEquals($superAdmin->id, $chain[0]['user_id']);
        $this->assertEquals(1, $chain[0]['level']);
        
        // Check logging
        $this->assertDatabaseHas('impersonation_logs', [
            'impersonator_id' => $superAdmin->id,
            'impersonated_id' => $targetUser->id,
            'action' => 'start',
            'level' => 1,
        ]);
    }

    public function test_non_super_admin_cannot_start_impersonation()
    {
        $admin = User::factory()->create();
        $admin->assignRole('admin');
        
        $targetUser = User::factory()->create();
        $targetUser->assignRole('user');

        $response = $this->actingAs($admin)
            ->withSession(['_token' => 'test-token'])
            ->post(route('impersonate.start', $targetUser), ['_token' => 'test-token']);

        $response->assertStatus(403);
    }

    public function test_cannot_impersonate_self()
    {
        $superAdmin = User::factory()->create();
        $superAdmin->assignRole('super-admin');

        $response = $this->actingAs($superAdmin)
            ->withSession(['_token' => 'test-token'])
            ->post(route('impersonate.start', $superAdmin), ['_token' => 'test-token']);

        $response->assertRedirect();
        $response->assertSessionHas('status', 'error');
        $response->assertSessionHas('message');
    }

    public function test_cannot_impersonate_other_super_admin()
    {
        $superAdmin1 = User::factory()->create();
        $superAdmin1->assignRole('super-admin');
        
        $superAdmin2 = User::factory()->create();
        $superAdmin2->assignRole('super-admin');

        $response = $this->actingAs($superAdmin1)
            ->withSession(['_token' => 'test-token'])
            ->post(route('impersonate.start', $superAdmin2), ['_token' => 'test-token']);

        $response->assertRedirect();
        $response->assertSessionHas('status', 'error');
        $response->assertSessionHas('message');
    }

    public function test_can_stop_impersonation()
    {
        $superAdmin = User::factory()->create();
        $superAdmin->assignRole('super-admin');
        
        $targetUser = User::factory()->create();
        $targetUser->assignRole('user');

        // Start impersonation
        Session::put('impersonating', true);
        Session::put('impersonator_id', $superAdmin->id);
        Session::put('impersonation_chain', [
            ['user_id' => $superAdmin->id, 'level' => 1, 'started_at' => now()]
        ]);

        $response = $this->actingAs($targetUser)
            ->withSession(['_token' => 'test-token'])
            ->post(route('impersonate.stop'), ['_token' => 'test-token']);

        $response->assertRedirect(route('users.index'));
        $response->assertSessionHas('message');
        
        // Check session cleanup
        $this->assertFalse(Session::get('impersonating', false));
        $this->assertNull(Session::get('impersonator_id'));
        $this->assertEmpty(Session::get('impersonation_chain', []));
        
        // Check logging
        $this->assertDatabaseHas('impersonation_logs', [
            'impersonator_id' => $superAdmin->id,
            'impersonated_id' => $targetUser->id,
            'action' => 'stop',
            'level' => 1,
        ]);
    }

    public function test_multi_level_impersonation_chain()
    {
        $superAdmin = User::factory()->create();
        $superAdmin->assignRole('super-admin');
        
        $admin = User::factory()->create();
        $admin->assignRole('admin');
        
        $user = User::factory()->create();
        $user->assignRole('user');

        // Start first level impersonation (super-admin -> admin)
        $this->actingAs($superAdmin)
            ->withSession(['_token' => 'test-token'])
            ->post(route('impersonate.start', $admin), ['_token' => 'test-token']);

        $chain = Session::get('impersonation_chain');
        $this->assertIsArray($chain);
        $this->assertCount(1, $chain);
        $this->assertEquals(1, $chain[0]['level']);

        // Start second level impersonation (admin -> user)
        $this->actingAs($admin)
            ->withSession(['_token' => 'test-token'])
            ->post(route('impersonate.start', $user), ['_token' => 'test-token']);

        $chain = Session::get('impersonation_chain');
        $this->assertIsArray($chain);
        $this->assertCount(2, $chain);
        $this->assertEquals(1, $chain[0]['level']);
        $this->assertEquals(2, $chain[1]['level']);
        
        // Check logging for second level
        $this->assertDatabaseHas('impersonation_logs', [
            'impersonator_id' => $admin->id,
            'impersonated_id' => $user->id,
            'action' => 'start',
            'level' => 2,
        ]);
    }

    public function test_stop_multi_level_impersonation_returns_to_previous_level()
    {
        $superAdmin = User::factory()->create();
        $superAdmin->assignRole('super-admin');
        
        $admin = User::factory()->create();
        $admin->assignRole('admin');
        
        $user = User::factory()->create();
        $user->assignRole('user');

        // Set up multi-level impersonation chain
        Session::put('impersonating', true);
        Session::put('impersonator_id', $admin->id);
        Session::put('impersonation_chain', [
            ['user_id' => $superAdmin->id, 'level' => 1, 'started_at' => now()],
            ['user_id' => $admin->id, 'level' => 2, 'started_at' => now()]
        ]);

        $response = $this->actingAs($user)
            ->withSession(['_token' => 'test-token'])
            ->post(route('impersonate.stop'), ['_token' => 'test-token']);

        $response->assertRedirect(route('home'));
        $response->assertSessionHas('message');
        
        // Should return to previous level (admin)
        $chain = Session::get('impersonation_chain');
        $this->assertIsArray($chain);
        $this->assertCount(1, $chain);
        $this->assertEquals($superAdmin->id, $chain[0]['user_id']);
        $this->assertEquals($admin->id, Session::get('impersonator_id'));
    }

    public function test_impersonation_log_model_methods()
    {
        $superAdmin = User::factory()->create();
        $superAdmin->assignRole('super-admin');
        
        $user = User::factory()->create();
        $user->assignRole('user');

        $startedAt = now();
        $chainData = [
            ['user_id' => $superAdmin->id, 'level' => 1, 'started_at' => $startedAt->toISOString()]
        ];

        // Test logStart method
        $log = ImpersonationLog::logStart(
            $superAdmin, 
            $user, 
            '127.0.0.1', 
            'Test User Agent', 
            1, 
            $chainData
        );

        $this->assertInstanceOf(ImpersonationLog::class, $log);
        $this->assertEquals('start', $log->action);
        $this->assertEquals(1, $log->level);
        $this->assertEquals($chainData, $log->chain_data);
        $this->assertEquals('127.0.0.1', $log->ip_address);
        $this->assertEquals('Test User Agent', $log->user_agent);

        // Test logStop method
        $stopLog = ImpersonationLog::logStop(
            $superAdmin, 
            $user, 
            '127.0.0.1', 
            'Test User Agent', 
            1, 
            $chainData
        );

        $this->assertEquals('stop', $stopLog->action);
    }

    public function test_impersonation_log_scopes()
    {
        $superAdmin = User::factory()->create();
        $superAdmin->assignRole('super-admin');
        
        $user = User::factory()->create();
        $user->assignRole('user');

        // Create logs with different levels
        ImpersonationLog::logStart($superAdmin, $user, '127.0.0.1', 'Test', 1, []);
        ImpersonationLog::logStart($superAdmin, $user, '127.0.0.1', 'Test', 2, []);
        ImpersonationLog::logStart($superAdmin, $user, '127.0.0.1', 'Test', 3, []);

        // Test byLevel scope
        $level1Logs = ImpersonationLog::byLevel(1)->get();
        $this->assertCount(1, $level1Logs);

        // Test multiLevel scope
        $multiLevelLogs = ImpersonationLog::multiLevel()->get();
        $this->assertCount(2, $multiLevelLogs); // Levels 2 and 3
    }

    public function test_formatted_chain_attribute()
    {
        $superAdmin = User::factory()->create();
        $superAdmin->assignRole('super-admin');
        
        $user = User::factory()->create();
        $user->assignRole('user');

        $chainData = [
            ['user_id' => $superAdmin->id, 'level' => 1, 'started_at' => now()],
            ['user_id' => $user->id, 'level' => 2, 'started_at' => now()]
        ];

        $log = ImpersonationLog::logStart($superAdmin, $user, '127.0.0.1', 'Test', 2, $chainData);

        $formattedChain = $log->getFormattedChainAttribute();
        $this->assertEquals('L1 → L2', $formattedChain);
    }
}