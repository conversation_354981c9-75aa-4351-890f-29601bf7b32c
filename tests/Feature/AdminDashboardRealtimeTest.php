<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\SupportTicket;
use App\Models\Payment;
use App\Models\Sender;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;

class AdminDashboardRealtimeTest extends TestCase
{
    use RefreshDatabase;

    protected $admin;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create admin user
        $this->admin = User::factory()->create();
        $this->admin->assignRole('super-admin');
    }

    /** @test */
    public function admin_can_access_dashboard()
    {
        $response = $this->actingAs($this->admin)
            ->get('/admin/dashboard');

        $response->assertStatus(200);
        $response->assertViewIs('admin.dashboard.index');
    }

    /** @test */
    public function admin_can_fetch_alerts_api()
    {
        // Create test data
        SupportTicket::factory()->create([
            'priority' => 'critical',
            'status' => 'open'
        ]);

        $response = $this->actingAs($this->admin)
            ->getJson('/admin/dashboard/alerts');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                '*' => [
                    'type',
                    'title',
                    'message',
                    'count',
                    'priority'
                ]
            ],
            'timestamp',
            'count'
        ]);
    }

    /** @test */
    public function admin_can_fetch_metrics_api()
    {
        $response = $this->actingAs($this->admin)
            ->getJson('/admin/dashboard/metrics');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'critical_tickets',
                'high_priority_tickets',
                'open_tickets',
                'pending_payments',
                'pending_payment_amount',
                'pending_senders',
                'failed_domains',
                'pending_domains',
                'new_users_today',
                'unverified_users',
                'last_updated'
            ],
            'timestamp'
        ]);
    }

    /** @test */
    public function admin_can_fetch_action_queue_api()
    {
        // Create test data
        SupportTicket::factory()->create([
            'priority' => 'critical',
            'status' => 'open'
        ]);

        $response = $this->actingAs($this->admin)
            ->getJson('/admin/dashboard/action-queue');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data',
            'timestamp',
            'count'
        ]);
    }

    /** @test */
    public function admin_can_fetch_summary_cards_api()
    {
        $response = $this->actingAs($this->admin)
            ->getJson('/admin/dashboard/summary-cards');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                '*' => [
                    'title',
                    'value',
                    'subtitle',
                    'icon',
                    'color',
                    'action_url'
                ]
            ],
            'timestamp'
        ]);
    }

    /** @test */
    public function admin_can_fetch_aggregated_metrics_api()
    {
        $response = $this->actingAs($this->admin)
            ->getJson('/admin/dashboard/aggregated-metrics');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'sms_last_week',
                'cost_last_week',
                'sms_in_last_month',
                'cost_in_last_month',
                'total_balance',
                'total_balance_msgs',
                'last_month_name'
            ],
            'timestamp'
        ]);
    }

    /** @test */
    public function admin_can_fetch_comprehensive_dashboard_data()
    {
        $response = $this->actingAs($this->admin)
            ->getJson('/admin/dashboard/data');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'summary_cards',
                'action_queue',
                'alerts',
                'metrics',
                'aggregated_metrics',
                'quick_actions'
            ],
            'timestamp'
        ]);
    }

    /** @test */
    public function dashboard_data_is_cached()
    {
        // Clear cache
        Cache::forget('admin_dashboard_core_metrics');
        
        // First request should cache the data
        $this->actingAs($this->admin)
            ->getJson('/admin/dashboard/metrics');

        // Verify cache exists
        $this->assertTrue(Cache::has('admin_dashboard_core_metrics'));
    }

    /** @test */
    public function api_handles_errors_gracefully()
    {
        // Mock database error by using invalid connection
        config(['database.default' => 'invalid']);

        $response = $this->actingAs($this->admin)
            ->getJson('/admin/dashboard/metrics');

        // Should return error response but not crash
        $response->assertStatus(500);
        $response->assertJsonStructure([
            'success',
            'message',
            'data',
            'timestamp'
        ]);
    }

    /** @test */
    public function non_admin_cannot_access_dashboard_apis()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)
            ->getJson('/admin/dashboard/alerts');

        $response->assertStatus(403);
    }

    /** @test */
    public function unauthenticated_user_cannot_access_dashboard_apis()
    {
        $response = $this->getJson('/admin/dashboard/alerts');

        $response->assertStatus(401);
    }
}
