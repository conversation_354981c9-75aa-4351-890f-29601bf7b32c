<?php

namespace Tests\Feature;

use App\Models\Company;
use App\Models\Payment;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

class PaymentApprovalSecurityTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create roles
        $this->artisan('db:seed', ['--class' => 'RoleSeeder']);
    }

    /** @test */
    public function unauthorized_user_cannot_approve_payment_via_direct_url()
    {
        // Create hierarchy
        $company = Company::factory()->create();
        
        $masterReseller = User::factory()->create(['company_id' => $company->id]);
        $masterReseller->assignRole('master-reseller');
        
        $unauthorizedUser = User::factory()->create(['company_id' => $company->id]);
        $unauthorizedUser->assignRole('client');
        
        $targetClient = User::factory()->create([
            'company_id' => $company->id,
            'parent_id' => $masterReseller->id
        ]);
        $targetClient->assignRole('client');
        
        // Create payment for target client
        $payment = Payment::factory()->create([
            'user_id' => $targetClient->id,
            'company_id' => $company->id,
            'payment_status' => 'pending',
            'gateway' => 'manual',
            'transaction_id' => 'TEST123456'
        ]);
        
        // Attempt to approve payment as unauthorized user via direct URL
        $response = $this->actingAs($unauthorizedUser)
            ->get(route('payment.success', [
                'trans_id' => $payment->transaction_id,
                'payment_method' => 'manual',
                'route' => 'recharges'
            ]));
        
        // Should be redirected with error
        $response->assertRedirect();
        $response->assertSessionHas('error');
        
        // Payment should still be pending
        $payment->refresh();
        $this->assertEquals('pending', $payment->payment_status);
    }

    /** @test */
    public function reseller_cannot_approve_payment_outside_their_hierarchy()
    {
        // Create hierarchy
        $company = Company::factory()->create();
        
        $reseller1 = User::factory()->create(['company_id' => $company->id]);
        $reseller1->assignRole('reseller');
        
        $reseller2 = User::factory()->create(['company_id' => $company->id]);
        $reseller2->assignRole('reseller');
        
        $client = User::factory()->create([
            'company_id' => $company->id,
            'parent_id' => $reseller2->id
        ]);
        $client->assignRole('client');
        
        // Create payment for reseller2's client
        $payment = Payment::factory()->create([
            'user_id' => $client->id,
            'company_id' => $company->id,
            'payment_status' => 'pending',
            'gateway' => 'manual',
            'transaction_id' => 'TEST789012'
        ]);
        
        // Attempt to approve as reseller1 (unauthorized)
        $response = $this->actingAs($reseller1)
            ->get(route('payment.success', [
                'trans_id' => $payment->transaction_id,
                'payment_method' => 'manual',
                'route' => 'recharges'
            ]));
        
        // Should be redirected with error
        $response->assertRedirect();
        $response->assertSessionHas('error');
        
        // Payment should still be pending
        $payment->refresh();
        $this->assertEquals('pending', $payment->payment_status);
    }

    /** @test */
    public function cannot_approve_already_completed_payment()
    {
        // Create test data
        $company = Company::factory()->create();
        
        $superAdmin = User::factory()->create();
        $superAdmin->assignRole('super-admin');
        
        $client = User::factory()->create(['company_id' => $company->id]);
        $client->assignRole('client');
        
        // Create already completed payment
        $payment = Payment::factory()->create([
            'user_id' => $client->id,
            'company_id' => $company->id,
            'payment_status' => 'completed',
            'gateway' => 'manual',
            'transaction_id' => 'COMPLETED123'
        ]);
        
        // Attempt to approve already completed payment
        $response = $this->actingAs($superAdmin)
            ->get(route('payment.success', [
                'trans_id' => $payment->transaction_id,
                'payment_method' => 'manual',
                'route' => 'recharges'
            ]));
        
        // Should be redirected with error
        $response->assertRedirect();
        $response->assertSessionHas('error');
    }

    /** @test */
    public function cannot_approve_with_invalid_transaction_id()
    {
        $superAdmin = User::factory()->create();
        $superAdmin->assignRole('super-admin');
        
        // Attempt to approve with non-existent transaction ID
        $response = $this->actingAs($superAdmin)
            ->get(route('payment.success', [
                'trans_id' => 'INVALID123',
                'payment_method' => 'manual',
                'route' => 'recharges'
            ]));
        
        // Should be redirected with error
        $response->assertRedirect();
        $response->assertSessionHas('error');
    }

    /** @test */
    public function unauthenticated_user_cannot_access_approval_endpoint()
    {
        // Attempt to access without authentication
        $response = $this->get(route('payment.success', [
            'trans_id' => 'TEST123',
            'payment_method' => 'manual',
            'route' => 'recharges'
        ]));
        
        // Should be redirected to login
        $response->assertRedirect(route('login'));
    }

    /** @test */
    public function security_events_are_logged_for_unauthorized_attempts()
    {
        Log::fake();
        
        // Create test data
        $company = Company::factory()->create();
        
        $unauthorizedUser = User::factory()->create(['company_id' => $company->id]);
        $unauthorizedUser->assignRole('client');
        
        $targetClient = User::factory()->create(['company_id' => $company->id]);
        $targetClient->assignRole('client');
        
        $payment = Payment::factory()->create([
            'user_id' => $targetClient->id,
            'company_id' => $company->id,
            'payment_status' => 'pending',
            'gateway' => 'manual',
            'transaction_id' => 'SECURITY123'
        ]);
        
        // Attempt unauthorized approval
        $this->actingAs($unauthorizedUser)
            ->get(route('payment.success', [
                'trans_id' => $payment->transaction_id,
                'payment_method' => 'manual',
                'route' => 'recharges'
            ]));
        
        // Verify security event was logged
        Log::assertLogged('warning', function ($message, $context) {
            return $context['event'] === 'payment_approval_permission_denied';
        });
    }

    /** @test */
    public function successful_approval_is_logged_for_audit()
    {
        Log::fake();
        
        // Create test data
        $company = Company::factory()->create();
        
        $superAdmin = User::factory()->create();
        $superAdmin->assignRole('super-admin');
        
        $client = User::factory()->create(['company_id' => $company->id]);
        $client->assignRole('client');
        
        $payment = Payment::factory()->create([
            'user_id' => $client->id,
            'company_id' => $company->id,
            'payment_status' => 'pending',
            'gateway' => 'manual',
            'transaction_id' => 'AUDIT123'
        ]);
        
        // Perform authorized approval
        $this->actingAs($superAdmin)
            ->get(route('payment.success', [
                'trans_id' => $payment->transaction_id,
                'payment_method' => 'manual',
                'route' => 'recharges'
            ]));
        
        // Verify approval was logged
        Log::assertLogged('info', function ($message, $context) {
            return $context['action'] === 'approved';
        });
    }

    /** @test */
    public function rate_limiting_prevents_excessive_approval_attempts()
    {
        // Create test data
        $company = Company::factory()->create();
        
        $superAdmin = User::factory()->create();
        $superAdmin->assignRole('super-admin');
        
        // Create multiple pending payments
        $payments = Payment::factory()->count(15)->create([
            'company_id' => $company->id,
            'payment_status' => 'pending',
            'gateway' => 'manual'
        ]);
        
        $successfulRequests = 0;
        $rateLimitedRequests = 0;
        
        // Attempt to approve many payments quickly
        foreach ($payments as $payment) {
            $response = $this->actingAs($superAdmin)
                ->get(route('payment.success', [
                    'trans_id' => $payment->transaction_id,
                    'payment_method' => 'manual',
                    'route' => 'recharges'
                ]));
            
            if ($response->status() === 429) {
                $rateLimitedRequests++;
            } else {
                $successfulRequests++;
            }
        }
        
        // Should have some rate limited requests
        $this->assertGreaterThan(0, $rateLimitedRequests);
        $this->assertLessThanOrEqual(10, $successfulRequests); // Rate limit is 10 per minute
    }
}
