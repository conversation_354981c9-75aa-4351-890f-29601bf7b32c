<?php

namespace Tests\Feature;

use App\Models\Setting;
use App\Models\User;
use App\Services\SettingsService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class SettingsTest extends TestCase
{
    use RefreshDatabase;

    protected $user;

    protected function setUp(): void
    {
        parent::setUp();

        // Disable CSRF for testing
        $this->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class);

        // Seed roles first
        $this->seed(\Database\Seeders\RoleSeeder::class);

        // Create a super admin user for testing
        $this->user = User::factory()->create();
        $this->user->assignRole('super-admin');
    }

    /** @test */
    public function it_can_access_settings_page()
    {
        $response = $this->actingAs($this->user)
            ->get(route('admin.settings.index'));

        $response->assertStatus(200);
        $response->assertViewIs('admin.settings.index');
    }

    /** @test */
    public function it_can_update_text_settings()
    {
        $data = [
            'website_title' => 'Test SMS Application',
            'meta_description' => 'Test description for SMS app',
            'meta_keywords' => 'test, sms, application',
            'og_title' => 'Test OG Title',
            'og_description' => 'Test OG Description',
            'google_analytics_id' => 'G-XXXXXXXXXX',
            'robots_txt' => "User-agent: *\nDisallow: /admin/"
        ];

        $response = $this->actingAs($this->user)
            ->post(route('admin.settings.update'), $data);

        $response->assertRedirect(route('admin.settings.index'));
        $response->assertSessionHas('success');

        // Verify settings were saved
        $this->assertEquals('Test SMS Application', Setting::getValue('website_title'));
        $this->assertEquals('Test description for SMS app', Setting::getValue('meta_description'));
        $this->assertEquals('test, sms, application', Setting::getValue('meta_keywords'));
    }

    /** @test */
    public function it_can_upload_logo_file()
    {
        Storage::fake('public');

        $file = UploadedFile::fake()->image('logo.png', 200, 200);

        $response = $this->actingAs($this->user)
            ->post(route('admin.settings.update'), [
                'website_logo' => $file
            ]);

        $response->assertRedirect(route('admin.settings.index'));
        $response->assertSessionHas('success');

        // Verify file was stored
        $logoPath = Setting::getValue('website_logo');
        $this->assertNotNull($logoPath);
        Storage::disk('public')->assertExists($logoPath);
    }

    /** @test */
    public function it_validates_file_uploads()
    {
        Storage::fake('public');

        // Test invalid file type
        $file = UploadedFile::fake()->create('document.pdf', 1000);

        $response = $this->actingAs($this->user)
            ->post(route('admin.settings.update'), [
                'website_logo' => $file
            ]);

        $response->assertSessionHasErrors('website_logo');
    }

    /** @test */
    public function it_validates_character_limits()
    {
        $data = [
            'meta_description' => str_repeat('a', 161), // Exceeds 160 character limit
            'og_title' => str_repeat('b', 61), // Exceeds 60 character limit
        ];

        $response = $this->actingAs($this->user)
            ->post(route('admin.settings.update'), $data);

        $response->assertSessionHasErrors(['meta_description', 'og_title']);
    }

    /** @test */
    public function it_can_retrieve_settings_via_service()
    {
        Setting::setValue('website_title', 'Service Test Title');
        Setting::setValue('meta_description', 'Service Test Description');

        $this->assertEquals('Service Test Title', SettingsService::getWebsiteTitle());
        $this->assertEquals('Service Test Description', SettingsService::getMetaDescription());
    }

    /** @test */
    public function it_returns_default_values_when_settings_not_found()
    {
        $this->assertEquals(config('app.name', 'SMS Application'), SettingsService::getWebsiteTitle());
        $this->assertEquals('Professional SMS messaging platform for businesses', SettingsService::getMetaDescription());
    }

    /** @test */
    public function it_can_delete_file_settings()
    {
        Storage::fake('public');

        // First upload a file
        $file = UploadedFile::fake()->image('logo.png', 200, 200);
        Setting::setValue('website_logo', 'settings/logos/test-logo.png', 'file');
        Storage::disk('public')->put('settings/logos/test-logo.png', 'fake content');

        $response = $this->actingAs($this->user)
            ->delete(route('admin.settings.delete-file'), [
                'key' => 'website_logo'
            ]);

        $response->assertJson(['success' => true]);
        
        // Verify setting was deleted
        $this->assertNull(Setting::getValue('website_logo'));
    }

    /** @test */
    public function unauthorized_users_cannot_access_settings()
    {
        $regularUser = User::factory()->create();
        $regularUser->assignRole('client');

        $response = $this->actingAs($regularUser)
            ->get(route('admin.settings.index'));

        $response->assertStatus(403);
    }

    /** @test */
    public function it_can_update_popup_settings()
    {
        $data = [
            'popup_settings' => '1',
            'popup_title' => 'Important Notification',
            'popup_description' => 'This is a test popup notification for users.',
            'popup_status' => '1'
        ];

        $response = $this->actingAs($this->user)
            ->post(route('admin.settings.update'), $data);

        $response->assertRedirect(route('admin.settings.index'));
        $response->assertSessionHas('success', 'Pop-up settings updated successfully!');

        // Verify settings were saved
        $this->assertEquals('Important Notification', Setting::getValue('popup_title'));
        $this->assertEquals('This is a test popup notification for users.', Setting::getValue('popup_description'));
        $this->assertEquals(1, Setting::getValue('popup_status'));
    }

    /** @test */
    public function it_validates_popup_settings_required_fields()
    {
        $data = [
            'popup_settings' => '1',
            'popup_title' => '', // Required field empty
            'popup_description' => '', // Required field empty
            'popup_status' => '1'
        ];

        $response = $this->actingAs($this->user)
            ->post(route('admin.settings.update'), $data);

        $response->assertSessionHasErrors(['popup_title', 'popup_description']);
    }

    /** @test */
    public function it_validates_popup_title_character_limit()
    {
        $data = [
            'popup_settings' => '1',
            'popup_title' => str_repeat('a', 256), // Exceeds 255 character limit
            'popup_description' => 'Valid description',
            'popup_status' => '1'
        ];

        $response = $this->actingAs($this->user)
            ->post(route('admin.settings.update'), $data);

        $response->assertSessionHasErrors('popup_title');
    }

    /** @test */
    public function it_validates_popup_description_character_limit()
    {
        $data = [
            'popup_settings' => '1',
            'popup_title' => 'Valid title',
            'popup_description' => str_repeat('a', 10001), // Exceeds 10000 character limit
            'popup_status' => '1'
        ];

        $response = $this->actingAs($this->user)
            ->post(route('admin.settings.update'), $data);

        $response->assertSessionHasErrors('popup_description');
    }

    /** @test */
    public function it_can_upload_popup_file()
    {
        Storage::fake('public');

        $file = UploadedFile::fake()->create('document.pdf', 1000, 'application/pdf');

        $data = [
            'popup_settings' => '1',
            'popup_title' => 'Test Title',
            'popup_description' => 'Test Description',
            'popup_status' => '1',
            'popup_file' => $file
        ];

        $response = $this->actingAs($this->user)
            ->post(route('admin.settings.update'), $data);

        $response->assertRedirect(route('admin.settings.index'));
        $response->assertSessionHas('success');

        // Verify file was stored
        $filePath = Setting::getValue('popup_file');
        $this->assertNotNull($filePath);
        Storage::disk('public')->assertExists($filePath);
    }

    /** @test */
    public function it_validates_popup_file_type()
    {
        Storage::fake('public');

        // Test invalid file type (only PDF, JPG, JPEG allowed)
        $file = UploadedFile::fake()->create('document.txt', 1000, 'text/plain');

        $data = [
            'popup_settings' => '1',
            'popup_title' => 'Test Title',
            'popup_description' => 'Test Description',
            'popup_status' => '1',
            'popup_file' => $file
        ];

        $response = $this->actingAs($this->user)
            ->post(route('admin.settings.update'), $data);

        $response->assertSessionHasErrors('popup_file');
    }

    /** @test */
    public function it_validates_popup_file_size()
    {
        Storage::fake('public');

        // Test file size exceeding 5MB limit
        $file = UploadedFile::fake()->create('large-document.pdf', 6000, 'application/pdf'); // 6MB

        $data = [
            'popup_settings' => '1',
            'popup_title' => 'Test Title',
            'popup_description' => 'Test Description',
            'popup_status' => '1',
            'popup_file' => $file
        ];

        $response = $this->actingAs($this->user)
            ->post(route('admin.settings.update'), $data);

        $response->assertSessionHasErrors('popup_file');
    }

    /** @test */
    public function it_can_delete_popup_file()
    {
        Storage::fake('public');

        // First set a popup file
        Setting::setValue('popup_file', 'settings/popup-files/test-file.pdf', 'file');
        Storage::disk('public')->put('settings/popup-files/test-file.pdf', 'fake content');

        $response = $this->actingAs($this->user)
            ->delete(route('admin.settings.delete-file'), [
                'key' => 'popup_file'
            ]);

        $response->assertJson(['success' => true]);

        // Verify setting was deleted
        $this->assertNull(Setting::getValue('popup_file'));
    }

    /** @test */
    public function popup_settings_service_returns_correct_data()
    {
        Storage::fake('public');
        
        // Create a test file
        $testFile = UploadedFile::fake()->create('test.pdf', 100, 'application/pdf');
        $filePath = $testFile->store('settings/popup-files', 'public');

        // Set up popup settings
        Setting::setValue('popup_title', 'Service Test Title');
        Setting::setValue('popup_description', 'Service Test Description');
        Setting::setValue('popup_status', 1, 'boolean');
        Setting::setValue('popup_file', $filePath, 'file');

        // Clear cache to ensure fresh data
        SettingsService::clearPopupCache();

        $popupSettings = SettingsService::getPopupSettings();

        $this->assertNotNull($popupSettings);
        $this->assertEquals('Service Test Title', $popupSettings['title']);
        $this->assertEquals('Service Test Description', $popupSettings['description']);
        $this->assertTrue($popupSettings['enabled']);
        $this->assertArrayHasKey('file', $popupSettings);
        $this->assertEquals('pdf', $popupSettings['file']['type']);
    }

    /** @test */
    public function popup_settings_service_returns_null_when_disabled()
    {
        // Set up popup settings but disabled
        Setting::setValue('popup_title', 'Test Title');
        Setting::setValue('popup_description', 'Test Description');
        Setting::setValue('popup_status', 0, 'boolean'); // Disabled

        // Clear cache to ensure fresh data
        SettingsService::clearPopupCache();

        $popupSettings = SettingsService::getPopupSettings();

        $this->assertNull($popupSettings);
    }

    /** @test */
    public function popup_settings_service_returns_null_when_missing_required_fields()
    {
        // Clear any existing settings first
        Setting::where('setting_key', 'popup_title')->delete();
        Setting::where('setting_key', 'popup_description')->delete();
        Setting::where('setting_key', 'popup_status')->delete();

        // Set up popup settings but missing title (only description and status)
        Setting::setValue('popup_description', 'Test Description');
        Setting::setValue('popup_status', 1, 'boolean');
        // popup_title is intentionally not set

        // Clear cache thoroughly to ensure fresh data
        SettingsService::clearPopupCache();
        \Illuminate\Support\Facades\Cache::forget('setting_popup_title_');
        \Illuminate\Support\Facades\Cache::forget('settings_company_');

        $popupSettings = SettingsService::getPopupSettings();

        $this->assertNull($popupSettings);
    }

    /** @test */
    public function reseller_can_access_popup_settings()
    {
        $resellerUser = User::factory()->create();
        $resellerUser->assignRole('reseller');

        $response = $this->actingAs($resellerUser)
            ->get(route('admin.settings.index'));

        $response->assertStatus(200);
        $response->assertSee('Pop-up Settings');
    }

    /** @test */
    public function master_reseller_can_access_popup_settings()
    {
        $masterResellerUser = User::factory()->create();
        $masterResellerUser->assignRole('master-reseller');

        $response = $this->actingAs($masterResellerUser)
            ->get(route('admin.settings.index'));

        $response->assertStatus(200);
        $response->assertSee('Pop-up Settings');
    }

    /** @test */
    public function client_cannot_access_popup_settings()
    {
        $clientUser = User::factory()->create();
        $clientUser->assignRole('client');

        $response = $this->actingAs($clientUser)
            ->get(route('admin.settings.index'));

        $response->assertStatus(403);
    }
}
