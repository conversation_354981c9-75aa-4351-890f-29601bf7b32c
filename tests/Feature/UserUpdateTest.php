<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Role;
use App\Models\Company;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Spatie\Permission\Models\Permission;

class UserUpdateTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create roles
        $superAdminRole = Role::create(['name' => 'super-admin', 'guard_name' => 'web']);
        $masterResellerRole = Role::create(['name' => 'master-reseller', 'guard_name' => 'web']);
        $resellerRole = Role::create(['name' => 'reseller', 'guard_name' => 'web']);
        $clientRole = Role::create(['name' => 'client', 'guard_name' => 'web']);
        
        // Create permissions
        Permission::create(['name' => 'add users', 'guard_name' => 'web']);
        Permission::create(['name' => 'edit users', 'guard_name' => 'web']);
    }

    /** @test */
    public function super_admin_can_update_any_user()
    {
        // Create super admin
        $superAdmin = User::factory()->create();
        $superAdmin->assignRole('super-admin');
        
        // Create target user
        $targetUser = User::factory()->create();
        $targetUser->assignRole('client');
        
        // Act as super admin
        $this->actingAs($superAdmin);
        
        // Update user data
        $updateData = [
            'name' => 'Updated Name',
            'email' => '<EMAIL>',
            'phone' => '1234567890',
            'status' => 1,
            'remarks' => 'Updated by super admin'
        ];
        
        $response = $this->putJson(route('users.update', $targetUser), $updateData);
        
        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'User updated successfully!'
                ]);
        
        // Verify user was updated
        $targetUser->refresh();
        $this->assertEquals('Updated Name', $targetUser->name);
        $this->assertEquals('<EMAIL>', $targetUser->email);
        $this->assertEquals('1234567890', $targetUser->phone);
    }

    /** @test */
    public function master_reseller_can_update_users_in_hierarchy()
    {
        // Create master reseller
        $masterReseller = User::factory()->create();
        $masterReseller->assignRole('master-reseller');
        
        // Create reseller under master reseller
        $reseller = User::factory()->create(['parent_id' => $masterReseller->id]);
        $reseller->assignRole('reseller');
        
        // Create client under reseller
        $client = User::factory()->create(['parent_id' => $reseller->id]);
        $client->assignRole('client');
        
        // Act as master reseller
        $this->actingAs($masterReseller);
        
        // Update client data
        $updateData = [
            'name' => 'Updated Client Name',
            'email' => '<EMAIL>',
            'phone' => '9876543210'
        ];
        
        $response = $this->putJson(route('users.update', $client), $updateData);
        
        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'User updated successfully!'
                ]);
        
        // Verify client was updated
        $client->refresh();
        $this->assertEquals('Updated Client Name', $client->name);
    }

    /** @test */
    public function reseller_can_only_update_direct_clients()
    {
        // Create master reseller
        $masterReseller = User::factory()->create();
        $masterReseller->assignRole('master-reseller');
        
        // Create reseller under master reseller
        $reseller = User::factory()->create(['parent_id' => $masterReseller->id]);
        $reseller->assignRole('reseller');
        
        // Create client under reseller
        $client = User::factory()->create(['parent_id' => $reseller->id]);
        $client->assignRole('client');
        
        // Create another client under master reseller (not direct child of reseller)
        $otherClient = User::factory()->create(['parent_id' => $masterReseller->id]);
        $otherClient->assignRole('client');
        
        // Act as reseller
        $this->actingAs($reseller);
        
        // Should be able to update direct client
        $updateData = [
            'name' => 'Updated Direct Client',
            'email' => '<EMAIL>',
            'phone' => '1111111111'
        ];
        
        $response = $this->putJson(route('users.update', $client), $updateData);
        $response->assertStatus(200);
        
        // Should NOT be able to update other client
        $response = $this->putJson(route('users.update', $otherClient), $updateData);
        $response->assertStatus(403);
    }

    /** @test */
    public function client_cannot_update_other_users()
    {
        // Create client
        $client = User::factory()->create();
        $client->assignRole('client');
        
        // Create another client
        $otherClient = User::factory()->create();
        $otherClient->assignRole('client');
        
        // Act as client
        $this->actingAs($client);
        
        // Try to update other client
        $updateData = [
            'name' => 'Unauthorized Update',
            'email' => '<EMAIL>',
            'phone' => '0000000000'
        ];
        
        $response = $this->putJson(route('users.update', $otherClient), $updateData);
        
        $response->assertStatus(403);
    }

    /** @test */
    public function user_cannot_update_themselves()
    {
        // Create user
        $user = User::factory()->create();
        $user->assignRole('super-admin');
        
        // Act as the user
        $this->actingAs($user);
        
        // Try to update themselves
        $updateData = [
            'name' => 'Self Update',
            'email' => '<EMAIL>',
            'phone' => '5555555555'
        ];
        
        $response = $this->putJson(route('users.update', $user), $updateData);
        
        $response->assertStatus(403);
    }

    /** @test */
    public function validation_errors_are_returned_properly()
    {
        // Create super admin
        $superAdmin = User::factory()->create();
        $superAdmin->assignRole('super-admin');

        // Create target user
        $targetUser = User::factory()->create();
        $targetUser->assignRole('client');

        // Create another user with email we'll try to duplicate
        $existingUser = User::factory()->create(['email' => '<EMAIL>']);

        // Act as super admin
        $this->actingAs($superAdmin);

        // Try to update with invalid data
        $updateData = [
            'name' => '', // Required field empty
            'email' => '<EMAIL>', // Duplicate email
            'phone' => '' // Required field empty
        ];

        $response = $this->putJson(route('users.update', $targetUser), $updateData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['name', 'email', 'phone']);
    }

    /** @test */
    public function user_can_update_with_same_phone_number()
    {
        // Create company
        $company = Company::factory()->create();

        // Create super admin with company
        $superAdmin = User::factory()->create(['company_id' => $company->id]);
        $superAdmin->assignRole('super-admin');

        // Create target user with specific phone number and same company
        $targetUser = User::factory()->create([
            'phone' => '1234567890',
            'company_id' => $company->id
        ]);
        $targetUser->assignRole('client');

        // Act as super admin and disable middleware that might interfere
        $this->actingAs($superAdmin);

        // Update user with same phone number (should not trigger validation error)
        $updateData = [
            'name' => 'Updated Name',
            'email' => '<EMAIL>',
            'phone' => '1234567890' // Same phone number
        ];

        $response = $this->withoutMiddleware([
                        \App\Http\Middleware\VerifyCsrfToken::class,
                        \App\Http\Middleware\Tenant\Tenant::class
                    ])
                         ->putJson(route('users.update', $targetUser), $updateData);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'User updated successfully!'
                ]);

        // Verify user was updated
        $targetUser->refresh();
        $this->assertEquals('Updated Name', $targetUser->name);
        $this->assertEquals('<EMAIL>', $targetUser->email);
        $this->assertEquals('1234567890', $targetUser->phone);
    }

    /** @test */
    public function user_cannot_update_with_another_users_phone_number()
    {
        // Create company
        $company = Company::factory()->create();

        // Create super admin with company
        $superAdmin = User::factory()->create(['company_id' => $company->id]);
        $superAdmin->assignRole('super-admin');

        // Create target user with same company
        $targetUser = User::factory()->create([
            'phone' => '1111111111',
            'company_id' => $company->id
        ]);
        $targetUser->assignRole('client');

        // Create another user with different phone number and same company
        $existingUser = User::factory()->create([
            'phone' => '2222222222',
            'company_id' => $company->id
        ]);
        $existingUser->assignRole('client');

        // Act as super admin and disable middleware that might interfere
        $this->actingAs($superAdmin);

        // Try to update target user with existing user's phone number
        $updateData = [
            'name' => 'Updated Name',
            'email' => '<EMAIL>',
            'phone' => '2222222222' // Another user's phone number
        ];

        $response = $this->withoutMiddleware([
                        \App\Http\Middleware\VerifyCsrfToken::class,
                        \App\Http\Middleware\Tenant\Tenant::class
                    ])
                         ->putJson(route('users.update', $targetUser), $updateData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['phone']);
    }

    /** @test */
    public function get_available_roles_returns_correct_roles_for_super_admin()
    {
        // Create super admin
        $superAdmin = User::factory()->create();
        $superAdmin->assignRole('super-admin');
        
        // Act as super admin
        $this->actingAs($superAdmin);
        
        $response = $this->getJson('/webapi/available-roles');
        
        $response->assertStatus(200)
                ->assertJson([
                    'success' => true
                ])
                ->assertJsonStructure([
                    'success',
                    'roles'
                ]);
        
        $roles = $response->json('roles');
        
        // Super admin should see all roles except super-admin
        $this->assertArrayHasKey('2', $roles); // master-reseller
        $this->assertArrayHasKey('3', $roles); // reseller
        $this->assertArrayHasKey('4', $roles); // client
    }

    /** @test */
    public function get_available_roles_returns_limited_roles_for_reseller()
    {
        // Create reseller
        $reseller = User::factory()->create();
        $reseller->assignRole('reseller');
        
        // Act as reseller
        $this->actingAs($reseller);
        
        $response = $this->getJson('/webapi/available-roles');
        
        $response->assertStatus(200);
        
        $roles = $response->json('roles');
        
        // Reseller should only see client role
        $this->assertCount(1, $roles);
        $this->assertContains('Client', $roles);
    }
}
