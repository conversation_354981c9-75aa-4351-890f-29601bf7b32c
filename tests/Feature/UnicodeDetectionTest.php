<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class UnicodeDetectionTest extends TestCase
{
    use RefreshDatabase;

    public function test_script_message_js_contains_unicode_detection_functions()
    {
        $scriptPath = public_path('js/script.message.js');
        
        $this->assertFileExists($scriptPath);
        
        $scriptContent = file_get_contents($scriptPath);
        
        // Check for Unicode detection functions
        $this->assertStringContainsString('containsDoubleByteCharacters', $scriptContent);
        $this->assertStringContainsString('detectUnicodeAndUpdateMessageType', $scriptContent);
        $this->assertStringContainsString('updateCharacterCountForElement', $scriptContent);
        
        // Check for Unicode regex pattern
        $this->assertStringContainsString('/[^\x00-\x7F]/', $scriptContent);
        
        // Check for SMS type radio button handling
        $this->assertStringContainsString('sms_type', $scriptContent);
        $this->assertStringContainsString('unicode', $scriptContent);
        $this->assertStringContainsString('text', $scriptContent);
    }

    public function test_character_count_functionality_exists()
    {
        $scriptPath = public_path('js/script.message.js');
        $scriptContent = file_get_contents($scriptPath);
        
        // Check for character counting variables
        $this->assertStringContainsString('maximumCharactersEnglish', $scriptContent);
        $this->assertStringContainsString('maximumCharactersUnicode', $scriptContent);
        $this->assertStringContainsString('1530', $scriptContent); // English limit
        $this->assertStringContainsString('670', $scriptContent);  // Unicode limit
        
        // Check for character count update function
        $this->assertStringContainsString('updateCharacterCount', $scriptContent);
        $this->assertStringContainsString('smsCountInfo', $scriptContent);
        
        // Check for SMS count calculation
        $this->assertStringContainsString('160', $scriptContent); // English SMS length
        $this->assertStringContainsString('70', $scriptContent);  // Unicode SMS length
    }

    public function test_dynamic_sms_view_file_contains_required_elements()
    {
        $viewPath = resource_path('views/messaging/send-dynamic-sms.blade.php');
        
        $this->assertFileExists($viewPath);
        
        $viewContent = file_get_contents($viewPath);
        
        // Check for character count display elements
        $this->assertStringContainsString('smsCountInfo', $viewContent);
        $this->assertStringContainsString('Characters Left', $viewContent);
        $this->assertStringContainsString('SMS', $viewContent);
        $this->assertStringContainsString('Char./SMS', $viewContent);
        
        // Check for SMS textarea elements
        $this->assertStringContainsString('name="sms_content"', $viewContent);
        $this->assertStringContainsString('id="smsContent"', $viewContent);
        
        // Check for SMS type radio buttons
        $this->assertStringContainsString('name="sms_type"', $viewContent);
        $this->assertStringContainsString('value="text"', $viewContent);
        $this->assertStringContainsString('value="unicode"', $viewContent);
        $this->assertStringContainsString('value="flash"', $viewContent);
        $this->assertStringContainsString('value="flash_unicode"', $viewContent);
        
        // Check for script inclusion
        $this->assertStringContainsString('script.message.js', $viewContent);
    }
}