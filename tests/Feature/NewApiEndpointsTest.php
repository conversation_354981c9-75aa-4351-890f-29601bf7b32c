<?php

namespace Tests\Feature;

use App\Models\Company;
use App\Models\User;
use App\Models\Message;
use App\Models\Sender;
use App\Models\Server;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

class NewApiEndpointsTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $company;
    protected $apiKey;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a test company with API key
        $this->company = Company::factory()->create([
            'api_key' => 'test-api-key-12345',
            'current_balance' => 1000.50,
        ]);

        // Create a test user
        $this->user = User::factory()->create([
            'username' => 'testuser',
            'password' => Hash::make('testpassword'),
            'company_id' => $this->company->id,
        ]);

        // Associate user with company
        $this->user->companies()->attach($this->company->id);

        $this->apiKey = $this->company->api_key;
    }

    /** @test */
    public function it_can_get_balance_with_api_key()
    {
        $response = $this->postJson("/api/{$this->apiKey}/getBalance", [
            'api_key' => $this->apiKey
        ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'response_code',
                    'message',
                    'data' => [
                        'current_balance',
                        'balance_expired',
                        'company_name'
                    ]
                ])
                ->assertJson([
                    'response_code' => '0000',
                    'data' => [
                        'current_balance' => 1000.50,
                        'company_name' => $this->company->name
                    ]
                ]);
    }

    /** @test */
    public function it_can_get_balance_with_username_password()
    {
        $response = $this->postJson("/api/{$this->apiKey}/getBalance", [
            'username' => 'testuser',
            'password' => 'testpassword'
        ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'response_code',
                    'message',
                    'data' => [
                        'current_balance',
                        'balance_expired',
                        'company_name'
                    ]
                ]);
    }

    /** @test */
    public function it_returns_error_for_invalid_api_key()
    {
        $response = $this->postJson("/api/invalid-key/getBalance", [
            'api_key' => 'invalid-key'
        ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'response_code',
                    'message'
                ])
                ->assertJson([
                    'response_code' => '1003'
                ]);
    }

    /** @test */
    public function it_can_get_dlr_reports()
    {
        // Create test messages
        $server = Server::factory()->create();
        $sender = Sender::factory()->create(['company_id' => $this->company->id]);
        
        Message::factory()->create([
            'company_id' => $this->company->id,
            'user_id' => $this->user->id,
            'sender_id' => $sender->id,
            'server_id' => $server->id,
            'phone_number' => '1234567890',
            'sms_content' => 'Test message',
            'status' => 'delivered'
        ]);

        $response = $this->postJson("/api/{$this->apiKey}/getDLR/getAll", [
            'api_key' => $this->apiKey,
            'limit' => 10
        ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'response_code',
                    'message',
                    'data' => [
                        'total',
                        'limit',
                        'offset',
                        'messages' => [
                            '*' => [
                                'message_id',
                                'phone_number',
                                'sender_name',
                                'sms_content',
                                'status',
                                'sent_at'
                            ]
                        ]
                    ]
                ]);
    }

    /** @test */
    public function it_can_get_api_key_with_credentials()
    {
        $response = $this->postJson("/api/getkey/testuser/testpassword");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'response_code',
                    'message',
                    'data' => [
                        'api_key',
                        'company_name',
                        'user_name'
                    ]
                ])
                ->assertJson([
                    'response_code' => '0000',
                    'data' => [
                        'api_key' => $this->apiKey,
                        'company_name' => $this->company->name,
                        'user_name' => $this->user->name
                    ]
                ]);
    }

    /** @test */
    public function it_returns_error_for_invalid_credentials()
    {
        $response = $this->postJson("/api/getkey/wronguser/wrongpassword");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'response_code',
                    'message'
                ])
                ->assertJson([
                    'response_code' => '1010'
                ]);
    }
}
