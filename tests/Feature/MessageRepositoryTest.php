<?php

namespace Tests\Feature;

use App\Http\Repositories\MessageRepository;
use App\Models\Message;
use App\Models\Company;
use App\Models\User;
use App\Models\Sender;
use App\Models\Server;
use App\Tenant\Manager;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class MessageRepositoryTest extends TestCase
{
    use RefreshDatabase;

    protected $messageRepository;
    protected $company;
    protected $user;
    protected $sender;
    protected $server;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test data
        $this->company = Company::factory()->create();
        $this->user = User::factory()->create(['company_id' => $this->company->id]);
        $this->sender = Sender::factory()->create(['company_id' => $this->company->id]);
        $this->server = Server::factory()->create();
        
        // Mock the tenant manager
        $tenantManager = $this->createMock(Manager::class);
        $tenantManager->method('getTenant')->willReturn($this->company);
        $this->app->instance(Manager::class, $tenantManager);
        
        $this->messageRepository = new MessageRepository();
    }

    /** @test */
    public function store_many_generates_message_ids_for_bulk_inserts()
    {
        $messagesData = [
            [
                'sender_id' => $this->sender->id,
                'server_id' => $this->server->id,
                'phone_number' => '1234567890',
                'sms_type' => 'text',
                'sms_content' => 'Test message 1',
                'sms_count' => 1,
                'sms_cost' => 0.50,
                'batch_number' => 'TEST123',
                'user_id' => $this->user->id,
                'status' => 'pending',
            ],
            [
                'sender_id' => $this->sender->id,
                'server_id' => $this->server->id,
                'phone_number' => '0987654321',
                'sms_type' => 'text',
                'sms_content' => 'Test message 2',
                'sms_count' => 1,
                'sms_cost' => 0.50,
                'batch_number' => 'TEST123',
                'user_id' => $this->user->id,
                'status' => 'pending',
            ],
        ];

        $result = $this->messageRepository->storeMany($messagesData);

        $this->assertTrue($result);

        // Verify messages were created with message_ids
        $messages = Message::where('batch_number', 'TEST123')->get();
        $this->assertCount(2, $messages);

        foreach ($messages as $message) {
            $this->assertNotNull($message->message_id);
            $this->assertEquals(10, strlen($message->message_id));
            $this->assertMatchesRegularExpression('/^[A-Z0-9]{10}$/', $message->message_id);
        }

        // Verify message_ids are unique
        $messageIds = $messages->pluck('message_id')->toArray();
        $this->assertEquals(count($messageIds), count(array_unique($messageIds)));
    }

    /** @test */
    public function store_many_sets_company_id_from_tenant()
    {
        $messagesData = [
            [
                'sender_id' => $this->sender->id,
                'server_id' => $this->server->id,
                'phone_number' => '1234567890',
                'sms_type' => 'text',
                'sms_content' => 'Test message',
                'sms_count' => 1,
                'sms_cost' => 0.50,
                'batch_number' => 'TEST456',
                'user_id' => $this->user->id,
                'status' => 'pending',
            ],
        ];

        $this->messageRepository->storeMany($messagesData);

        $message = Message::where('batch_number', 'TEST456')->first();
        $this->assertEquals($this->company->id, $message->company_id);
    }
}
