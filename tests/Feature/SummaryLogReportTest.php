<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Company;
use App\Models\Message;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SummaryLogReportTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user with super-admin role
        $this->user = User::factory()->create();
        $this->user->assignRole('super-admin');
        
        // Create test companies
        $this->company1 = Company::factory()->create(['name' => 'Test Company 1']);
        $this->company2 = Company::factory()->create(['name' => 'Test Company 2']);
        
        // Create test messages
        Message::factory()->create([
            'company_id' => $this->company1->id,
            'user_id' => $this->user->id,
            'sms_cost' => 10.50,
            'created_at' => now()
        ]);
        
        Message::factory()->create([
            'company_id' => $this->company1->id,
            'user_id' => $this->user->id,
            'sms_cost' => 15.25,
            'created_at' => now()
        ]);
        
        Message::factory()->create([
            'company_id' => $this->company2->id,
            'user_id' => $this->user->id,
            'sms_cost' => 8.75,
            'created_at' => now()
        ]);
    }

    /** @test */
    public function it_can_access_summary_log_page()
    {
        $response = $this->actingAs($this->user)
            ->get('/reports/summary-log');

        $response->assertStatus(200);
        $response->assertViewIs('reports.summary-log');
        $response->assertSee('Summary Log');
        $response->assertSee('Company-wise SMS usage summary and cost breakdown');
    }

    /** @test */
    public function it_can_fetch_summary_log_data_via_datatables()
    {
        $response = $this->actingAs($this->user)
            ->get('/datatables/summary-log-list');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data' => [
                '*' => [
                    'serial',
                    'company_name',
                    'total_sms_sent',
                    'total_amount_deducted'
                ]
            ]
        ]);
    }

    /** @test */
    public function it_aggregates_data_correctly_by_company()
    {
        $response = $this->actingAs($this->user)
            ->get('/datatables/summary-log-list');

        $data = $response->json('data');
        
        // Should have 2 companies
        $this->assertCount(2, $data);
        
        // Find company 1 data
        $company1Data = collect($data)->firstWhere('company_name', 'Test Company 1');
        $this->assertNotNull($company1Data);
        $this->assertEquals('2', $company1Data['total_sms_sent']); // 2 messages
        $this->assertEquals('25.75', $company1Data['total_amount_deducted']); // 10.50 + 15.25
        
        // Find company 2 data
        $company2Data = collect($data)->firstWhere('company_name', 'Test Company 2');
        $this->assertNotNull($company2Data);
        $this->assertEquals('1', $company2Data['total_sms_sent']); // 1 message
        $this->assertEquals('8.75', $company2Data['total_amount_deducted']); // 8.75
    }

    /** @test */
    public function it_can_export_summary_log_data()
    {
        $response = $this->actingAs($this->user)
            ->get('/reports/export-summary-log');

        $response->assertStatus(200);
        $response->assertHeader('content-type', 'text/csv; charset=UTF-8');
        $response->assertHeader('content-disposition', 'attachment; filename="summary_log_report_' . date('Y-m-d') . '.csv"');
        
        $content = $response->getContent();
        $this->assertStringContainsString('Company Name,Total SMS Sent,Total Amount Deducted', $content);
        $this->assertStringContainsString('Test Company 1', $content);
        $this->assertStringContainsString('Test Company 2', $content);
    }

    /** @test */
    public function it_filters_by_date_range()
    {
        // Create a message from yesterday
        Message::factory()->create([
            'company_id' => $this->company1->id,
            'user_id' => $this->user->id,
            'sms_cost' => 5.00,
            'created_at' => now()->subDay()
        ]);

        $response = $this->actingAs($this->user)
            ->get('/datatables/summary-log-list?' . http_build_query([
                'date_from' => now()->format('Y-m-d'),
                'date_to' => now()->format('Y-m-d')
            ]));

        $data = $response->json('data');
        
        // Should only include today's messages
        $company1Data = collect($data)->firstWhere('company_name', 'Test Company 1');
        $this->assertEquals('2', $company1Data['total_sms_sent']); // Only today's 2 messages
        $this->assertEquals('25.75', $company1Data['total_amount_deducted']); // Only today's costs
    }
}
