<?php

namespace Tests\Feature;

use App\Models\Message;
use App\Models\Company;
use App\Models\User;
use App\Models\Sender;
use App\Models\Server;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class MessageIdTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test data
        $this->company = Company::factory()->create();
        $this->user = User::factory()->create(['company_id' => $this->company->id]);
        $this->sender = Sender::factory()->create(['company_id' => $this->company->id]);
        $this->server = Server::factory()->create();
    }

    /** @test */
    public function message_id_is_automatically_generated_on_creation()
    {
        $message = Message::create([
            'sender_id' => $this->sender->id,
            'server_id' => $this->server->id,
            'phone_number' => '1234567890',
            'sms_type' => 'text',
            'sms_content' => 'Test message',
            'sms_count' => 1,
            'sms_cost' => 0.50,
            'batch_number' => 'TEST123',
            'company_id' => $this->company->id,
            'user_id' => $this->user->id,
            'status' => 'pending',
        ]);

        $this->assertNotNull($message->message_id);
        $this->assertEquals(10, strlen($message->message_id));
        $this->assertMatchesRegularExpression('/^[A-Z0-9]{10}$/', $message->message_id);
    }

    /** @test */
    public function message_id_is_unique()
    {
        $message1 = Message::create([
            'sender_id' => $this->sender->id,
            'server_id' => $this->server->id,
            'phone_number' => '1234567890',
            'sms_type' => 'text',
            'sms_content' => 'Test message 1',
            'sms_count' => 1,
            'sms_cost' => 0.50,
            'batch_number' => 'TEST123',
            'company_id' => $this->company->id,
            'user_id' => $this->user->id,
            'status' => 'pending',
        ]);

        $message2 = Message::create([
            'sender_id' => $this->sender->id,
            'server_id' => $this->server->id,
            'phone_number' => '0987654321',
            'sms_type' => 'text',
            'sms_content' => 'Test message 2',
            'sms_count' => 1,
            'sms_cost' => 0.50,
            'batch_number' => 'TEST456',
            'company_id' => $this->company->id,
            'user_id' => $this->user->id,
            'status' => 'pending',
        ]);

        $this->assertNotEquals($message1->message_id, $message2->message_id);
    }

    /** @test */
    public function message_id_can_be_manually_set()
    {
        $customMessageId = 'CUSTOM1234';
        
        $message = Message::create([
            'message_id' => $customMessageId,
            'sender_id' => $this->sender->id,
            'server_id' => $this->server->id,
            'phone_number' => '1234567890',
            'sms_type' => 'text',
            'sms_content' => 'Test message',
            'sms_count' => 1,
            'sms_cost' => 0.50,
            'batch_number' => 'TEST123',
            'company_id' => $this->company->id,
            'user_id' => $this->user->id,
            'status' => 'pending',
        ]);

        $this->assertEquals($customMessageId, $message->message_id);
    }

    /** @test */
    public function generate_unique_message_id_method_works()
    {
        $messageId1 = Message::generateUniqueMessageId();
        $messageId2 = Message::generateUniqueMessageId();

        $this->assertNotEquals($messageId1, $messageId2);
        $this->assertEquals(10, strlen($messageId1));
        $this->assertEquals(10, strlen($messageId2));
        $this->assertMatchesRegularExpression('/^[A-Z0-9]{10}$/', $messageId1);
        $this->assertMatchesRegularExpression('/^[A-Z0-9]{10}$/', $messageId2);
    }
}
