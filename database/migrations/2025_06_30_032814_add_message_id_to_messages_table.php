<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('messages', function (Blueprint $table) {
            // Add message_id column as a unique 10-character alphanumeric string
            $table->string('message_id', 10)->unique()->nullable()->after('id');
            $table->index('message_id'); // Add index for performance
        });

        // Generate message_id for existing records
        $this->generateMessageIdsForExistingRecords();

        // Make the column non-nullable after populating existing records
        Schema::table('messages', function (Blueprint $table) {
            $table->string('message_id', 10)->nullable(false)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('messages', function (Blueprint $table) {
            $table->dropIndex(['message_id']);
            $table->dropColumn('message_id');
        });
    }

    /**
     * Generate unique message IDs for existing records
     */
    private function generateMessageIdsForExistingRecords(): void
    {
        $messages = \DB::table('messages')->whereNull('message_id')->get(['id']);

        foreach ($messages as $message) {
            $messageId = $this->generateUniqueMessageId();
            \DB::table('messages')
                ->where('id', $message->id)
                ->update(['message_id' => $messageId]);
        }
    }

    /**
     * Generate a unique 10-character alphanumeric message ID
     */
    private function generateUniqueMessageId(): string
    {
        do {
            $messageId = strtoupper(Str::random(10));
        } while (\DB::table('messages')->where('message_id', $messageId)->exists());

        return $messageId;
    }
};
