<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('impersonation_logs', function (Blueprint $table) {
            $table->integer('level')->default(1)->after('action');
            $table->json('chain_data')->nullable()->after('level');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('impersonation_logs', function (Blueprint $table) {
            $table->dropColumn(['level', 'chain_data']);
        });
    }
};
