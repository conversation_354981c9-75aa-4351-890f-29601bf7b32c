<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add balance_after column to transactions table
        Schema::table('transactions', function (Blueprint $table) {
            $table->decimal('balance_after', 10, 2)->nullable()->after('amount_out');
        });

        // Drop the existing trigger
        DB::unprepared('DROP TRIGGER IF EXISTS create_new_transaction');

        // Create updated trigger that includes balance_after
        DB::unprepared('CREATE TRIGGER create_new_transaction
            AFTER UPDATE ON companies
            FOR EACH ROW
            BEGIN
                DECLARE amount_in DECIMAL(10,2);
                DECLARE amount_out DECIMAL(10,2);

                IF NEW.current_balance != OLD.current_balance THEN
                    SET amount_in = IF(NEW.current_balance > OLD.current_balance, NEW.current_balance - OLD.current_balance, 0);
                    SET amount_out = IF(NEW.current_balance < OLD.current_balance, OLD.current_balance - NEW.current_balance, 0);

                    INSERT INTO transactions
                    (user_id, amount_in, amount_out, balance_after, date, company_id, remarks, created_at, updated_at)
                    VALUES (
                        1,
                        amount_in,
                        amount_out,
                        NEW.current_balance,
                        NOW(),
                        NEW.id,
                        NEW.remarks,
                        NOW(),
                        NOW()
                    );
                END IF;
            END');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop the updated trigger
        DB::unprepared('DROP TRIGGER IF EXISTS create_new_transaction');

        // Recreate the original trigger without balance_after
        DB::unprepared('CREATE TRIGGER create_new_transaction
            AFTER UPDATE ON companies
            FOR EACH ROW
            BEGIN
                DECLARE amount_in DECIMAL(10,2);
                DECLARE amount_out DECIMAL(10,2);

                IF NEW.current_balance != OLD.current_balance THEN
                    SET amount_in = IF(NEW.current_balance > OLD.current_balance, NEW.current_balance - OLD.current_balance, 0);
                    SET amount_out = IF(NEW.current_balance < OLD.current_balance, OLD.current_balance - NEW.current_balance, 0);

                    INSERT INTO transactions
                    (user_id, amount_in, amount_out, date, company_id, remarks, created_at, updated_at)
                    VALUES (
                        1,
                        amount_in,
                        amount_out,
                        CURRENT_DATE,
                        NEW.id,
                        NEW.remarks,
                        NOW(),
                        NOW()
                    );
                END IF;
            END');

        // Remove the balance_after column
        Schema::table('transactions', function (Blueprint $table) {
            $table->dropColumn('balance_after');
        });
    }
};
