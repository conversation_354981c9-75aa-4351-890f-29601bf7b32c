<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('impersonation_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('impersonator_id'); // The admin who started impersonation
            $table->unsignedBigInteger('impersonated_id'); // The user being impersonated
            $table->enum('action', ['start', 'stop']); // Whether this is start or stop
            $table->string('ip_address')->nullable();
            $table->text('user_agent')->nullable();
            $table->timestamp('action_at'); // When the action occurred
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('impersonator_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('impersonated_id')->references('id')->on('users')->onDelete('cascade');

            // Indexes for better performance
            $table->index(['impersonator_id', 'action_at']);
            $table->index(['impersonated_id', 'action_at']);
            $table->index('action_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('impersonation_logs');
    }
};
