<?php

namespace Database\Seeders;

use App\Models\Server;
use Illuminate\Database\Seeder;

class ServerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Server::create([
            'name' => 'RouteMobile',
            'api_link' => 'http://apibd.rmlconnect.net/bulksms/personalizedbulksms',
            'api_key' => '',
            'username' => 'Miraj3',
            'password' => '8m43VREX',
            'sending_limit' => '60',
            'time_base' => '1',
            'time_unit' => 'minute',
            'enable_service' => 'all',
            'user_id' => '1',
            'status' => 'enabled',
        ]);

        Server::create([
            'name' => 'ElitBuzz',
            'api_link' => 'https://bangladeshsms.com/smsapi',
            'api_key' => 'R60006406704b980654199.85914489',
            'username' => '',
            'password' => '',
            'sending_limit' => '100',
            'time_base' => '1',
            'time_unit' => 'minute',
            'enable_service' => 'all',
            'user_id' => '1',
            'status' => 'enabled',
        ]);
    }
}
