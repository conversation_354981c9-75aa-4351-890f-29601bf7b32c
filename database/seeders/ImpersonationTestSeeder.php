<?php

namespace Database\Seeders;

use App\Models\ImpersonationLog;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;

class ImpersonationTestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Ensure roles exist
        $roles = ['super-admin', 'admin', 'master-reseller', 'reseller', 'user'];
        foreach ($roles as $roleName) {
            Role::firstOrCreate(['name' => $roleName]);
        }

        // Create test users for different roles
        $superAdmin = User::factory()->create([
            'name' => 'Super Admin Test User',
            'email' => '<EMAIL>',
        ]);
        $superAdmin->assignRole('super-admin');

        $admin = User::factory()->create([
            'name' => 'Admin Test User',
            'email' => '<EMAIL>',
        ]);
        $admin->assignRole('admin');

        $masterReseller = User::factory()->create([
            'name' => 'Master Reseller Test User',
            'email' => '<EMAIL>',
        ]);
        $masterReseller->assignRole('master-reseller');

        $reseller = User::factory()->create([
            'name' => 'Reseller Test User',
            'email' => '<EMAIL>',
        ]);
        $reseller->assignRole('reseller');

        $regularUser = User::factory()->create([
            'name' => 'Regular User Test',
            'email' => '<EMAIL>',
        ]);
        $regularUser->assignRole('user');

        // Create some sample impersonation logs
        $chainData1 = [
            ['user_id' => $superAdmin->id, 'level' => 1, 'started_at' => now()->subHours(2)->toISOString()]
        ];

        $chainData2 = [
            ['user_id' => $superAdmin->id, 'level' => 1, 'started_at' => now()->subHours(1)->toISOString()],
            ['user_id' => $admin->id, 'level' => 2, 'started_at' => now()->subMinutes(30)->toISOString()]
        ];

        // Single level impersonation logs
        ImpersonationLog::create([
            'impersonator_id' => $superAdmin->id,
            'impersonated_id' => $admin->id,
            'action' => 'start',
            'level' => 1,
            'chain_data' => $chainData1,
            'ip_address' => '*************',
            'user_agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
            'action_at' => now()->subHours(2),
        ]);

        ImpersonationLog::create([
            'impersonator_id' => $superAdmin->id,
            'impersonated_id' => $admin->id,
            'action' => 'stop',
            'level' => 1,
            'chain_data' => $chainData1,
            'ip_address' => '*************',
            'user_agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
            'action_at' => now()->subHours(1)->subMinutes(30),
        ]);

        // Multi-level impersonation logs
        ImpersonationLog::create([
            'impersonator_id' => $superAdmin->id,
            'impersonated_id' => $admin->id,
            'action' => 'start',
            'level' => 1,
            'chain_data' => [['user_id' => $superAdmin->id, 'level' => 1, 'started_at' => now()->subHours(1)->toISOString()]],
            'ip_address' => '*************',
            'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'action_at' => now()->subHours(1),
        ]);

        ImpersonationLog::create([
            'impersonator_id' => $admin->id,
            'impersonated_id' => $regularUser->id,
            'action' => 'start',
            'level' => 2,
            'chain_data' => $chainData2,
            'ip_address' => '*************',
            'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'action_at' => now()->subMinutes(30),
        ]);

        ImpersonationLog::create([
            'impersonator_id' => $admin->id,
            'impersonated_id' => $regularUser->id,
            'action' => 'stop',
            'level' => 2,
            'chain_data' => $chainData2,
            'ip_address' => '*************',
            'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'action_at' => now()->subMinutes(10),
        ]);

        ImpersonationLog::create([
            'impersonator_id' => $superAdmin->id,
            'impersonated_id' => $admin->id,
            'action' => 'stop',
            'level' => 1,
            'chain_data' => [['user_id' => $superAdmin->id, 'level' => 1, 'started_at' => now()->subHours(1)->toISOString()]],
            'ip_address' => '*************',
            'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'action_at' => now()->subMinutes(5),
        ]);

        $this->command->info('Impersonation test seeder completed successfully!');
    }
}