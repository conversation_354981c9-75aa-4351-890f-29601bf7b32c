<?php

namespace Database\Seeders;

use App\Models\Notice;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class NoticeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first super-admin user
        $superAdmin = User::role('super-admin')->first();
        
        if (!$superAdmin) {
            $this->command->warn('No super-admin user found. Please create a super-admin user first.');
            return;
        }

        $notices = [
            [
                'title' => 'System Maintenance Notice',
                'description' => 'We will be performing scheduled maintenance on our SMS servers on Sunday, July 21st from 2:00 AM to 4:00 AM UTC. During this time, SMS services may be temporarily unavailable. We apologize for any inconvenience.',
                'created_by' => $superAdmin->id,
                'is_active' => true,
            ],
            [
                'title' => 'New Unicode SMS Support',
                'description' => 'We are excited to announce enhanced Unicode SMS support! You can now send SMS messages in multiple languages including Arabic, Chinese, Hindi, and more. The system will automatically detect Unicode characters and adjust pricing accordingly.',
                'created_by' => $superAdmin->id,
                'is_active' => true,
            ],
            [
                'title' => 'Rate Changes Effective August 1st',
                'description' => 'Please be informed that SMS rates for certain international destinations will be updated effective August 1st, 2025. Please check the updated pricing in your coverage section. Existing credits will remain valid.',
                'created_by' => $superAdmin->id,
                'is_active' => true,
            ],
            [
                'title' => 'Security Enhancement Update',
                'description' => 'We have implemented additional security measures including enhanced user authentication and improved session management. All users are advised to update their passwords and enable two-factor authentication if available.',
                'created_by' => $superAdmin->id,
                'is_active' => false,
            ],
            [
                'title' => 'API Documentation Updated',
                'description' => 'Our API documentation has been updated with new endpoints and improved examples. Developers can now access enhanced features for bulk SMS sending and delivery report tracking. Visit the developers section for more details.',
                'created_by' => $superAdmin->id,
                'is_active' => true,
            ],
        ];

        foreach ($notices as $noticeData) {
            Notice::create($noticeData);
        }

        $this->command->info('Notice seeder completed successfully!');
    }
}
