<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Seeder;

class PopupSettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // BTRC Guideline Pop-up Settings
        $title = 'Important Notification';
        
        $description = 'As per attached (BTRC) guideline for SMS broadcast please flow the below instruction from now onward.


1. নিয়ম অনুযায়ী, টারগেটিং/এপিআই/নাম্বারলিস্ট এর সকল ধরণের প্রোমোশনাল/ গ্রিটিংস এসএমএস অবশ্যই বাংলায় ( ইউনিকোড) হতে হবে। শুধুমাত্র মেশিন জেনারেটেড এসএমএস / নোটিফিকেশন ( উদাহরণঃ এটিএম কার্ড OTP ) ,ছাড়া সব ধরণের এসএমএস অবশ্যই বাংলা ( ইউনিকোড) ব্যবহার করতে আহবান এবং সতর্ক করা হচ্ছে। অন্যথায় একাউন্ট স্থগিত হবে এবং ইউজার/গ্রাহক/ক্লায়েন্ট এর দায়ভার বহন করবেন। ধন্যবাদ।

Dear User/Client, Greetings! First, both API and Campaign SMS must be in Bangla (Unicode) according to regulations. Except for only machine-generated SMS/notification (for example, ATM card OTP, etc.), all other SMS content must be in Bangla. The rules and regulations stated here have to be followed by all. Otherwise, the SMS account will be blocked, and the User/Client will bear the responsibility for any damage caused due to violation of this regulation. Thanks.

2. The content is to be vetted from BTRC before broadcast and then shared with MNOs.

Please take immediate action and comply with BTRC guideline.';

        $status = true;

        // Get the first super admin user or create settings without user reference
        $superAdminUser = \App\Models\User::whereHas('roles', function($query) {
            $query->where('name', 'super-admin');
        })->first();

        $userId = $superAdminUser ? $superAdminUser->id : null;

        // Create global popup settings (company_id = null for super admin)
        Setting::updateOrCreate(
            [
                'setting_key' => 'popup_title',
                'company_id' => null,
            ],
            [
                'setting_value' => $title,
                'setting_type' => 'string',
                'created_by' => $userId,
                'updated_by' => $userId,
            ]
        );

        Setting::updateOrCreate(
            [
                'setting_key' => 'popup_description',
                'company_id' => null,
            ],
            [
                'setting_value' => $description,
                'setting_type' => 'string',
                'created_by' => $userId,
                'updated_by' => $userId,
            ]
        );

        Setting::updateOrCreate(
            [
                'setting_key' => 'popup_status',
                'company_id' => null,
            ],
            [
                'setting_value' => $status ? '1' : '0',
                'setting_type' => 'boolean',
                'created_by' => $userId,
                'updated_by' => $userId,
            ]
        );

        $this->command->info('BTRC Guideline popup settings have been seeded successfully.');
    }
}
