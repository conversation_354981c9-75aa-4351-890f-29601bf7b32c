<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;

class CheckBalanceConsistency extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'balance:check-consistency {--user-id= : Check specific user ID} {--limit=10 : Number of users to check}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check balance consistency between DataTable and details page calculations';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Checking balance consistency...');
        $this->newLine();

        $query = User::with('company', 'roles');

        if ($userId = $this->option('user-id')) {
            $query->where('id', $userId);
        } else {
            $query->limit($this->option('limit'));
        }

        $users = $query->get();

        if ($users->isEmpty()) {
            $this->warn('No users found to check.');
            return;
        }

        $inconsistencies = 0;
        $totalChecked = 0;

        foreach ($users as $user) {
            $totalChecked++;
            
            // Simulate DataTable balance calculation (new approach)
            $datatableBalance = $user->company ? $user->company->current_balance : 0;
            $datatableFormatted = '$' . number_format($datatableBalance, 2);

            // Simulate details page balance calculation
            $detailsBalance = $user->company->current_balance ?? 0;
            $detailsFormatted = '$' . number_format($detailsBalance, 2);

            // Check for consistency
            $isConsistent = $datatableBalance === $detailsBalance;

            if (!$isConsistent) {
                $inconsistencies++;
                $this->error("❌ User ID {$user->id} ({$user->name}):");
                $this->line("   DataTable: {$datatableFormatted}");
                $this->line("   Details:   {$detailsFormatted}");
                $this->newLine();
            } else {
                $this->info("✅ User ID {$user->id} ({$user->name}): {$datatableFormatted}");
            }

            // Show additional info if requested
            if ($this->option('user-id')) {
                $this->line("   Company ID: " . ($user->company_id ?? 'NULL'));
                $this->line("   Company Name: " . ($user->company->name ?? 'N/A'));
                $this->line("   Role: " . ($user->roles->first()->name ?? 'N/A'));
                $this->line("   Raw Balance: " . ($user->company->current_balance ?? 'N/A'));
                $this->newLine();
            }
        }

        $this->newLine();
        $this->info("Summary:");
        $this->line("Total users checked: {$totalChecked}");
        $this->line("Consistent: " . ($totalChecked - $inconsistencies));
        $this->line("Inconsistent: {$inconsistencies}");

        if ($inconsistencies === 0) {
            $this->info("🎉 All balances are consistent!");
        } else {
            $this->warn("⚠️  Found {$inconsistencies} inconsistencies that need attention.");
        }

        return $inconsistencies === 0 ? 0 : 1;
    }
}
