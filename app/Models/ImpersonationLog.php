<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ImpersonationLog extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'impersonator_id',
        'impersonated_id',
        'action',
        'level',
        'chain_data',
        'ip_address',
        'user_agent',
        'action_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'action_at' => 'datetime',
        'chain_data' => 'array',
    ];

    /**
     * Get the user who performed the impersonation (admin).
     */
    public function impersonator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'impersonator_id');
    }

    /**
     * Get the user who was impersonated.
     */
    public function impersonated(): BelongsTo
    {
        return $this->belongsTo(User::class, 'impersonated_id');
    }

    /**
     * Create a log entry for impersonation start.
     */
    public static function logStart(User $impersonator, User $impersonated, string $ipAddress = null, string $userAgent = null, int $level = 1, array $chainData = null): self
    {
        return self::create([
            'impersonator_id' => $impersonator->id,
            'impersonated_id' => $impersonated->id,
            'action' => 'start',
            'level' => $level,
            'chain_data' => $chainData,
            'ip_address' => $ipAddress,
            'user_agent' => $userAgent,
            'action_at' => now(),
        ]);
    }

    /**
     * Create a log entry for impersonation stop.
     */
    public static function logStop(User $impersonator, User $impersonated, string $ipAddress = null, string $userAgent = null, int $level = 1, array $chainData = null): self
    {
        return self::create([
            'impersonator_id' => $impersonator->id,
            'impersonated_id' => $impersonated->id,
            'action' => 'stop',
            'level' => $level,
            'chain_data' => $chainData,
            'ip_address' => $ipAddress,
            'user_agent' => $userAgent,
            'action_at' => now(),
        ]);
    }

    /**
     * Get logs for a specific impersonation level.
     */
    public function scopeByLevel($query, int $level)
    {
        return $query->where('level', $level);
    }

    /**
     * Get logs for multi-level impersonation chains.
     */
    public function scopeMultiLevel($query)
    {
        return $query->where('level', '>', 1);
    }

    /**
     * Get the impersonation chain data as a formatted string.
     */
    public function getFormattedChainAttribute(): string
    {
        if (!$this->chain_data) {
            return "Level {$this->level}";
        }

        $chain = collect($this->chain_data)->map(function ($item) {
            return "L{$item['level']}";
        })->implode(' → ');

        return $chain ?: "Level {$this->level}";
    }
}
