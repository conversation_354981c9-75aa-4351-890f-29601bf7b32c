<?php

namespace App\Models;

use App\Tenant\Traits\ForTenants;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Payment extends Model
{
    use ForTenants;
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'amount',
        'gateway',
        'transaction_id',
        'user_id',
        'company_id',
        'payment_status',
        'remarks',
        'api_response',
    ];

    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function recharge()
    {
        return $this->hasOne(Recharge::class);
    }
}
