<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Company extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'name',
        'company_id',
        'logo',
        'remarks',
        'status',
        'current_balance',
        'balance_expired',
        'api_key',
        'margin_type',
        'minimum_recharge_amount',
    ];

    protected $casts = [
        'balance_expired' => 'datetime',
    ];

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class);
    }

    public function contacts()
    {
        return $this->hasMany(User::class);
    }

    public function coverages()
    {
        return $this->hasMany(Coverage::class);
    }

    public function parent()
    {
        return $this->belongsTo(Company::class, 'company_id')
            ->select('id', 'company_id', 'current_balance')
            ->with('parent')
            ->whereNotNull('company_id');
    }

    public function transactions()
    {
        return $this->hasMany(Transaction::class);
    }

    public function domains()
    {
        return $this->hasMany(Domain::class);
    }
}
