<?php

namespace App\Models;

use App\Tenant\Traits\ForTenants;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Message extends Model
{
    use ForTenants;
    use HasFactory;

    protected $fillable = [
        'message_id',
        'sender_id',
        'server_id',
        'phone_number',
        'sms_type',
        'sms_content',
        'sms_count',
        'sms_cost',
        'batch_number',
        'schedule_at',
        'api_response',
        'company_id',
        'user_id',
        'status',
    ];

    /**
     * Boot the model and set up event listeners
     */
    protected static function boot()
    {
        parent::boot();

        // Auto-generate message_id when creating a new message
        static::creating(function ($message) {
            if (empty($message->message_id)) {
                $message->message_id = static::generateUniqueMessageId();
            }
        });
    }

    /**
     * Generate a unique 10-character alphanumeric message ID
     */
    public static function generateUniqueMessageId(): string
    {
        do {
            $messageId = strtoupper(Str::random(10));
        } while (static::where('message_id', $messageId)->exists());

        return $messageId;
    }

    public function server()
    {
        return $this->belongsTo(Server::class);
    }

    public function sender()
    {
        return $this->belongsTo(Sender::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function company()
    {
        return $this->belongsTo(Company::class);
    }
}
