<?php

namespace App\Models;

use App\Tenant\Traits\ForTenants;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Sender extends Model
{
    use ForTenants, HasFactory;

    protected $fillable = [
        'name',
        'executed_at',
        'company_id',
        'user_id',
        'server_id',          // Added server_id field
        'is_masking',
        'is_default',
        'is_server_default',   // Added is_server_default field
        'status',
        'required_documents',  // Added required_documents field
    ];

    protected $casts = [
        'executed_at' => 'datetime',
        'is_masking' => 'boolean',
        'is_default' => 'boolean',
        'is_server_default' => 'boolean',
        'required_documents' => 'array', // Cast required_documents to array
    ];

    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    public function server()
    {
        return $this->belongsTo(Server::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
