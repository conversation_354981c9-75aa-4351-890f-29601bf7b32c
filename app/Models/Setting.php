<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Cache;

class Setting extends Model
{
    use HasFactory;

    protected $fillable = [
        'setting_key',
        'setting_value',
        'setting_type',
        'company_id',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'setting_value' => 'string',
    ];

    /**
     * Boot the model and set up event listeners
     */
    protected static function boot()
    {
        parent::boot();

        // Auto-set created_by and updated_by
        static::creating(function ($setting) {
            if (auth()->check()) {
                $setting->created_by = auth()->id();
                $setting->updated_by = auth()->id();
            }
        });

        static::updating(function ($setting) {
            if (auth()->check()) {
                $setting->updated_by = auth()->id();
            }
        });

        // Clear cache when settings are modified
        static::saved(function ($setting) {
            Cache::forget("setting_{$setting->setting_key}_{$setting->company_id}");
            Cache::forget("settings_company_{$setting->company_id}");
        });

        static::deleted(function ($setting) {
            Cache::forget("setting_{$setting->setting_key}_{$setting->company_id}");
            Cache::forget("settings_company_{$setting->company_id}");
        });
    }

    /**
     * Get the company that owns the setting
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * Get the user who created the setting
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated the setting
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Scope for global settings (super admin)
     */
    public function scopeGlobal($query)
    {
        return $query->whereNull('company_id');
    }

    /**
     * Scope for company-specific settings
     */
    public function scopeForCompany($query, $companyId)
    {
        return $query->where('company_id', $companyId);
    }

    /**
     * Get a setting value with fallback to global setting
     */
    public static function getValue($key, $companyId = null, $default = null)
    {
        $cacheKey = "setting_{$key}_{$companyId}";

        return Cache::remember($cacheKey, 3600, function () use ($key, $companyId, $default) {
            // First try company-specific setting
            if ($companyId) {
                $setting = static::where('setting_key', $key)
                    ->where('company_id', $companyId)
                    ->first();

                if ($setting) {
                    return static::castValue($setting->setting_value, $setting->setting_type);
                }
            }

            // Fallback to global setting
            $globalSetting = static::where('setting_key', $key)
                ->whereNull('company_id')
                ->first();

            if ($globalSetting) {
                return static::castValue($globalSetting->setting_value, $globalSetting->setting_type);
            }

            return $default;
        });
    }

    /**
     * Set a setting value
     */
    public static function setValue($key, $value, $type = 'string', $companyId = null)
    {
        return static::updateOrCreate(
            [
                'setting_key' => $key,
                'company_id' => $companyId,
            ],
            [
                'setting_value' => $value,
                'setting_type' => $type,
            ]
        );
    }

    /**
     * Cast setting value based on type
     */
    protected static function castValue($value, $type)
    {
        switch ($type) {
            case 'boolean':
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);
            case 'json':
                return json_decode($value, true);
            case 'file':
            case 'string':
            default:
                return $value;
        }
    }

    /**
     * Get all settings for a company as key-value pairs
     */
    public static function getAllForCompany($companyId = null)
    {
        $cacheKey = "settings_company_{$companyId}";

        return Cache::remember($cacheKey, 3600, function () use ($companyId) {
            $settings = [];

            // Get global settings first
            $globalSettings = static::whereNull('company_id')->get();
            foreach ($globalSettings as $setting) {
                $settings[$setting->setting_key] = static::castValue($setting->setting_value, $setting->setting_type);
            }

            // Override with company-specific settings if company ID provided
            if ($companyId) {
                $companySettings = static::where('company_id', $companyId)->get();
                foreach ($companySettings as $setting) {
                    $settings[$setting->setting_key] = static::castValue($setting->setting_value, $setting->setting_type);
                }
            }

            return $settings;
        });
    }
}
