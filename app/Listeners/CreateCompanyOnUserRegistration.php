<?php

namespace App\Listeners;

use App\Events\UserRegistered;
use App\Models\Company;
use App\Models\Coverage;
use Carbon\Carbon;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Str;

class CreateCompanyOnUserRegistration implements ShouldQueue
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(UserRegistered $event)
    {
        // Logic to create a company
        $company = Company::create([
            'name' => $event->user->name."'s company",
            'logo' => null,
            'remarks' => '',
            'status' => 'enabled',
            'minimum_recharge_amount' => 0,
            'current_balance' => 0,
            'api_key' => Str::random(30),
            'balance_expired' => Carbon::now(),
        ]);

        // Assign the company to the user (both relationships)
        $event->user->company_id = $company->id;
        $event->user->save();
        $event->user->companies()->attach($company->id);

        // Insert records into the coverages table for the company
        $this->createDefaultCoverages($company);
    }

    private function createDefaultCoverages(Company $company): void
    {
        // Insert default records into the coverages table
        $default_coverages = Coverage::where('company_id', 1)
            ->get()
            ->map(function ($coverage) use ($company) {
                return [
                    'company_id' => $company->id,
                    'prefix' => $coverage->prefix,
                    'masking_price' => $coverage->masking_price,
                    'non_masking_price' => $coverage->non_masking_price,
                    'operator' => $coverage->operator,
                    'server_id' => $coverage->server_id,
                    'status' => $coverage->status,
                    'created_at' => now(),
                ];
            })->toArray();

        Coverage::insert($default_coverages);
    }
}
