<?php

namespace App\Http\Controllers;

use App\Exceptions\ErrorMessageException;
use App\Helpers\Traits\HierarchicalPermissions;
use App\Http\Requests\StoreRechargeRequest;
use App\Http\Services\PaymentService;
use App\Models\Payment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class PaymentController extends Controller
{
    use HierarchicalPermissions;

    private $payment;

    public function __construct(PaymentService $payment)
    {
        $this->payment = $payment;
    }

    /**
     * Payment proceed method
     *
     * @return mixed
     */
    public function index(StoreRechargeRequest $request)
    {
        return $this->payment->proceedToPayment($request);
    }

    /**
     * @throws ErrorMessageException
     */
    public function success(Request $request)
    {
        // For manual payments, admin approval is the success confirmation
        // For other gateways, check the status parameter
        if ($request->payment_method !== 'manual' && $request->status != 'success') {
            throw new ErrorMessageException('Recharged failed');
        }

        // For manual payments, validate permission to approve
        if ($request->payment_method === 'manual') {
            $payment = $this->validateManualPaymentApprovalPermission($request);
            $this->logPaymentApprovalAttempt($payment, auth()->user(), 'approved');
        }

        $this->payment->success($request);

        // Clear dashboard cache after successful payment approval
        if ($request->payment_method === 'manual') {
            $this->clearDashboardCache();
        }

        if (! empty($request->route)) {
            return redirect()->route($request->route)->with('success', 'Recharged approved successfully');
        }

        return redirect()->route('account.recharge-history')->with('success', 'Recharge successfully');
    }

    public function fail(Request $request)
    {
        $this->payment->fail($request);

        return redirect()->route('account.recharge-history');
    }

    public function cancel(Request $request)
    {
        $this->payment->canceled($request);

        return redirect()->route('account.recharge-history');
    }

    public function ipn(Request $request)
    {
        $this->payment->ipn($request);
    }

    /**
     * Validate permission for manual payment approval
     *
     * @param Request $request
     * @return Payment
     * @throws ErrorMessageException
     */
    private function validateManualPaymentApprovalPermission(Request $request): Payment
    {
        // Get the current authenticated user
        $currentUser = auth()->user();

        if (!$currentUser) {
            $this->logSecurityEvent('payment_approval_no_auth', $request);
            throw new ErrorMessageException('Authentication required for payment approval');
        }

        // Get the transaction ID from request
        $transactionId = $request->input('trans_id');

        if (!$transactionId) {
            $this->logSecurityEvent('payment_approval_no_trans_id', $request, $currentUser);
            throw new ErrorMessageException('Invalid payment transaction ID');
        }

        // Find the payment with user relationship
        $payment = Payment::with(['user.parent'])
            ->where('transaction_id', $transactionId)
            ->where('gateway', 'manual')
            ->first();

        if (!$payment) {
            $this->logSecurityEvent('payment_approval_not_found', $request, $currentUser, [
                'transaction_id' => $transactionId
            ]);
            throw new ErrorMessageException('Payment not found or not a manual payment');
        }

        // Check if payment is in pending status
        if ($payment->payment_status !== 'pending') {
            $this->logSecurityEvent('payment_approval_invalid_status', $request, $currentUser, [
                'payment_id' => $payment->id,
                'current_status' => $payment->payment_status
            ]);
            throw new ErrorMessageException('Payment is not in pending status');
        }

        // Validate hierarchical permission
        if (!$this->canApprovePayment($currentUser, $payment)) {
            $this->logSecurityEvent('payment_approval_permission_denied', $request, $currentUser, [
                'payment_id' => $payment->id,
                'payment_user_id' => $payment->user_id,
                'approver_role' => $currentUser->getRoleNames()->first()
            ]);
            throw new ErrorMessageException('You do not have permission to approve this payment');
        }

        return $payment;
    }

    /**
     * Log payment approval attempts for audit purposes
     */
    private function logPaymentApprovalAttempt(Payment $payment, $user, string $action)
    {
        Log::info('Payment approval attempt', [
            'action' => $action,
            'payment_id' => $payment->id,
            'transaction_id' => $payment->transaction_id,
            'payment_amount' => $payment->amount,
            'payment_user_id' => $payment->user_id,
            'payment_user_name' => $payment->user->name ?? 'Unknown',
            'approver_id' => $user->id,
            'approver_name' => $user->name,
            'approver_role' => $user->getRoleNames()->first(),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'timestamp' => now()
        ]);
    }

    /**
     * Log security events for monitoring
     */
    private function logSecurityEvent(string $event, Request $request, $user = null, array $extra = [])
    {
        Log::warning('Payment approval security event', array_merge([
            'event' => $event,
            'user_id' => $user->id ?? null,
            'user_name' => $user->name ?? 'Unknown',
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'request_data' => $request->all(),
            'timestamp' => now()
        ], $extra));
    }

    /**
     * Clear dashboard cache for all admin users after payment approval
     *
     * @return void
     */
    private function clearDashboardCache(): void
    {
        try {
            // Get all admin users (super-admin, master-reseller, reseller)
            $adminUsers = \App\Models\User::whereHas('roles', function($query) {
                $query->whereIn('name', ['super-admin', 'master-reseller', 'reseller']);
            })->get();

            // Clear dashboard cache for each admin user
            foreach ($adminUsers as $user) {
                $userRoles = $user->getRoleNames();
                if ($userRoles->isNotEmpty()) {
                    $roleKey = $userRoles->first();

                    // Clear all dashboard-related cache keys
                    $cacheKeys = [
                        'admin_dashboard_core_metrics_' . $user->id . '_' . $roleKey,
                        'admin_dashboard_metrics_' . $user->id . '_' . $roleKey,
                        'admin_aggregated_metrics_' . $user->id . '_' . $roleKey
                    ];

                    foreach ($cacheKeys as $cacheKey) {
                        Cache::forget($cacheKey);
                    }
                }
            }

            Log::info('Dashboard cache cleared after manual payment approval', [
                'cleared_for_users' => $adminUsers->count(),
                'timestamp' => now()
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to clear dashboard cache after payment approval', [
                'error' => $e->getMessage(),
                'timestamp' => now()
            ]);
        }
    }
}
