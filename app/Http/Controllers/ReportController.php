<?php

namespace App\Http\Controllers;

use App\Models\Message;
use Carbon\Carbon;
use Illuminate\Http\Request;

class ReportController extends Controller
{
    public function todayDetail(Request $request)
    {
        // Get current user for tenant filtering
        $user = auth()->user();

        $query = Message::select(
            'messages.id',
            'messages.message_id',
            'messages.phone_number',
            'messages.schedule_at',
            'messages.created_at',
            'messages.sms_count',
            'messages.sms_cost',
            'messages.sms_content',
            'messages.sms_type',
            'messages.batch_number',
            'messages.api_response',
            'messages.status',
            'senders.name AS sender_name',
            'senders.is_masking',
            'companies.name AS company_name',
            'users.name AS user_name'
        )
            ->selectRaw('(
                SELECT operator FROM coverages
                WHERE coverages.company_id = messages.company_id
                    AND coverages.prefix = LEFT(messages.phone_number, 5)
                LIMIT 1
            ) AS operator')
            ->leftJoin('senders', 'senders.id', 'messages.sender_id')
            ->leftJoin('companies', 'companies.id', 'messages.company_id')
            ->leftJoin('users', 'users.id', 'messages.user_id');

        // Apply tenant filtering based on user role
        if (!$user->hasRole('super-admin')) {
            // For non-super-admin users, filter by their company and child companies
            if ($user->company) {
                $companyIds = [$user->company->id];

                // If user is master-reseller or reseller, include child companies
                if ($user->hasAnyRole(['master-reseller', 'reseller'])) {
                    $childCompanies = \App\Models\Company::where('company_id', $user->company->id)->pluck('id')->toArray();
                    $companyIds = array_merge($companyIds, $childCompanies);
                }

                $query->whereIn('messages.company_id', $companyIds);
            } else {
                // If user has no company, return empty result
                $query->where('messages.id', -1);
            }
        }

        // Apply date filters
        $query->when($request->has('date_from') && $request->date_from, function ($q) use ($request) {
                return $q->whereDate('messages.created_at', '>=', $request->date_from);
            })
            ->when($request->has('date_to') && $request->date_to, function ($q) use ($request) {
                return $q->whereDate('messages.created_at', '<=', $request->date_to);
            })
            ->when(!$request->has('date_from') && !$request->has('date_to'), function ($q) {
                return $q->whereDate('messages.created_at', today());
            });

        // Apply user role filter
        if ($request->has('user_role') && $request->user_role && $request->user_role !== 'all') {
            $query->whereHas('user.roles', function ($q) use ($request) {
                $q->where('name', $request->user_role);
            });
        }

        // Apply company/account filter
        if ($request->has('company_id') && $request->company_id && $request->company_id !== 'all') {
            $query->where('messages.company_id', $request->company_id);
        }

        $query->orderBy('messages.id', 'DESC');

        return datatables()->of($query)
            ->addColumn('serial', function ($message) {
                static $counter = 0;
                return ++$counter;
            })
            ->editColumn('sent_time', function ($message) {
                $time = $message->schedule_at ?: $message->created_at;
                if (!$time) {
                    return '-';
                }
                try {
                    $carbonTime = is_string($time) ? \Carbon\Carbon::parse($time) : $time;
                    return $carbonTime->format('M d, Y h:i A');
                } catch (\Exception $e) {
                    return '-';
                }
            })
            ->addColumn('schedule_status', function ($message) {
                try {
                    if ($message->schedule_at) {
                        $scheduleTime = is_string($message->schedule_at) ? \Carbon\Carbon::parse($message->schedule_at) : $message->schedule_at;
                        if ($scheduleTime > now()) {
                            return '<span class="badge badge-warning">Scheduled</span>';
                        } else {
                            return '<span class="badge badge-info">Scheduled (Sent)</span>';
                        }
                    } else {
                        return '<span class="badge badge-success">Immediate</span>';
                    }
                } catch (\Exception $e) {
                    return '<span class="badge badge-secondary">Unknown</span>';
                }
            })
            ->addColumn('charge_per_sms', function ($message) {
                if ($message->sms_count > 0) {
                    $perSms = $message->sms_cost / $message->sms_count;
                    return number_format($perSms, 4) . ' BDT';
                }
                return '0.0000 BDT';
            })
            ->editColumn('sms_count', function ($message) {
                return $message->sms_count ?: 1;
            })
            ->addColumn('total_cost', function ($message) {
                return number_format($message->sms_cost, 4) . ' BDT';
            })
            ->addColumn('api_status', function ($message) {
                if ($message->status === 'success') {
                    return '<span class="badge badge-success">Success</span>';
                } elseif ($message->status === 'failed') {
                    return '<span class="badge badge-danger">Failed</span>';
                } elseif ($message->status === 'pending') {
                    return '<span class="badge badge-warning">Pending</span>';
                } else {
                    return '<span class="badge badge-secondary">' . ucfirst($message->status ?: 'Unknown') . '</span>';
                }
            })
            ->addColumn('view_details', function ($message) {
                return '<button type="button" class="btn btn-sm btn-primary view-details-btn"
                               data-message-id="' . $message->message_id . '"
                               data-bs-toggle="modal"
                               data-bs-target="#smsDetailsModal">
                            <i class="fas fa-eye"></i> View Details
                        </button>';
            })
            ->rawColumns(['schedule_status', 'api_status', 'view_details'])
            ->filter(function ($query) use ($request) {
                // Enhanced search functionality
                if ($request->has('search') && !empty($request->search['value'])) {
                    $searchValue = $request->search['value'];
                    $query->where(function ($q) use ($searchValue) {
                        $q->where('messages.batch_number', 'like', "%{$searchValue}%")
                          ->orWhere('messages.phone_number', 'like', "%{$searchValue}%")
                          ->orWhere('senders.name', 'like', "%{$searchValue}%")
                          ->orWhere('messages.sms_content', 'like', "%{$searchValue}%")
                          ->orWhere('companies.name', 'like', "%{$searchValue}%")
                          ->orWhere('users.name', 'like', "%{$searchValue}%")
                          ->orWhere('messages.status', 'like', "%{$searchValue}%");
                    });
                }
            })
            ->make(true);
    }

    public function getSmsDetails(Request $request, $messageId)
    {
        // Get current user for tenant filtering
        $user = auth()->user();

        $query = Message::with(['sender', 'company', 'user', 'server'])
            ->select(
                'messages.*',
                'senders.name AS sender_name',
                'senders.is_masking',
                'companies.name AS company_name',
                'users.name AS user_name',
                'servers.name AS server_name'
            )
            ->leftJoin('senders', 'senders.id', 'messages.sender_id')
            ->leftJoin('companies', 'companies.id', 'messages.company_id')
            ->leftJoin('users', 'users.id', 'messages.user_id')
            ->leftJoin('servers', 'servers.id', 'messages.server_id');

        // Apply tenant filtering based on user role
        if (!$user->hasRole('super-admin')) {
            // For non-super-admin users, filter by their company and child companies
            if ($user->company) {
                $companyIds = [$user->company->id];

                // If user is master-reseller or reseller, include child companies
                if ($user->hasAnyRole(['master-reseller', 'reseller'])) {
                    $childCompanies = \App\Models\Company::where('company_id', $user->company->id)->pluck('id')->toArray();
                    $companyIds = array_merge($companyIds, $childCompanies);
                }

                $query->whereIn('messages.company_id', $companyIds);
            } else {
                // If user has no company, return empty result
                $query->where('messages.id', -1);
            }
        }

        $message = $query->where('messages.message_id', $messageId)->first();

        if (!$message) {
            return response()->json(['error' => 'Message not found or access denied'], 404);
        }

        // Calculate cost breakdown
        $perSmsRate = $message->sms_count > 0 ? $message->sms_cost / $message->sms_count : 0;

        // Get operator info
        $operator = null;
        if ($message->phone_number) {
            $prefix = substr($message->phone_number, 0, 5);
            $coverage = \App\Models\Coverage::where('company_id', $message->company_id)
                ->where('prefix', $prefix)
                ->first();
            $operator = $coverage ? $coverage->operator : 'Unknown';
        }

        // Determine schedule status
        $scheduleStatus = 'Immediate';
        $scheduleStatusBadge = 'success';
        if ($message->schedule_at) {
            if ($message->schedule_at > now()) {
                $scheduleStatus = 'Scheduled';
                $scheduleStatusBadge = 'warning';
            } else {
                $scheduleStatus = 'Scheduled (Sent)';
                $scheduleStatusBadge = 'info';
            }
        }

        // Base details available to all users
        $details = [
            'message_id' => $message->message_id,
            'phone_number' => $message->phone_number,
            'sender_name' => $message->sender_name ?: 'Unknown',
            'sender_type' => $message->is_masking ? 'Masking' : 'Non-Masking',
            'sms_content' => $message->sms_content ?: '',
            'sms_type' => ucfirst($message->sms_type ?: 'text'),
            'sms_count' => $message->sms_count ?: 1,
            'per_sms_rate' => number_format($perSmsRate, 4),
            'total_cost' => number_format($message->sms_cost ?: 0, 4),
            'batch_number' => $message->batch_number ?: 'N/A',
            'operator' => $operator,
            'status' => ucfirst($message->status ?: 'Unknown'),
            'schedule_status' => $scheduleStatus,
            'schedule_status_badge' => $scheduleStatusBadge,
            'created_at' => $message->created_at ? \Carbon\Carbon::parse($message->created_at)->format('M d, Y h:i:s A') : '-',
            'schedule_at' => $message->schedule_at ? \Carbon\Carbon::parse($message->schedule_at)->format('M d, Y h:i:s A') : null,
            'api_response' => $message->api_response ? json_decode($message->api_response, true) : null,
        ];

        // Role-based information display
        $userRole = $user->getRoleNames()->first(); // Get primary role

        // Server name - only visible to super admin
        if ($user->hasRole('super-admin')) {
            $details['server_name'] = $message->server_name ?: 'Unknown';
            $details['show_server'] = true;
        } else {
            $details['server_name'] = 'Hidden';
            $details['show_server'] = false;
        }

        // Company and user information - visible to admin roles only
        if ($user->hasAnyRole(['super-admin', 'master-reseller', 'reseller'])) {
            $details['company_name'] = $message->company_name ?: 'Unknown';
            $details['user_name'] = $message->user_name ?: 'Unknown';
            $details['show_admin_info'] = true;
        } else {
            $details['company_name'] = 'Hidden';
            $details['user_name'] = 'Hidden';
            $details['show_admin_info'] = false;
        }

        // Add user role for frontend conditional display
        $details['user_role'] = $userRole;
        $details['is_super_admin'] = $user->hasRole('super-admin');
        $details['is_admin_role'] = $user->hasAnyRole(['super-admin', 'master-reseller', 'reseller']);

        return response()->json($details);
    }

    public function exportDlr(Request $request)
    {
        try {
            // Get current user for tenant filtering
            $user = auth()->user();

            if (!$user) {
                return response()->json(['error' => 'User not authenticated'], 401);
            }

            $query = Message::select(
                'messages.message_id',
                'messages.phone_number',
                'messages.schedule_at',
                'messages.created_at',
                'messages.sms_count',
                'messages.sms_cost',
                'messages.sms_content',
                'messages.sms_type',
                'messages.batch_number',
                'messages.status',
                'senders.name AS sender_name',
                'senders.is_masking',
                'companies.name AS company_name',
                'users.name AS user_name'
            )
                ->selectRaw('(
                    SELECT operator FROM coverages
                    WHERE coverages.company_id = messages.company_id
                        AND coverages.prefix = LEFT(messages.phone_number, 5)
                    LIMIT 1
                ) AS operator')
                ->leftJoin('senders', 'senders.id', 'messages.sender_id')
                ->leftJoin('companies', 'companies.id', 'messages.company_id')
                ->leftJoin('users', 'users.id', 'messages.user_id');

            // Apply tenant filtering based on user role
            if (!$user->hasRole('super-admin')) {
                // For non-super-admin users, filter by their company and child companies
                if ($user->company) {
                    $companyIds = [$user->company->id];

                    // If user is master-reseller or reseller, include child companies
                    if ($user->hasAnyRole(['master-reseller', 'reseller'])) {
                        $childCompanies = \App\Models\Company::where('company_id', $user->company->id)->pluck('id')->toArray();
                        $companyIds = array_merge($companyIds, $childCompanies);
                    }

                    $query->whereIn('messages.company_id', $companyIds);
                } else {
                    // If user has no company, return empty result
                    $query->where('messages.id', -1);
                }
            }

        // Apply date filters
        $query->when($request->has('date_from') && $request->date_from, function ($q) use ($request) {
                return $q->whereDate('messages.created_at', '>=', $request->date_from);
            })
            ->when($request->has('date_to') && $request->date_to, function ($q) use ($request) {
                return $q->whereDate('messages.created_at', '<=', $request->date_to);
            })
            ->when(!$request->has('date_from') && !$request->has('date_to'), function ($q) {
                return $q->whereDate('messages.created_at', today());
            });

        // Apply user role filter
        if ($request->has('user_role') && $request->user_role && $request->user_role !== 'all') {
            $query->whereHas('user.roles', function ($q) use ($request) {
                $q->where('name', $request->user_role);
            });
        }

        // Apply company/account filter
        if ($request->has('company_id') && $request->company_id && $request->company_id !== 'all') {
            $query->where('messages.company_id', $request->company_id);
        }

        // Apply search filter if provided
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('messages.phone_number', 'like', "%{$search}%")
                  ->orWhere('senders.name', 'like', "%{$search}%")
                  ->orWhere('companies.name', 'like', "%{$search}%")
                  ->orWhere('users.name', 'like', "%{$search}%")
                  ->orWhere('messages.batch_number', 'like', "%{$search}%")
                  ->orWhere('messages.sms_content', 'like', "%{$search}%");
            });
        }

        $messages = $query->orderBy('messages.id', 'DESC')->get();

        // Prepare CSV data
        $csvData = [];

        // CSV Headers
        $headers = ['SL', 'Message ID', 'Sent Time', 'Sender'];
        if ($user->hasAnyRole(['super-admin', 'master-reseller', 'reseller'])) {
            $headers[] = 'Company';
        }
        $headers = array_merge($headers, [
            'Schedule Status', 'Charge/SMS', 'Total Sent', 'Total Cost', 'API Status',
            'Phone Number', 'SMS Type', 'Operator', 'Batch Number'
        ]);
        $csvData[] = $headers;

        // CSV Data
        foreach ($messages as $index => $message) {
            // Fix date formatting with proper type checking
            $sentTime = $message->schedule_at ?: $message->created_at;
            $formattedTime = '-';
            if ($sentTime) {
                try {
                    $carbonTime = is_string($sentTime) ? \Carbon\Carbon::parse($sentTime) : $sentTime;
                    $formattedTime = $carbonTime->format('M d, Y h:i A');
                } catch (\Exception $e) {
                    $formattedTime = '-';
                }
            }

            // Fix schedule status with proper type checking
            $scheduleStatus = 'Immediate';
            if ($message->schedule_at) {
                try {
                    $scheduleTime = is_string($message->schedule_at) ? \Carbon\Carbon::parse($message->schedule_at) : $message->schedule_at;
                    if ($scheduleTime > now()) {
                        $scheduleStatus = 'Scheduled';
                    } else {
                        $scheduleStatus = 'Scheduled (Sent)';
                    }
                } catch (\Exception $e) {
                    $scheduleStatus = 'Unknown';
                }
            }

            $chargePerSms = $message->sms_count > 0 ? number_format($message->sms_cost / $message->sms_count, 4) : '0.0000';
            $totalCost = number_format($message->sms_cost, 4);
            $apiStatus = ucfirst($message->status ?: 'Unknown');

            $row = [
                $index + 1,
                $message->message_id ?: 'N/A',
                $formattedTime,
                $message->sender_name ?: 'Unknown'
            ];

            if ($user->hasAnyRole(['super-admin', 'master-reseller', 'reseller'])) {
                $row[] = $message->company_name ?: 'Unknown';
            }

            $row = array_merge($row, [
                $scheduleStatus,
                $chargePerSms . ' BDT',
                $message->sms_count ?: 1,
                $totalCost . ' BDT',
                $apiStatus,
                $message->phone_number,
                ucfirst($message->sms_type),
                $message->operator ?: 'Unknown',
                $message->batch_number
            ]);

            $csvData[] = $row;
        }

        // Generate filename
        $dateRange = '';
        if ($request->date_from && $request->date_to) {
            $dateRange = $request->date_from . '_to_' . $request->date_to;
        } elseif ($request->date_from) {
            $dateRange = 'from_' . $request->date_from;
        } elseif ($request->date_to) {
            $dateRange = 'until_' . $request->date_to;
        } else {
            $dateRange = date('Y-m-d');
        }

        $filename = 'sms_delivery_report_' . $dateRange . '.csv';

        // Create CSV response
        $callback = function() use ($csvData) {
            $file = fopen('php://output', 'w');
            foreach ($csvData as $row) {
                fputcsv($file, $row);
            }
            fclose($file);
        };

        return response()->stream($callback, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ]);

        } catch (\Exception $e) {
            // Log the error for debugging
            \Log::error('Export DLR Error: ' . $e->getMessage(), [
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);

            // Return a user-friendly error response
            return response()->json([
                'error' => 'Export failed: ' . $e->getMessage()
            ], 500);
        }
    }

    public function getFilterOptions(Request $request)
    {
        $user = auth()->user();

        // Get available roles based on user permissions
        $availableRoles = [];
        if ($user->hasRole('super-admin')) {
            $availableRoles = [
                'super-admin' => 'Super Admin',
                'master-reseller' => 'Master Reseller',
                'reseller' => 'Reseller',
                'client' => 'Client'
            ];
        } elseif ($user->hasRole('master-reseller')) {
            $availableRoles = [
                'master-reseller' => 'Master Reseller',
                'reseller' => 'Reseller',
                'client' => 'Client'
            ];
        } elseif ($user->hasRole('reseller')) {
            $availableRoles = [
                'reseller' => 'Reseller',
                'client' => 'Client'
            ];
        } else {
            $availableRoles = [
                'client' => 'Client'
            ];
        }

        // Get available companies based on user permissions
        $availableCompanies = [];
        if ($user->hasRole('super-admin')) {
            // Super admin can see all companies
            $companies = \App\Models\Company::select('id', 'name')->orderBy('name')->get();
            foreach ($companies as $company) {
                $availableCompanies[$company->id] = $company->name;
            }
        } elseif ($user->hasAnyRole(['master-reseller', 'reseller'])) {
            // Master reseller and reseller can see their own company and child companies
            if ($user->company) {
                $companyIds = [$user->company->id];

                // Add child companies
                $childCompanies = \App\Models\Company::where('company_id', $user->company->id)->pluck('id')->toArray();
                $companyIds = array_merge($companyIds, $childCompanies);

                $companies = \App\Models\Company::whereIn('id', $companyIds)
                    ->select('id', 'name')
                    ->orderBy('name')
                    ->get();

                foreach ($companies as $company) {
                    $availableCompanies[$company->id] = $company->name;
                }
            }
        } else {
            // Client can only see their own company
            if ($user->company) {
                $availableCompanies[$user->company->id] = $user->company->name;
            }
        }

        return response()->json([
            'roles' => $availableRoles,
            'companies' => $availableCompanies
        ]);
    }

    public function summaryLog(Request $request)
    {
        // Get current user for tenant filtering
        $user = auth()->user();

        $query = Message::select(
            'companies.id as company_id',
            'companies.name as company_name',
            \DB::raw('COUNT(messages.id) as total_sms_sent'),
            \DB::raw('SUM(messages.sms_cost) as total_amount_deducted')
        )
        ->join('companies', 'messages.company_id', '=', 'companies.id')
        ->join('users', 'messages.user_id', '=', 'users.id');

        // Apply tenant filtering based on user role (same logic as DLR report)
        if (!$user->hasRole('super-admin')) {
            // For non-super-admin users, filter by their company and child companies
            if ($user->company) {
                $companyIds = [$user->company->id];

                // If user is master-reseller or reseller, include child companies
                if ($user->hasAnyRole(['master-reseller', 'reseller'])) {
                    $childCompanies = \App\Models\Company::where('company_id', $user->company->id)->pluck('id')->toArray();
                    $companyIds = array_merge($companyIds, $childCompanies);
                }

                $query->whereIn('messages.company_id', $companyIds);
            } else {
                // If user has no company, return empty result
                $query->where('messages.id', -1);
            }
        }

        // Apply date range filter
        if ($request->has('date_from') && $request->date_from) {
            $query->whereDate('messages.created_at', '>=', $request->date_from);
        }
        if ($request->has('date_to') && $request->date_to) {
            $query->whereDate('messages.created_at', '<=', $request->date_to);
        }

        // Apply role filter
        if ($request->has('user_role') && $request->user_role && $request->user_role !== 'all') {
            $query->whereHas('user', function($q) use ($request) {
                $q->whereHas('roles', function($roleQuery) use ($request) {
                    $roleQuery->where('name', $request->user_role);
                });
            });
        }

        // Apply company/account filter
        if ($request->has('company_id') && $request->company_id && $request->company_id !== 'all') {
            $query->where('messages.company_id', $request->company_id);
        }

        $query->groupBy('companies.id', 'companies.name')
              ->orderBy('companies.name', 'ASC');

        return datatables()->of($query)
            ->addColumn('serial', function ($summary) {
                static $counter = 0;
                return ++$counter;
            })
            ->editColumn('total_sms_sent', function ($summary) {
                return number_format($summary->total_sms_sent);
            })
            ->editColumn('total_amount_deducted', function ($summary) {
                return number_format($summary->total_amount_deducted, 2);
            })
            ->rawColumns(['serial'])
            ->make(true);
    }

    public function exportSummaryLog(Request $request)
    {
        try {
            // Get current user for tenant filtering
            $user = auth()->user();

            if (!$user) {
                return response()->json(['error' => 'User not authenticated'], 401);
            }

            $query = Message::select(
                'companies.id as company_id',
                'companies.name as company_name',
                \DB::raw('COUNT(messages.id) as total_sms_sent'),
                \DB::raw('SUM(messages.sms_cost) as total_amount_deducted')
            )
            ->join('companies', 'messages.company_id', '=', 'companies.id')
            ->join('users', 'messages.user_id', '=', 'users.id');

            // Apply tenant filtering based on user role (same logic as DLR report)
            if (!$user->hasRole('super-admin')) {
                // For non-super-admin users, filter by their company and child companies
                if ($user->company) {
                    $companyIds = [$user->company->id];

                    // If user is master-reseller or reseller, include child companies
                    if ($user->hasAnyRole(['master-reseller', 'reseller'])) {
                        $childCompanies = \App\Models\Company::where('company_id', $user->company->id)->pluck('id')->toArray();
                        $companyIds = array_merge($companyIds, $childCompanies);
                    }

                    $query->whereIn('messages.company_id', $companyIds);
                } else {
                    // If user has no company, return empty result
                    $query->where('messages.id', -1);
                }
            }

            // Apply date range filter
            if ($request->has('date_from') && $request->date_from) {
                $query->whereDate('messages.created_at', '>=', $request->date_from);
            }
            if ($request->has('date_to') && $request->date_to) {
                $query->whereDate('messages.created_at', '<=', $request->date_to);
            }

            // Apply role filter
            if ($request->has('user_role') && $request->user_role && $request->user_role !== 'all') {
                $query->whereHas('user', function($q) use ($request) {
                    $q->whereHas('roles', function($roleQuery) use ($request) {
                        $roleQuery->where('name', $request->user_role);
                    });
                });
            }

            // Apply company/account filter
            if ($request->has('company_id') && $request->company_id && $request->company_id !== 'all') {
                $query->where('messages.company_id', $request->company_id);
            }

            $summaries = $query->groupBy('companies.id', 'companies.name')
                              ->orderBy('companies.name', 'ASC')
                              ->get();

            // Prepare CSV data
            $csvData = [];

            // CSV Headers
            $headers = ['Company Name', 'Total SMS Sent', 'Total Amount Deducted'];
            $csvData[] = $headers;

            // Add data rows
            foreach ($summaries as $summary) {
                $csvData[] = [
                    $summary->company_name,
                    number_format($summary->total_sms_sent),
                    number_format($summary->total_amount_deducted, 2)
                ];
            }

            // Generate filename with date range
            $dateRange = '';
            if ($request->date_from && $request->date_to) {
                $dateRange = $request->date_from . '_to_' . $request->date_to;
            } elseif ($request->date_from) {
                $dateRange = 'from_' . $request->date_from;
            } elseif ($request->date_to) {
                $dateRange = 'until_' . $request->date_to;
            } else {
                $dateRange = date('Y-m-d');
            }

            $filename = 'summary_log_report_' . $dateRange . '.csv';

            // Create CSV response
            $callback = function() use ($csvData) {
                $file = fopen('php://output', 'w');
                foreach ($csvData as $row) {
                    fputcsv($file, $row);
                }
                fclose($file);
            };

            return response()->stream($callback, 200, [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            ]);

        } catch (\Exception $e) {
            // Log the error for debugging
            \Log::error('Export Summary Log Error: ' . $e->getMessage(), [
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);

            // Return a user-friendly error response
            return response()->json([
                'error' => 'Export failed: ' . $e->getMessage()
            ], 500);
        }
    }
}
