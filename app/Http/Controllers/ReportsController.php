<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class ReportsController extends Controller
{
    /**
     * Reports view dlsr methods
     * $type: todays-dlr, todays-details, archived-dlr
     *
     * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View|\Illuminate\Foundation\Application
     */
    public function viewDlr(Request $request, $type)
    {
        // Your logic here
        return view('reports.view-dlr', compact('type'));
    }

    /**
     * Reports view dlsr methods
     * $type: todays-dlr, archived-dlr
     *
     * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View|\Illuminate\Foundation\Application
     */
    public function campaignDlr(Request $request, $type)
    {
        // Your logic here
        return view('reports.campaign-dlr', compact('type'));
    }

    // Statistics view methods
    public function summaryLogs()
    {
        // Your logic here
        return view('reports.summary-log');
    }

    public function detailsLogs()
    {
        // Your logic here
        return view('reports.details-logs');
    }

    public function dayWiseLogs()
    {
        // Your logic here
        return view('reports.day-wise-logs');
    }

    public function apiSmsPurpose()
    {
        // Your logic here
        return view('reports.api-sms-purpose');
    }

    public function scheduledSms()
    {
        // Your logic here
        return view('reports.scheduled-sms');
    }

    public function transactions()
    {
        // Your logic here
        return view('reports.transactions');
    }

    // Usages view methods
    public function todayUsages()
    {
        // Your logic here
        return view('reports.usages-today');
    }

    public function last7DaysUsages()
    {
        // Your logic here
        return view('reports.usages-last-7-days');
    }

    public function thisMonthUsages()
    {
        // Your logic here
        return view('reports.usages-this-month');
    }
}
