<?php

namespace App\Http\Controllers;

use App\Helpers\Traits\HierarchicalPermissions;
use App\Http\Requests\StoreRechargeRequest;
use App\Http\Requests\UpdateRechargeRequest;
use App\Models\Payment;
use App\Models\Recharge;
use App\Models\Transaction;
use App\Models\User;
use App\Tenant\Scopes\TenantScope;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Yajra\DataTables\Exceptions\Exception;

class RechargeController extends Controller
{
    use HierarchicalPermissions;
    /**
     * @throws Exception
     */
    public function browse(Request $request)
    {
        return datatables()->of(
            Recharge::with('payment')->orderBy('id', 'desc')
        )
            ->editColumn('recharge_date', function (Recharge $recharge) {
                if (! isset($recharge->payment)) {
                    return '';
                }
                $formattedDate = Carbon::parse($recharge->payment->created_at)->format('M d, Y h:i A');
                $humanReadable = Carbon::parse($recharge->payment->created_at)->diffForHumans();

                return "$formattedDate ($humanReadable)";
            })
            ->addColumn('action', function (Recharge $recharge) {
                return '<a href="'.route('recharges.show', $recharge->id).'" class="btn btn-sm btn-light btn-active-light-primary">
                    <i class="ki-duotone ki-eye fs-5">
                        <span class="path1"></span>
                        <span class="path2"></span>
                        <span class="path3"></span>
                    </i>
                    View Details
                </a>';
            })
            ->rawColumns(['action'])
            ->toJson();
    }

    /**
     * @throws Exception
     */
    public function list(Request $request)
    {
        return datatables()->of(
            Payment::withoutGlobalScope(TenantScope::class)
                ->with(['company', 'user.parent'])
                ->select('payments.*')
                ->where('payments.gateway', 'manual')
                ->orderBy('id', 'desc')
        )
            ->addColumn('recharge_date', function (Payment $payment) {
                if (empty($payment->created_at)) {
                    return '';
                }
                $formattedDate = Carbon::parse($payment->created_at)->format('M d, Y h:i A');
                $humanReadable = Carbon::parse($payment->created_at)->diffForHumans();

                return "$formattedDate ($humanReadable)";
            })
            ->editColumn('remarks', function (Payment $payment) {
                return $payment->remarks;
            })
            ->addColumn('recharge_amount', function (Payment $payment) {
                return $payment->amount;
            })
            ->addColumn('action', function (Payment $payment) {
                $action = '<a href="'.route('recharges.show', $payment->recharge->id ?? 0).'" class="btn btn-sm btn-light btn-active-light-primary me-2">
                    <i class="ki-duotone ki-eye fs-5">
                        <span class="path1"></span>
                        <span class="path2"></span>
                        <span class="path3"></span>
                    </i>
                    View Details
                </a>';

                // Only show approval button if payment is pending and user has permission
                if ($payment->payment_status == 'pending' && $this->canApprovePayment(auth()->user(), $payment)) {
                    $action .= ' <a href="'.route('payment.success', [
                        'trans_id' => $payment->transaction_id,
                        'payment_method' => 'manual',
                        'route' => 'recharges',
                    ]).'" class="btn btn-primary btn-sm" >Approved</a>';
                }

                return $action;
            })
            ->rawColumns(['action'])
            ->toJson();
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreRechargeRequest $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(Recharge $recharge)
    {
        // Load additional relationships (basic ones are already loaded by route model binding)
        $recharge->load([
            'user.roles',
            'user.parent'
        ]);

        // Permission checking is now handled by route model binding

        // Get related transactions for this recharge
        $relatedTransactions = collect();
        if ($recharge->company_id && $recharge->recharge_date) {
            // Ensure recharge_date is a Carbon instance and add null checking
            $rechargeDate = $recharge->recharge_date;
            $endDate = $rechargeDate->copy()->addMinutes(5); // Use copy() to avoid mutating original

            $relatedTransactions = Transaction::with(['user', 'company'])
                ->where('company_id', $recharge->company_id)
                ->where('date', '>=', $rechargeDate)
                ->where('date', '<=', $endDate) // Within 5 minutes of recharge
                ->orderBy('date', 'desc')
                ->get();
        }

        return view('recharges.show', compact('recharge', 'relatedTransactions'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Recharge $recharge)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateRechargeRequest $request, Recharge $recharge)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Recharge $recharge)
    {
        //
    }

    /**
     * Check if current user can view the recharge
     */
    private function canViewRecharge(User $currentUser, Recharge $recharge): bool
    {
        // Super admin can view any recharge
        if ($currentUser->hasRole('super-admin')) {
            return true;
        }

        // Master reseller can view recharges in their hierarchy
        if ($currentUser->hasRole('master-reseller')) {
            return $this->isUserInHierarchy($currentUser, $recharge->user);
        }

        // Reseller can view recharges for direct clients only
        if ($currentUser->hasRole('reseller')) {
            return $recharge->user->parent_id === $currentUser->id;
        }

        // Users can view their own recharges
        if ($currentUser->id === $recharge->user_id) {
            return true;
        }

        // Clients cannot view other users' recharges
        return false;
    }


}
