<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use Barryvdh\DomPDF\Facade\Pdf;

class DevelopersPdfController extends Controller
{
    public function downloadPdf()
    {
        $company = app(\App\Tenant\Manager::class)->getTenant();

        // Fallback if no tenant is found
        if (!$company) {
            $company = (object) [
                'api_key' => null,
                'name' => 'Default Company'
            ];
        }

        $pdf = Pdf::loadView('others.developers-pdf', compact('company'));

        return $pdf->download('api-documentation.pdf');
    }
}
