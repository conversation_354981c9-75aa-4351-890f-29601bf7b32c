<?php

namespace App\Http\Controllers;

use App\Exceptions\ErrorMessageException;
use App\Helpers\Common;
use App\Helpers\Traits\HierarchicalPermissions;
use App\Http\Requests\StoreUserRequest;
use App\Http\Requests\UpdateUserRequest;
use App\Http\Services\PaymentService;
use App\Http\Services\UserService;
use App\Models\Coverage;
use App\Models\Recharge;
use App\Models\User;
use App\Tenant\Scopes\TenantScope;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Rappasoft\LaravelAuthenticationLog\Models\AuthenticationLog;
use Spatie\Permission\Models\Role;

class UserController extends Controller
{
    use HierarchicalPermissions;

    private $user;

    private $payment;

    public function __construct(UserService $user, PaymentService $payment)
    {
        $this->user = $user;
        $this->payment = $payment;

        // User-related middleware
        $this->middleware(['permission:user-list|user-create|user-edit|user-delete'], ['only' => ['index', 'show']]);
        $this->middleware(['permission:user-create'], ['only' => ['create', 'store']]);
        $this->middleware(['permission:user-edit'], ['only' => ['edit', 'update', 'updateStatus']]);
        $this->middleware(['permission:user-delete'], ['only' => ['destroy']]);

        // Coverage-related middleware
        $this->middleware(['permission:coverage-list'], ['only' => ['getCoverageData']]);
        $this->middleware(['permission:coverage-create'], ['only' => ['storeCoverage']]);
        $this->middleware(['permission:coverage-edit'], ['only' => ['getCoverage', 'updateCoverage']]);
        $this->middleware(['permission:coverage-delete'], ['only' => ['deleteCoverage']]);
    }

    /**
     * Browse All user using Data Table
     *
     * @throws \Yajra\DataTables\Exceptions\Exception
     * @throws \Exception
     */
    public function browse(Request $request): \Illuminate\Http\JsonResponse
    {
        $user = auth()->user();

        return datatables()
            ->of(
                User::has('company')
                    ->with('roles', 'company', 'parent')
                    ->when(! $user->hasRole('super-admin'), function ($q) use ($user) {
                        $q->where('parent_id', $user->id);
                    })
                    ->when($user->hasRole('super-admin'), function ($q) use ($user) {
                        $q->where('id', '!=', $user->id);
                    })
                    ->whereHas('roles', function ($query) use ($request) {
                        $query->when($request->role_id, function ($q) use ($request) {
                            return $q->where('id', $request->role_id);
                        });
                    })
                    ->orderBy('id', 'ASC')
            )
            ->addColumn('user_type', function (User $user) {
                $name = $user->roles->first()->name ?? '-';

                return ucfirst(str_replace('-', ' ', $name));
            })
            ->addColumn('current_balance', function (User $user) {
                $balance = $user->company ? $user->company->current_balance : 0;
                return '$' . number_format($balance, 2);
            })
            ->addColumn('balance_expired', function (User $user) {
                if ($user->company && $user->company->balance_expired) {
                    return Carbon::parse($user->company->balance_expired)->format('F j, Y, g:i a');
                }
                return 'N/A';
            })
            ->addColumn('parent_account', function (User $user) {
                // Only show parent account for super-admin users
                $currentUser = auth()->user();
                if (!$currentUser->hasRole('super-admin')) {
                    return '';
                }

                if ($user->parent_id === null) {
                    return '<span class="badge badge-light-primary">Root Account</span>';
                }

                if ($user->parent && $user->parent->name) {
                    return '<span class="text-gray-800 fw-bold">' . $user->parent->name . '</span>';
                }

                return '<span class="text-muted">N/A</span>';
            })
            ->addColumn('action', function (User $user) {
                $currentUser = auth()->user();

                // Start building the action dropdown with improved styling
                $action = '<div class="dropdown">
                    <button class="btn btn-light btn-active-light-primary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="ki-duotone ki-setting-3 fs-6 me-1">
                            <span class="path1"></span>
                            <span class="path2"></span>
                            <span class="path3"></span>
                            <span class="path4"></span>
                            <span class="path5"></span>
                        </i>
                        Actions
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end min-w-125px">';

                // Credit/Debit option
                $action .= '<li>
                    <button class="dropdown-item px-3 py-2" data-bs-toggle="modal" data-bs-target="#kt_modal_add_balance" data-user="'.$user->id.'">
                        <i class="ki-duotone ki-wallet fs-6 me-2 text-primary">
                            <span class="path1"></span>
                            <span class="path2"></span>
                            <span class="path3"></span>
                            <span class="path4"></span>
                        </i>
                        Credit/Debit
                    </button>
                </li>';

                // View option
                $action .= '<li>
                    <a class="dropdown-item px-3 py-2" href="'.route('users.show', $user->id).'">
                        <i class="ki-duotone ki-eye fs-6 me-2 text-success">
                            <span class="path1"></span>
                            <span class="path2"></span>
                            <span class="path3"></span>
                        </i>
                        View Details
                    </a>
                </li>';

                // User verification option - for users who haven't verified their email
                if (!$user->hasVerifiedEmail() && $this->canVerifyUser($currentUser, $user)) {
                    $action .= '<li><hr class="dropdown-divider my-2"></li>';
                    $action .= '<li>
                        <form method="POST" action="'.route('users.verify', $user->id).'" style="display: inline; width: 100%;" class="verify-user-form">
                            '.csrf_field().'
                            <button type="submit" class="dropdown-item px-3 py-2 text-primary verify-user-btn" data-user-name="'.$user->name.'">
                                <i class="ki-duotone ki-check-circle fs-6 me-2 text-primary">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                </i>
                                Verify User
                            </button>
                        </form>
                    </li>';
                }

                // Impersonation option - only for super-admin and not for self or other super-admins
                if ($currentUser->hasRole('super-admin') &&
                    $user->id !== $currentUser->id &&
                    !$user->hasRole('super-admin')) {

                    $action .= '<li><hr class="dropdown-divider my-2"></li>';
                    $action .= '<li>
                        <form method="POST" action="'.route('impersonate.start', $user->id).'" style="display: inline; width: 100%;">
                            '.csrf_field().'
                            <button type="submit" class="dropdown-item px-3 py-2 text-warning impersonate-btn" data-user-name="'.$user->name.'">
                                <i class="ki-duotone ki-user-tick fs-6 me-2 text-warning">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                    <span class="path3"></span>
                                </i>
                                Login As
                            </button>
                        </form>
                    </li>';
                }

                $action .= '</ul></div>';

                return $action;
            })
            ->rawColumns(['avatar', 'action', 'parent_account'])
            ->toJson();
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $user = Auth::user();
        $users_by_role = Role::with('users')->where('name', '!=', 'super-admin')->get();
        // Get the list of roles
        $roles = Role::where('name', '!=', 'super-admin')->pluck('name', 'id');
        // Filter the roles based on the current role
        $roles = collect($roles)->filter(function ($name, $id) use ($user) {
            return $id > $user->roles->first()->id;
        })->map(function ($name) {
            return ucfirst(str_replace('-', ' ', $name));
        })->toArray();

        return view('users.index', compact('roles', 'users_by_role'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreUserRequest $request)
    {
        $this->user->store($request);

        return response([
            'success' => true,
            'message' => 'User has been created successfully.',
        ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(User $user)
    {
        $user = User::with(['roles', 'company', 'parent', 'children'])
            ->whereId($user->id)
            ->first();

        $logs = AuthenticationLog::where('authenticatable_id', '=', $user->id)
            ->orderBy('login_at', 'DESC')
            ->get();

        // Get coverage data for the user's company
        $coverages = collect();
        if ($user->company_id) {
            $coverages = Coverage::with('company')
                ->where('company_id', $user->company_id)
                ->orderBy('prefix')
                ->get();
        }

        // Get operators and prefixes for the coverage form
        $operators = Common::operators();
        $prefixes = Common::prefixes();

        // Get current user for role filtering
        $currentUser = Auth::user();
        $roles = Role::where('name', '!=', 'super-admin')->pluck('name', 'id');
        // Filter the roles based on the current user's role (who is doing the editing)
        $roles = collect($roles)->filter(function ($name, $id) use ($currentUser) {
            return $id > $currentUser->roles->first()->id;
        })->map(function ($name) {
            return ucfirst(str_replace('-', ' ', $name));
        })->toArray();

        // Get current user for permission checks
        $currentUser = Auth::user();

        // Check if current user can manage this user's status
        $canManageStatus = $this->canManageUserStatus($currentUser, $user);

        // Check if current user can edit coverage for this user
        $canEditCoverage = $this->canEditCoverage($currentUser, $user);

        // Check if current user can edit this user
        $canEditUser = $this->canEditUser($currentUser, $user);

        return view('users.view', compact(
            'user',
            'logs',
            'roles',
            'coverages',
            'operators',
            'prefixes',
            'currentUser',
            'canManageStatus',
            'canEditCoverage',
            'canEditUser'
        ));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(User $user)
    {
        $this->user->edit($user);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateUserRequest $request, User $user)
    {
        try {
            $currentUser = Auth::user();

            // Get validated data
            $validatedData = $request->validated();

            // Log the update attempt
            \Log::info('User update attempt', [
                'target_user_id' => $user->id,
                'target_user_name' => $user->name,
                'updated_by' => $currentUser->id,
                'updated_by_name' => $currentUser->name,
                'updated_fields' => array_keys($validatedData),
            ]);

            // Store old values for logging
            $oldValues = $user->only(['name', 'email', 'phone', 'status', 'remarks']);

            // Separate user and company data
            $userData = collect($validatedData)->except(['minimum_recharge_amount'])->toArray();
            $companyData = collect($validatedData)->only(['minimum_recharge_amount'])->filter()->toArray();

            // Update user data
            $user->update($userData);

            // Update company data if provided and user has a company
            if (!empty($companyData) && $user->company) {
                $user->company->update($companyData);

                \Log::info('User company data updated', [
                    'user_id' => $user->id,
                    'company_id' => $user->company->id,
                    'updated_fields' => array_keys($companyData),
                    'updated_by' => $currentUser->id,
                ]);
            }

            // Handle role change if provided and authorized
            if (isset($validatedData['role_id']) && $currentUser->hasAnyRole(['super-admin', 'master-reseller'])) {
                $oldRole = $user->roles->first();
                $newRole = \App\Models\Role::find($validatedData['role_id']);

                if ($newRole && (!$oldRole || $oldRole->id !== $newRole->id)) {
                    // Remove old roles and assign new role
                    $user->syncRoles([$newRole->name]);

                    \Log::info('User role updated', [
                        'user_id' => $user->id,
                        'old_role' => $oldRole ? $oldRole->name : 'none',
                        'new_role' => $newRole->name,
                        'updated_by' => $currentUser->id,
                    ]);
                }
            }

            // Log successful update
            \Log::info('User updated successfully', [
                'user_id' => $user->id,
                'user_name' => $user->name,
                'old_values' => $oldValues,
                'new_values' => $user->only(['name', 'email', 'phone', 'status', 'remarks']),
                'updated_by' => $currentUser->id,
                'updated_by_name' => $currentUser->name,
            ]);

            // Return JSON response for AJAX requests
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'User updated successfully!',
                    'user' => $user->fresh(['roles', 'company', 'parent']),
                ]);
            }

            return redirect()->route('users.show', $user)->with([
                'message' => 'User updated successfully!',
                'status' => 'success',
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            \Log::error('User update validation failed', [
                'user_id' => $user->id,
                'validation_errors' => $e->errors(),
                'request_data' => $request->all(),
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed.',
                    'errors' => $e->errors(),
                ], 422);
            }

            return redirect()->back()->withErrors($e->errors())->withInput();

        } catch (\Exception $e) {
            \Log::error('User update failed', [
                'user_id' => $user->id,
                'error_message' => $e->getMessage(),
                'error_trace' => $e->getTraceAsString(),
                'request_data' => $request->all(),
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'An error occurred while updating the user.',
                ], 500);
            }

            return redirect()->back()->with([
                'message' => 'An error occurred while updating the user.',
                'status' => 'error',
            ]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(User $user)
    {
        $this->user->delete($user);

        return response([
            'success' => true,
            'message' => 'User has been deleted successfully.',
        ]);
    }

    public function getUser(User $user)
    {
        // Load the user with company and role relationships
        $user->load(['company', 'roles']);

        return response([
            'success' => true,
            'data' => $user,
        ]);
    }



    /**
     * Get coverage data for DataTables in user view
     */
    public function getCoverageData(User $user, Request $request)
    {
        if (!$user->company_id) {
            return datatables()->of(collect())->toJson();
        }

        return datatables()->of(
            Coverage::withoutGlobalScope(TenantScope::class)
                ->with('company')
                ->where('company_id', $user->company_id)
                ->orderBy('prefix')
        )
            ->addColumn('company_name', function (Coverage $coverage) {
                return $coverage->company->name ?? 'N/A';
            })
            ->addColumn('masking_price_formatted', function (Coverage $coverage) {
                return '$' . number_format($coverage->masking_price, 2);
            })
            ->addColumn('non_masking_price_formatted', function (Coverage $coverage) {
                return '$' . number_format($coverage->non_masking_price, 2);
            })
            ->addColumn('status_badge', function (Coverage $coverage) {
                $badgeClass = $coverage->status === 'enabled' ? 'badge-light-success' : 'badge-light-danger';
                return '<span class="badge ' . $badgeClass . '">' . ucfirst($coverage->status) . '</span>';
            })
            ->addColumn('action', function (Coverage $coverage) use ($user) {
                $currentUser = Auth::user();
                $canEdit = $this->canEditCoverage($currentUser, $user);

                $action = '<div class="d-flex justify-content-end flex-shrink-0">';

                if ($canEdit && $currentUser->hasPermissionTo('coverage-edit')) {
                    $action .= '<button class="btn btn-icon btn-bg-light btn-active-color-primary btn-sm me-1 edit-coverage-btn"
                                       data-coverage-id="' . $coverage->id . '"
                                       data-bs-toggle="modal"
                                       data-bs-target="#kt_modal_edit_coverage"
                                       title="Edit Coverage">
                                       <span class="svg-icon svg-icon-3">
                                           <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                               <path opacity="0.3" d="M21.4 8.35303L19.241 10.511L13.485 4.755L15.643 2.59595C16.0248 2.21423 16.5426 1.99988 17.0825 1.99988C17.6224 1.99988 18.1402 2.21423 18.522 2.59595L21.4 5.474C21.7817 5.85581 21.9962 6.37355 21.9962 6.91345C21.9962 7.45335 21.7817 7.97122 21.4 8.35303ZM3.68699 21.932L9.88699 19.865L4.13699 14.115L2.06999 20.315C1.98815 20.5619 1.99703 20.8267 2.09478 21.0681C2.19253 21.3095 2.36823 21.5130 2.59699 21.6493C2.82575 21.7856 3.09052 21.8495 3.35699 21.832C3.62346 21.8145 3.87699 21.7164 4.08699 21.5523L3.68699 21.932Z" fill="currentColor"/>
                                               <path d="M5.574 21.3L3.692 21.928C3.46591 22.0032 3.22334 22.0141 2.99144 21.9594C2.75954 21.9046 2.54744 21.7864 2.3789 21.6179C2.21036 21.4495 2.09202 21.2375 2.03711 21.0056C1.9822 20.7737 1.99289 20.5312 2.06799 20.3051L2.696 18.422L5.574 21.3ZM4.13699 14.105L9.891 19.859L19.245 10.505L13.491 4.75098L4.13699 14.105Z" fill="currentColor"/>
                                           </svg>
                                       </span>
                                </button>';
                }

                if ($canEdit && $currentUser->hasPermissionTo('coverage-delete')) {
                    $action .= '<button class="btn btn-icon btn-bg-light btn-active-color-primary btn-sm delete-coverage-btn"
                                       data-coverage-id="' . $coverage->id . '"
                                       title="Delete Coverage">
                                       <span class="svg-icon svg-icon-3">
                                           <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                               <path d="M5 9C5 8.44772 5.44772 8 6 8H18C18.5523 8 19 8.44772 19 9V18C19 19.6569 17.6569 21 16 21H8C6.34315 21 5 19.6569 5 18V9Z" fill="currentColor"/>
                                               <path opacity="0.5" d="M5 5C5 4.44772 5.44772 4 6 4H18C18.5523 4 19 4.44772 19 5V5C19 5.55228 18.5523 6 18 6H6C5.44772 6 5 5.55228 5 5V5Z" fill="currentColor"/>
                                               <path opacity="0.5" d="M9 4C9 3.44772 9.44772 3 10 3H14C14.5523 3 15 3.44772 15 4V4H9V4Z" fill="currentColor"/>
                                           </svg>
                                       </span>
                                </button>';
                }

                $action .= '</div>';
                return $action;
            })
            ->rawColumns(['status_badge', 'action'])
            ->toJson();
    }

    /**
     * Store a new coverage for the user's company
     */
    public function storeCoverage(Request $request, User $user)
    {
        // Check hierarchical permissions
        $currentUser = Auth::user();
        if (!$this->canEditCoverage($currentUser, $user)) {
            return response()->json([
                'success' => false,
                'message' => 'You do not have permission to manage coverage for this user.',
            ], 403);
        }

        $request->validate([
            'prefix' => 'required|string|max:20',
            'operator' => 'required|string|max:100',
            'masking_price' => 'required|numeric|min:0|max:999999.99',
            'non_masking_price' => 'required|numeric|min:0|max:999999.99',
            'status' => 'required|in:enabled,disabled',
        ], [
            'prefix.required' => 'Prefix is required.',
            'operator.required' => 'Operator is required.',
            'masking_price.required' => 'Masking price is required.',
            'masking_price.min' => 'Masking price must be at least 0.',
            'non_masking_price.required' => 'Non-masking price is required.',
            'non_masking_price.min' => 'Non-masking price must be at least 0.',
            'status.required' => 'Status is required.',
        ]);

        if (!$user->company_id) {
            return response()->json([
                'success' => false,
                'message' => 'User does not have an associated company.',
            ], 400);
        }

        // Check for duplicate prefix for the same company
        $existingCoverage = Coverage::where('company_id', $user->company_id)
            ->where('prefix', $request->prefix)
            ->first();

        if ($existingCoverage) {
            return response()->json([
                'success' => false,
                'message' => 'A coverage with this prefix already exists for this company.',
            ], 422);
        }

        try {
            $coverage = Coverage::create([
                'company_id' => $user->company_id,
                'prefix' => $request->prefix,
                'operator' => $request->operator,
                'masking_price' => $request->masking_price,
                'non_masking_price' => $request->non_masking_price,
                'status' => $request->status,
            ]);

            // Audit logging
            $this->logCoverageAction('created', $coverage, $user, $currentUser);

            return response()->json([
                'success' => true,
                'message' => 'Coverage added successfully.',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while adding the coverage.',
            ], 500);
        }
    }

    /**
     * Get a specific coverage for editing
     */
    public function getCoverage(User $user, Coverage $coverage)
    {
        // Check hierarchical permissions
        $currentUser = Auth::user();
        if (!$this->canEditCoverage($currentUser, $user)) {
            return response()->json([
                'success' => false,
                'message' => 'You do not have permission to manage coverage for this user.',
            ], 403);
        }

        // Coverage is automatically scoped to user's company by route model binding
        return response()->json([
            'success' => true,
            'data' => $coverage,
        ]);
    }

    /**
     * Update a coverage for the user's company
     */
    public function updateCoverage(Request $request, User $user, Coverage $coverage)
    {
        // Check hierarchical permissions
        $currentUser = Auth::user();
        if (!$this->canEditCoverage($currentUser, $user)) {
            return response()->json([
                'success' => false,
                'message' => 'You do not have permission to manage coverage for this user.',
            ], 403);
        }

        $request->validate([
            'prefix' => 'required|string|max:20',
            'operator' => 'required|string|max:100',
            'masking_price' => 'required|numeric|min:0|max:999999.99',
            'non_masking_price' => 'required|numeric|min:0|max:999999.99',
            'status' => 'required|in:enabled,disabled',
        ], [
            'prefix.required' => 'Prefix is required.',
            'operator.required' => 'Operator is required.',
            'masking_price.required' => 'Masking price is required.',
            'masking_price.min' => 'Masking price must be at least 0.',
            'non_masking_price.required' => 'Non-masking price is required.',
            'non_masking_price.min' => 'Non-masking price must be at least 0.',
            'status.required' => 'Status is required.',
        ]);

        // Coverage is automatically scoped to user's company by route model binding

        // Check for duplicate prefix for the same company (excluding current coverage)
        $existingCoverage = Coverage::where('company_id', $user->company_id)
            ->where('prefix', $request->prefix)
            ->where('id', '!=', $coverage->id)
            ->first();

        if ($existingCoverage) {
            return response()->json([
                'success' => false,
                'message' => 'A coverage with this prefix already exists for this company.',
            ], 422);
        }

        try {
            // Store old data for audit logging
            $oldData = [
                'prefix' => $coverage->prefix,
                'operator' => $coverage->operator,
                'masking_price' => $coverage->masking_price,
                'non_masking_price' => $coverage->non_masking_price,
                'status' => $coverage->status,
            ];

            $coverage->update([
                'prefix' => $request->prefix,
                'operator' => $request->operator,
                'masking_price' => $request->masking_price,
                'non_masking_price' => $request->non_masking_price,
                'status' => $request->status,
            ]);

            // Audit logging
            $this->logCoverageAction('updated', $coverage, $user, $currentUser, $oldData);

            return response()->json([
                'success' => true,
                'message' => 'Coverage updated successfully.',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while updating the coverage.',
            ], 500);
        }
    }

    /**
     * Delete a coverage for the user's company
     */
    public function deleteCoverage(User $user, Coverage $coverage)
    {
        // Check hierarchical permissions
        $currentUser = Auth::user();
        if (!$this->canEditCoverage($currentUser, $user)) {
            return response()->json([
                'success' => false,
                'message' => 'You do not have permission to manage coverage for this user.',
            ], 403);
        }

        // Coverage is automatically scoped to user's company by route model binding

        try {
            // Audit logging before deletion
            $this->logCoverageAction('deleted', $coverage, $user, $currentUser);

            $coverage->delete();

            return response()->json([
                'success' => true,
                'message' => 'Coverage deleted successfully.',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while deleting the coverage.',
            ], 500);
        }
    }

    /**
     * @throws ErrorMessageException
     */
    public function balance(Request $request)
    {

        // Todo add validation for request
        // self balance add not possible
        // check add balance permission
        // only under user balance can add.

        $request->request->add([
            'get_payment_info' => true,
        ]);
        $payment = $this->payment->proceedToPayment($request);

        $request->request->add([
            'trans_id' => $payment->transaction_id,
        ]);

        $this->payment->success($request);

        return response([
            'success' => true,
            'message' => 'Done',
        ]);
    }

    /**
     * Get recharge history data for a specific user
     */
    public function getRechargeHistoryData(Request $request, User $user)
    {
        // Check hierarchical permissions
        $currentUser = Auth::user();
        if (!$this->canViewUserRechargeHistory($currentUser, $user)) {
            return response()->json([
                'success' => false,
                'message' => 'You do not have permission to view recharge history for this user.',
            ], 403);
        }

        return datatables()->of(
            Recharge::withoutGlobalScope(TenantScope::class)
                ->with(['payment', 'company'])
                ->where('company_id', $user->company_id)
                ->orderBy('id', 'desc')
        )
            ->editColumn('recharge_date', function (Recharge $recharge) {
                if (!isset($recharge->payment)) {
                    return '';
                }
                $formattedDate = Carbon::parse($recharge->payment->created_at)->format('M d, Y h:i A');
                $humanReadable = Carbon::parse($recharge->payment->created_at)->diffForHumans();

                return "$formattedDate ($humanReadable)";
            })
            ->addColumn('gateway', function (Recharge $recharge) {
                return $recharge->payment ? $recharge->payment->gateway : '';
            })
            ->addColumn('transaction_id', function (Recharge $recharge) {
                return $recharge->payment ? $recharge->payment->transaction_id : '';
            })
            ->addColumn('amount', function (Recharge $recharge) {
                return $recharge->payment ? number_format($recharge->payment->amount, 2) : '';
            })
            ->addColumn('status', function (Recharge $recharge) {
                if (!$recharge->payment) {
                    return '';
                }
                $status = $recharge->payment->payment_status;
                $badgeClass = match($status) {
                    'completed', 'success' => 'badge-light-success',
                    'pending' => 'badge-light-warning',
                    'failed' => 'badge-light-danger',
                    default => 'badge-light-secondary'
                };
                return '<span class="badge ' . $badgeClass . '">' . ucfirst($status) . '</span>';
            })
            ->addColumn('remarks', function (Recharge $recharge) {
                return $recharge->payment ? $recharge->payment->remarks : '';
            })
            ->addColumn('action', function (Recharge $recharge) {
                return '<a href="'.route('recharges.show', $recharge->id).'" class="btn btn-sm btn-light btn-active-light-primary">
                    <i class="ki-duotone ki-eye fs-5">
                        <span class="path1"></span>
                        <span class="path2"></span>
                        <span class="path3"></span>
                    </i>
                    View Details
                </a>';
            })
            ->rawColumns(['status', 'action'])
            ->toJson();
    }



    /**
     * Manually verify a user's email
     */
    public function verifyUser(User $user)
    {
        $currentUser = Auth::user();

        // Check permissions
        if (!$this->canVerifyUser($currentUser, $user)) {
            return response()->json([
                'success' => false,
                'message' => 'You do not have permission to verify this user.',
            ], 403);
        }

        // Check if user is already verified
        if ($user->hasVerifiedEmail()) {
            return response()->json([
                'success' => false,
                'message' => 'User is already verified.',
            ], 400);
        }

        try {
            // Mark email as verified
            $user->markEmailAsVerified();

            // Log the verification action for audit purposes
            \Log::info('Manual user verification', [
                'verified_user_id' => $user->id,
                'verified_user_email' => $user->email,
                'verified_by_user_id' => $currentUser->id,
                'verified_by_user_email' => $currentUser->email,
                'timestamp' => now(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'User has been verified successfully.',
            ]);
        } catch (\Exception $e) {
            \Log::error('Failed to verify user', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'verified_by' => $currentUser->id,
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to verify user. Please try again.',
            ], 500);
        }
    }



    /**
     * Verify parent-child relationship recursively
     */
    private function verifyParentChildRelationship(User $parent, User $child): bool
    {
        if ($child->parent_id === $parent->id) {
            return true;
        }

        if ($child->parent_id && $child->parent) {
            return $this->verifyParentChildRelationship($parent, $child->parent);
        }

        return false;
    }

    /**
     * Log coverage actions for audit trail
     */
    private function logCoverageAction(string $action, Coverage $coverage, User $targetUser, User $currentUser, array $oldData = null): void
    {
        $logData = [
            'action' => $action,
            'coverage_id' => $coverage->id,
            'target_user_id' => $targetUser->id,
            'target_user_name' => $targetUser->name,
            'target_company_id' => $targetUser->company_id,
            'target_company_name' => $targetUser->company->name ?? 'N/A',
            'performed_by_user_id' => $currentUser->id,
            'performed_by_user_name' => $currentUser->name,
            'performed_by_role' => $currentUser->roles->pluck('name')->implode(', '),
            'coverage_data' => [
                'prefix' => $coverage->prefix,
                'operator' => $coverage->operator,
                'masking_price' => $coverage->masking_price,
                'non_masking_price' => $coverage->non_masking_price,
                'status' => $coverage->status,
            ],
            'old_data' => $oldData,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'timestamp' => now(),
        ];

        // Log to Laravel log file
        \Log::info("Coverage {$action}", $logData);

        // You can also store in database if you have an audit_logs table
        // AuditLog::create([
        //     'user_id' => $currentUser->id,
        //     'action' => "coverage_{$action}",
        //     'model_type' => Coverage::class,
        //     'model_id' => $coverage->id,
        //     'data' => json_encode($logData),
        //     'ip_address' => request()->ip(),
        // ]);
    }



    /**
     * Update user status
     */
    public function updateStatus(Request $request, User $user)
    {
        try {
            $request->validate([
                'status' => 'required|in:active,inactive',
            ]);

            $currentUser = Auth::user();

            if (!$this->canManageUserStatus($currentUser, $user)) {
                \Log::warning('User status update permission denied', [
                    'current_user_id' => $currentUser->id,
                    'current_user_role' => $currentUser->roles->first()->name ?? 'unknown',
                    'target_user_id' => $user->id,
                    'target_user_role' => $user->roles->first()->name ?? 'unknown',
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'You do not have permission to manage this user\'s status.',
                ], 403);
            }

            $oldStatus = $user->status_string;
            $newStatusValue = $request->status === 'active' ? 1 : 0;
            $user->update(['status' => $newStatusValue]);

            \Log::info('User status updated successfully', [
                'user_id' => $user->id,
                'user_name' => $user->name,
                'old_status' => $oldStatus,
                'new_status' => $request->status,
                'updated_by' => $currentUser->id,
                'updated_by_name' => $currentUser->name,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'User status updated successfully.',
                'status' => $request->status,
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            \Log::error('User status update validation failed', [
                'user_id' => $user->id,
                'validation_errors' => $e->errors(),
                'request_data' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Invalid status value provided.',
                'errors' => $e->errors(),
            ], 422);
        } catch (\Exception $e) {
            \Log::error('User status update failed', [
                'user_id' => $user->id,
                'error_message' => $e->getMessage(),
                'error_trace' => $e->getTraceAsString(),
                'request_data' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while updating user status.',
            ], 500);
        }
    }
}
