<?php

namespace App\Http\Controllers;

use App\Models\Transaction;
use Carbon\Carbon;
use Illuminate\Http\Request;

class TransactionController extends Controller
{
    public function browse(Request $request): \Illuminate\Http\JsonResponse
    {
        // Get current user for tenant filtering
        $user = auth()->user();

        if (!$user) {
            return response()->json(['error' => 'User not authenticated'], 401);
        }

        // Build base query with relationships
        $query = Transaction::select(
            'transactions.id',
            'transactions.user_id',
            'transactions.company_id',
            'transactions.amount_in',
            'transactions.amount_out',
            'transactions.balance_after',
            'transactions.date',
            'transactions.remarks',
            'transactions.log',
            'companies.name as company_name',
            'users.name as user_name'
        )
        ->join('companies', 'transactions.company_id', '=', 'companies.id')
        ->leftJoin('users', 'transactions.user_id', '=', 'users.id');

        // Apply tenant filtering based on user role (same logic as DLR report)
        if (!$user->hasRole('super-admin')) {
            // For non-super-admin users, filter by their company and child companies
            if ($user->company) {
                $companyIds = [$user->company->id];

                // If user is master-reseller or reseller, include child companies
                if ($user->hasAnyRole(['master-reseller', 'reseller'])) {
                    $childCompanies = \App\Models\Company::where('company_id', $user->company->id)->pluck('id')->toArray();
                    $companyIds = array_merge($companyIds, $childCompanies);
                }

                $query->whereIn('transactions.company_id', $companyIds);
            } else {
                // If user has no company, return empty result
                $query->where('transactions.id', -1);
            }
        }

        // Apply filters from request
        if ($request->filled('date_from')) {
            $query->whereDate('transactions.date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('transactions.date', '<=', $request->date_to);
        }

        if ($request->filled('company_id') && $request->company_id !== 'all') {
            $query->where('transactions.company_id', $request->company_id);
        }

        if ($request->filled('transaction_type') && $request->transaction_type !== 'all') {
            if ($request->transaction_type === 'credit') {
                $query->where('transactions.amount_in', '>', 0);
            } elseif ($request->transaction_type === 'debit') {
                $query->where('transactions.amount_out', '>', 0);
            }
        }





        $query->orderBy('transactions.id', 'DESC');

        return datatables()->of($query)
            ->addColumn('serial', function ($transaction) {
                static $counter = 0;
                return ++$counter;
            })
            ->editColumn('date', function ($transaction) {
                $formattedDate = Carbon::parse($transaction->date)->format('M d, Y h:i A');
                $humanReadable = Carbon::parse($transaction->date)->diffForHumans();
                return "$formattedDate ($humanReadable)";
            })
            ->addColumn('transaction_type', function ($transaction) {
                if ($transaction->amount_in > 0) {
                    return '<span class="badge badge-success transaction-credit">
                                <i class="fas fa-arrow-up transaction-icon"></i>Credit
                            </span>';
                } elseif ($transaction->amount_out > 0) {
                    return '<span class="badge badge-danger transaction-debit">
                                <i class="fas fa-arrow-down transaction-icon"></i>Debit
                            </span>';
                }
                return '<span class="badge badge-secondary">Unknown</span>';
            })
            ->addColumn('amount', function ($transaction) {
                if ($transaction->amount_in > 0) {
                    return '<span class="transaction-amount text-success">+' . number_format($transaction->amount_in, 2) . '</span>';
                } elseif ($transaction->amount_out > 0) {
                    return '<span class="transaction-amount text-danger">-' . number_format($transaction->amount_out, 2) . '</span>';
                }
                return '<span class="transaction-amount">0.00</span>';
            })
            ->addColumn('description', function ($transaction) {
                $description = $transaction->remarks ?: 'No description';
                if (strlen($description) > 50) {
                    return '<span title="' . htmlspecialchars($description) . '">' .
                           htmlspecialchars(substr($description, 0, 50)) . '...</span>';
                }
                return htmlspecialchars($description);
            })
            ->addColumn('balance_impact', function ($transaction) {
                if ($transaction->amount_in > 0) {
                    return '<span class="text-success"><i class="fas fa-plus-circle me-1"></i>Balance Increased</span>';
                } elseif ($transaction->amount_out > 0) {
                    return '<span class="text-danger"><i class="fas fa-minus-circle me-1"></i>Balance Decreased</span>';
                }
                return '<span class="text-muted">No Impact</span>';
            })
            ->addColumn('company_name', function ($transaction) use ($user) {
                // Show company name only to admin roles
                if ($user->hasAnyRole(['super-admin', 'master-reseller', 'reseller'])) {
                    return $transaction->company_name ?: 'Unknown';
                }
                return 'Hidden';
            })
            ->addColumn('balance_after_formatted', function ($transaction) use ($user) {
                // Show balance only to admin roles
                if ($user->hasAnyRole(['super-admin', 'master-reseller', 'reseller'])) {
                    if ($transaction->balance_after !== null) {
                        return '<span class="fw-bold text-primary">' . number_format($transaction->balance_after, 2) . '</span>';
                    }
                    return '<span class="text-muted">N/A</span>';
                }
                return '<span class="text-muted">Hidden</span>';
            })
            ->rawColumns(['transaction_type', 'amount', 'description', 'balance_impact', 'balance_after_formatted'])
            ->make(true);
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('reports.transactions');
    }

    /**
     * Export transactions to CSV with filtering
     */
    public function exportTransactions(Request $request)
    {
        try {
            // Get current user for tenant filtering
            $user = auth()->user();

            if (!$user) {
                return response()->json(['error' => 'User not authenticated'], 401);
            }

            // Build base query with relationships (same logic as browse method)
            $query = Transaction::select(
                'transactions.id',
                'transactions.user_id',
                'transactions.company_id',
                'transactions.amount_in',
                'transactions.amount_out',
                'transactions.balance_after',
                'transactions.date',
                'transactions.remarks',
                'transactions.log',
                'companies.name as company_name',
                'users.name as user_name'
            )
            ->join('companies', 'transactions.company_id', '=', 'companies.id')
            ->leftJoin('users', 'transactions.user_id', '=', 'users.id');

            // Apply tenant filtering based on user role
            if (!$user->hasRole('super-admin')) {
                if ($user->company) {
                    $companyIds = [$user->company->id];

                    if ($user->hasAnyRole(['master-reseller', 'reseller'])) {
                        $childCompanies = \App\Models\Company::where('company_id', $user->company->id)->pluck('id')->toArray();
                        $companyIds = array_merge($companyIds, $childCompanies);
                    }

                    $query->whereIn('transactions.company_id', $companyIds);
                } else {
                    $query->where('transactions.id', -1);
                }
            }

            // Apply filters from request (same as browse method)
            if ($request->filled('date_from')) {
                $query->whereDate('transactions.date', '>=', $request->date_from);
            }

            if ($request->filled('date_to')) {
                $query->whereDate('transactions.date', '<=', $request->date_to);
            }

            if ($request->filled('company_id') && $request->company_id !== 'all') {
                $query->where('transactions.company_id', $request->company_id);
            }

            if ($request->filled('transaction_type') && $request->transaction_type !== 'all') {
                if ($request->transaction_type === 'credit') {
                    $query->where('transactions.amount_in', '>', 0);
                } elseif ($request->transaction_type === 'debit') {
                    $query->where('transactions.amount_out', '>', 0);
                }
            }





            $transactions = $query->orderBy('transactions.id', 'DESC')->get();

            // Prepare CSV data
            $csvData = [];

            // CSV Headers
            $headers = ['SL', 'Date', 'Transaction Type', 'Amount', 'Description'];
            if ($user->hasAnyRole(['super-admin', 'master-reseller', 'reseller'])) {
                $headers[] = 'Account';
                $headers[] = 'User';
            }
            $headers[] = 'Balance Impact';
            if ($user->hasAnyRole(['super-admin', 'master-reseller', 'reseller'])) {
                $headers[] = 'Balance After';
            }
            $csvData[] = $headers;

            // Add data rows
            $counter = 1;
            foreach ($transactions as $transaction) {
                $transactionType = '';
                $amount = '';
                $balanceImpact = '';

                if ($transaction->amount_in > 0) {
                    $transactionType = 'Credit';
                    $amount = '+' . number_format($transaction->amount_in, 2);
                    $balanceImpact = 'Balance Increased';
                } elseif ($transaction->amount_out > 0) {
                    $transactionType = 'Debit';
                    $amount = '-' . number_format($transaction->amount_out, 2);
                    $balanceImpact = 'Balance Decreased';
                } else {
                    $transactionType = 'Unknown';
                    $amount = '0.00';
                    $balanceImpact = 'No Impact';
                }

                $row = [
                    $counter++,
                    Carbon::parse($transaction->date)->format('M d, Y h:i A'),
                    $transactionType,
                    $amount,
                    $transaction->remarks ?: 'No description'
                ];

                if ($user->hasAnyRole(['super-admin', 'master-reseller', 'reseller'])) {
                    $row[] = $transaction->company_name ?: 'Unknown';
                    $row[] = $transaction->user_name ?: 'Unknown';
                }

                $row[] = $balanceImpact;

                if ($user->hasAnyRole(['super-admin', 'master-reseller', 'reseller'])) {
                    $row[] = $transaction->balance_after !== null ? number_format($transaction->balance_after, 2) : 'N/A';
                }

                $csvData[] = $row;
            }

            // Generate filename with date range
            $dateRange = '';
            if ($request->date_from && $request->date_to) {
                $dateRange = $request->date_from . '_to_' . $request->date_to;
            } elseif ($request->date_from) {
                $dateRange = 'from_' . $request->date_from;
            } elseif ($request->date_to) {
                $dateRange = 'until_' . $request->date_to;
            } else {
                $dateRange = date('Y-m-d');
            }

            $filename = 'account_balance_history_' . $dateRange . '.csv';

            // Create CSV response
            $callback = function() use ($csvData) {
                $file = fopen('php://output', 'w');
                foreach ($csvData as $row) {
                    fputcsv($file, $row);
                }
                fclose($file);
            };

            return response()->stream($callback, 200, [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            ]);

        } catch (\Exception $e) {
            // Log the error for debugging
            \Log::error('Export Transactions Error: ' . $e->getMessage(), [
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);

            // Return a user-friendly error response
            return response()->json([
                'error' => 'Export failed: ' . $e->getMessage()
            ], 500);
        }
    }
}
