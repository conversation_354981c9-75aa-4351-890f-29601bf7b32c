<?php

namespace App\Http\Controllers;

use App\Tenant\Manager;
use Illuminate\Support\Str;

class OtherController extends Controller
{
    public function priceCoverage()
    {
        // Your logic for handling price coverage
        return view('others.price-coverage');
    }

    public function phonebook()
    {
        // Your logic for handling phonebook
        return view('others.phonebook');
    }

    public function users()
    {
        // Your logic for handling users
        return view('others.users');
    }

    public function developers()
    {
        $company = app(Manager::class)->getTenant();

        // Fallback if no tenant is found
        if (!$company) {
            $company = (object) [
                'api_key' => null,
                'name' => 'Default Company'
            ];
        }

        // Your logic for handling developers
        return view('others.developers', compact('company'));
    }

    public function generateApiKey(): \Illuminate\Http\RedirectResponse
    {
        $company = app(Manager::class)->getTenant();
        $apiKey = Str::random(30);
        $company->api_key = $apiKey;
        $company->save();

        return redirect()->route('developers');
    }
}
