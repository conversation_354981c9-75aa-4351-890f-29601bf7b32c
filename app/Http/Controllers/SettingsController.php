<?php

namespace App\Http\Controllers;

use App\Http\Requests\SettingsRequest;
use App\Models\Setting;
use App\Models\Coverage;
use App\Models\Company;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class SettingsController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware(['auth', 'verified']);
        $this->middleware(function ($request, $next) {
            // Allow access to all roles except 'client'
            if (auth()->user()->hasRole('client')) {
                abort(403, 'Unauthorized access to settings.');
            }
            return $next($request);
        });
    }

    /**
     * Display the settings page.
     */
    public function index()
    {
        $user = auth()->user();
        $companyId = $user->hasRole('super-admin') ? null : $user->company_id;

        // Get all current settings
        $settings = Setting::getAllForCompany($companyId);

        return view('admin.settings.index', compact('settings'));
    }

    /**
     * Update the settings.
     */
    public function update(SettingsRequest $request)
    {
        $user = auth()->user();
        $companyId = $user->hasRole('super-admin') ? null : $user->company_id;

        try {
            $updatedSettings = [];

            // Check if this is a pop-up settings update
            if ($request->has('popup_settings')) {
                return $this->updatePopupSettings($request, $companyId);
            }

            // Handle text-based settings
            $textSettings = [
                'website_title',
                'meta_description',
                'meta_keywords',
                'og_title',
                'og_description',
                'google_analytics_id',
                'robots_txt'
            ];

            foreach ($textSettings as $key) {
                if ($request->has($key)) {
                    $value = $request->input($key);
                    if ($value !== null) {
                        Setting::setValue($key, $value, 'string', $companyId);
                        $updatedSettings[$key] = $value;
                    }
                }
            }

            // Handle file uploads
            if ($request->hasFile('website_logo')) {
                $logoPath = $this->handleFileUpload($request->file('website_logo'), 'logos', $companyId);
                if ($logoPath) {
                    Setting::setValue('website_logo', $logoPath, 'file', $companyId);
                    $updatedSettings['website_logo'] = $logoPath;
                }
            }

            if ($request->hasFile('website_favicon')) {
                $faviconPath = $this->handleFileUpload($request->file('website_favicon'), 'favicons', $companyId);
                if ($faviconPath) {
                    Setting::setValue('website_favicon', $faviconPath, 'file', $companyId);
                    $updatedSettings['website_favicon'] = $faviconPath;
                }
            }

            // Log the settings update
            $this->logSettingsUpdate($updatedSettings, $user, $companyId);

            return redirect()->route('admin.settings.index')
                ->with('success', 'Settings updated successfully!');

        } catch (\Exception $e) {
            Log::error('Settings update failed', [
                'user_id' => $user->id,
                'company_id' => $companyId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->back()
                ->with('error', 'Failed to update settings. Please try again.')
                ->withInput();
        }
    }

    /**
     * Handle file upload for settings
     */
    private function handleFileUpload($file, $directory, $companyId = null)
    {
        try {
            $filename = time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension();
            $path = "settings/{$directory}";

            if ($companyId) {
                $path .= "/company_{$companyId}";
            }

            $fullPath = $file->storeAs($path, $filename, 'public');

            return $fullPath;

        } catch (\Exception $e) {
            Log::error('File upload failed', [
                'directory' => $directory,
                'company_id' => $companyId,
                'error' => $e->getMessage()
            ]);

            return null;
        }
    }

    /**
     * Log settings update for audit trail
     */
    private function logSettingsUpdate($updatedSettings, $user, $companyId)
    {
        $logData = [
            'action' => 'settings_update',
            'user_id' => $user->id,
            'user_name' => $user->name,
            'company_id' => $companyId,
            'updated_settings' => $updatedSettings,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'timestamp' => now(),
        ];

        Log::info('Settings updated', $logData);
    }

    /**
     * Get setting value for AJAX requests
     */
    public function getValue(Request $request)
    {
        $request->validate([
            'key' => 'required|string|max:255'
        ]);

        $user = auth()->user();
        $companyId = $user->hasRole('super-admin') ? null : $user->company_id;

        $value = Setting::getValue($request->key, $companyId);

        return response()->json([
            'success' => true,
            'key' => $request->key,
            'value' => $value
        ]);
    }

    /**
     * Delete a file setting
     */
    public function deleteFile(Request $request)
    {
        $request->validate([
            'key' => 'required|string|in:website_logo,website_favicon,popup_file'
        ]);

        $user = auth()->user();
        $companyId = $user->hasRole('super-admin') ? null : $user->company_id;

        try {
            $setting = Setting::where('setting_key', $request->key)
                ->where('company_id', $companyId)
                ->first();

            if ($setting && $setting->setting_value) {
                // Delete the file from storage
                Storage::disk('public')->delete($setting->setting_value);

                // Delete the setting record
                $setting->delete();

                // Log the deletion
                Log::info('Setting file deleted', [
                    'key' => $request->key,
                    'file_path' => $setting->setting_value,
                    'user_id' => $user->id,
                    'company_id' => $companyId
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'File deleted successfully'
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'File not found'
            ], 404);

        } catch (\Exception $e) {
            Log::error('File deletion failed', [
                'key' => $request->key,
                'user_id' => $user->id,
                'company_id' => $companyId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to delete file'
            ], 500);
        }
    }

    /**
     * Get filter options for bulk pricing management
     */
    public function getBulkPricingFilterOptions(Request $request)
    {
        // Only super-admin can access bulk pricing
        if (!auth()->user()->hasRole('super-admin')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access'
            ], 403);
        }

        try {
            // Get all available roles for super-admin
            $availableRoles = [
                'master-reseller' => 'Master Reseller',
                'reseller' => 'Reseller',
                'client' => 'Client'
            ];

            // Get all companies based on role filter
            $availableCompanies = [];
            $roleFilter = $request->input('role_filter');

            if ($roleFilter && $roleFilter !== 'all') {
                // Get companies based on role
                $companies = Company::whereHas('users', function($query) use ($roleFilter) {
                    $query->whereHas('roles', function($roleQuery) use ($roleFilter) {
                        $roleQuery->where('name', $roleFilter);
                    });
                })->select('id', 'name')->orderBy('name')->get();
            } else {
                // Get all companies
                $companies = Company::select('id', 'name')->orderBy('name')->get();
            }

            foreach ($companies as $company) {
                $availableCompanies[$company->id] = $company->name;
            }

            return response()->json([
                'success' => true,
                'roles' => $availableRoles,
                'companies' => $availableCompanies
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get bulk pricing filter options', [
                'error' => $e->getMessage(),
                'user_id' => auth()->id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to load filter options'
            ], 500);
        }
    }

    /**
     * Preview bulk pricing changes
     */
    public function previewBulkPricingChanges(Request $request)
    {
        // Only super-admin can access bulk pricing
        if (!auth()->user()->hasRole('super-admin')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access'
            ], 403);
        }

        $request->validate([
            'target_role' => 'required|string|in:all,master-reseller,reseller,client',
            'target_account' => 'required|string',
            'adjustment_type' => 'required|string|in:percentage,fixed',
            'adjustment_value' => 'required|numeric|min:-100|max:1000',
            'sms_type' => 'required|string|in:both,masking,non_masking',
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:10|max:100'
        ]);

        try {
            // Get target companies based on filters
            $targetCompanies = $this->getTargetCompanies($request->target_role, $request->target_account);

            if ($targetCompanies->isEmpty()) {
                return response()->json([
                    'success' => false,
                    'message' => 'No companies found matching the criteria'
                ]);
            }

            // Get total count for statistics
            $totalCoverages = Coverage::whereIn('company_id', $targetCompanies->pluck('id'))->count();

            // Pagination parameters
            $page = $request->input('page', 1);
            $perPage = $request->input('per_page', 25);
            $offset = ($page - 1) * $perPage;

            // Get paginated coverage records
            $coverages = Coverage::whereIn('company_id', $targetCompanies->pluck('id'))
                ->with('company:id,name')
                ->offset($offset)
                ->limit($perPage)
                ->get();

            // Calculate new prices
            $previewData = [];
            $adjustmentType = $request->adjustment_type;
            $adjustmentValue = $request->adjustment_value;
            $smsType = $request->sms_type;

            foreach ($coverages as $coverage) {
                $newMaskingPrice = $coverage->masking_price;
                $newNonMaskingPrice = $coverage->non_masking_price;

                // Calculate new prices based on SMS type and adjustment type
                if ($smsType === 'both' || $smsType === 'masking') {
                    $newMaskingPrice = $this->calculateNewPrice($coverage->masking_price, $adjustmentType, $adjustmentValue);
                }

                if ($smsType === 'both' || $smsType === 'non_masking') {
                    $newNonMaskingPrice = $this->calculateNewPrice($coverage->non_masking_price, $adjustmentType, $adjustmentValue);
                }

                $previewData[] = [
                    'id' => $coverage->id,
                    'company_name' => $coverage->company->name,
                    'prefix' => $coverage->prefix,
                    'operator' => $coverage->operator,
                    'current_masking_price' => number_format($coverage->masking_price, 4),
                    'new_masking_price' => number_format($newMaskingPrice, 4),
                    'current_non_masking_price' => number_format($coverage->non_masking_price, 4),
                    'new_non_masking_price' => number_format($newNonMaskingPrice, 4),
                    'masking_changed' => $newMaskingPrice != $coverage->masking_price,
                    'non_masking_changed' => $newNonMaskingPrice != $coverage->non_masking_price
                ];
            }

            // Calculate pagination info
            $totalPages = ceil($totalCoverages / $perPage);
            $hasNextPage = $page < $totalPages;
            $hasPrevPage = $page > 1;

            return response()->json([
                'success' => true,
                'preview_data' => $previewData,
                'stats' => [
                    'companies_count' => $targetCompanies->count(),
                    'coverages_count' => $totalCoverages,
                    'adjustment_type' => ucfirst($adjustmentType),
                    'adjustment_value' => $adjustmentValue . ($adjustmentType === 'percentage' ? '%' : '')
                ],
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $perPage,
                    'total' => $totalCoverages,
                    'total_pages' => $totalPages,
                    'has_next_page' => $hasNextPage,
                    'has_prev_page' => $hasPrevPage,
                    'from' => $offset + 1,
                    'to' => min($offset + $perPage, $totalCoverages)
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to preview bulk pricing changes', [
                'error' => $e->getMessage(),
                'user_id' => auth()->id(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to preview changes: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Apply bulk pricing changes
     */
    public function applyBulkPricingChanges(Request $request)
    {
        // Only super-admin can access bulk pricing
        if (!auth()->user()->hasRole('super-admin')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access'
            ], 403);
        }

        $request->validate([
            'target_role' => 'required|string|in:all,master-reseller,reseller,client',
            'target_account' => 'required|string',
            'adjustment_type' => 'required|string|in:percentage,fixed',
            'adjustment_value' => 'required|numeric|min:-100|max:1000',
            'sms_type' => 'required|string|in:both,masking,non_masking'
        ]);

        try {
            DB::beginTransaction();

            // Get target companies based on filters
            $targetCompanies = $this->getTargetCompanies($request->target_role, $request->target_account);

            if ($targetCompanies->isEmpty()) {
                return response()->json([
                    'success' => false,
                    'message' => 'No companies found matching the criteria'
                ]);
            }

            // Get coverage records for target companies
            $coverages = Coverage::whereIn('company_id', $targetCompanies->pluck('id'))->get();

            $updatedCount = 0;
            $adjustmentType = $request->adjustment_type;
            $adjustmentValue = $request->adjustment_value;
            $smsType = $request->sms_type;

            foreach ($coverages as $coverage) {
                $updated = false;
                $oldMaskingPrice = $coverage->masking_price;
                $oldNonMaskingPrice = $coverage->non_masking_price;

                // Update prices based on SMS type
                if ($smsType === 'both' || $smsType === 'masking') {
                    $newMaskingPrice = $this->calculateNewPrice($coverage->masking_price, $adjustmentType, $adjustmentValue);
                    if ($newMaskingPrice != $coverage->masking_price) {
                        $coverage->masking_price = $newMaskingPrice;
                        $updated = true;
                    }
                }

                if ($smsType === 'both' || $smsType === 'non_masking') {
                    $newNonMaskingPrice = $this->calculateNewPrice($coverage->non_masking_price, $adjustmentType, $adjustmentValue);
                    if ($newNonMaskingPrice != $coverage->non_masking_price) {
                        $coverage->non_masking_price = $newNonMaskingPrice;
                        $updated = true;
                    }
                }

                if ($updated) {
                    $coverage->save();
                    $updatedCount++;

                    // Log the pricing change for audit
                    $this->logPricingChange($coverage, $oldMaskingPrice, $oldNonMaskingPrice, $request);
                }
            }

            DB::commit();

            // Log the bulk pricing operation
            Log::info('Bulk pricing changes applied', [
                'user_id' => auth()->id(),
                'user_name' => auth()->user()->name,
                'target_role' => $request->target_role,
                'target_account' => $request->target_account,
                'adjustment_type' => $adjustmentType,
                'adjustment_value' => $adjustmentValue,
                'sms_type' => $smsType,
                'companies_affected' => $targetCompanies->count(),
                'coverages_updated' => $updatedCount,
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);

            // Store filter criteria in session for post-application pagination
            session([
                'bulk_pricing_filters' => [
                    'target_role' => $request->target_role,
                    'target_account' => $request->target_account,
                    'company_ids' => $targetCompanies->pluck('id')->toArray()
                ]
            ]);

            return response()->json([
                'success' => true,
                'message' => "Bulk pricing update completed successfully. {$updatedCount} coverage records updated across {$targetCompanies->count()} companies.",
                'stats' => [
                    'companies_affected' => $targetCompanies->count(),
                    'coverages_updated' => $updatedCount
                ]
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to apply bulk pricing changes', [
                'error' => $e->getMessage(),
                'user_id' => auth()->id(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to apply changes: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get paginated updated pricing data for post-application display
     */
    public function getUpdatedPricingData(Request $request)
    {
        // Only super-admin can access bulk pricing
        if (!auth()->user()->hasRole('super-admin')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access'
            ], 403);
        }

        $request->validate([
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:10|max:100'
        ]);

        try {
            // Get filter criteria from session
            $filters = session('bulk_pricing_filters');
            if (!$filters || !isset($filters['company_ids'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'No recent bulk pricing operation found'
                ]);
            }

            // Pagination parameters
            $page = $request->input('page', 1);
            $perPage = $request->input('per_page', 25);
            $offset = ($page - 1) * $perPage;

            // Get total count
            $totalCoverages = Coverage::whereIn('company_id', $filters['company_ids'])->count();

            // Get paginated coverage records
            $coverages = Coverage::whereIn('company_id', $filters['company_ids'])
                ->with('company:id,name')
                ->offset($offset)
                ->limit($perPage)
                ->get();

            $updatedPricingData = [];
            foreach ($coverages as $coverage) {
                $updatedPricingData[] = [
                    'id' => $coverage->id,
                    'company_name' => $coverage->company->name,
                    'prefix' => $coverage->prefix,
                    'operator' => $coverage->operator,
                    'new_masking_price' => number_format($coverage->masking_price, 4),
                    'new_non_masking_price' => number_format($coverage->non_masking_price, 4)
                ];
            }

            // Calculate pagination info
            $totalPages = ceil($totalCoverages / $perPage);
            $hasNextPage = $page < $totalPages;
            $hasPrevPage = $page > 1;

            return response()->json([
                'success' => true,
                'data' => $updatedPricingData,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $perPage,
                    'total' => $totalCoverages,
                    'total_pages' => $totalPages,
                    'has_next_page' => $hasNextPage,
                    'has_prev_page' => $hasPrevPage,
                    'from' => $offset + 1,
                    'to' => min($offset + $perPage, $totalCoverages)
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get updated pricing data', [
                'error' => $e->getMessage(),
                'user_id' => auth()->id(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to load updated pricing data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get target companies based on role and account filters
     */
    private function getTargetCompanies($roleFilter, $accountFilter)
    {
        $query = Company::query();

        // Apply account filter
        if ($accountFilter !== 'all') {
            $query->where('id', $accountFilter);
        }

        // Apply role filter
        if ($roleFilter !== 'all') {
            $query->whereHas('users', function($userQuery) use ($roleFilter) {
                $userQuery->whereHas('roles', function($roleQuery) use ($roleFilter) {
                    $roleQuery->where('name', $roleFilter);
                });
            });
        }

        return $query->select('id', 'name')->get();
    }

    /**
     * Calculate new price based on adjustment type and value
     */
    private function calculateNewPrice($currentPrice, $adjustmentType, $adjustmentValue)
    {
        if ($adjustmentType === 'percentage') {
            // Calculate percentage adjustment
            $adjustment = ($currentPrice * $adjustmentValue) / 100;
            $newPrice = $currentPrice + $adjustment;
        } else {
            // Fixed amount adjustment
            $newPrice = $currentPrice + $adjustmentValue;
        }

        // Ensure price doesn't go below 0
        return max(0, round($newPrice, 4));
    }

    /**
     * Log pricing change for audit trail
     */
    private function logPricingChange($coverage, $oldMaskingPrice, $oldNonMaskingPrice, $request)
    {
        Log::info('Coverage pricing updated', [
            'coverage_id' => $coverage->id,
            'company_id' => $coverage->company_id,
            'prefix' => $coverage->prefix,
            'operator' => $coverage->operator,
            'old_masking_price' => $oldMaskingPrice,
            'new_masking_price' => $coverage->masking_price,
            'old_non_masking_price' => $oldNonMaskingPrice,
            'new_non_masking_price' => $coverage->non_masking_price,
            'adjustment_type' => $request->adjustment_type,
            'adjustment_value' => $request->adjustment_value,
            'sms_type' => $request->sms_type,
            'updated_by' => auth()->id(),
            'updated_by_name' => auth()->user()->name,
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'timestamp' => now()
        ]);
    }

    /**
     * Update pop-up settings
     */
    private function updatePopupSettings(SettingsRequest $request, $companyId)
    {
        $user = auth()->user();
        $updatedSettings = [];

        try {
            // Handle pop-up text settings
            $popupTextSettings = [
                'popup_title',
                'popup_description'
            ];

            foreach ($popupTextSettings as $key) {
                if ($request->has($key)) {
                    $value = $request->input($key);
                    if ($value !== null) {
                        Setting::setValue($key, $value, 'string', $companyId);
                        $updatedSettings[$key] = $value;
                    }
                }
            }

            // Handle pop-up status (boolean)
            $popupStatus = $request->has('popup_status') ? 1 : 0;
            Setting::setValue('popup_status', $popupStatus, 'boolean', $companyId);
            $updatedSettings['popup_status'] = $popupStatus;

            // Handle pop-up file upload
            if ($request->hasFile('popup_file')) {
                // Delete old file if exists
                $oldFile = Setting::getValue('popup_file', $companyId);
                if ($oldFile && Storage::disk('public')->exists($oldFile)) {
                    Storage::disk('public')->delete($oldFile);
                }

                $filePath = $this->handleFileUpload($request->file('popup_file'), 'popup-files', $companyId);
                if ($filePath) {
                    Setting::setValue('popup_file', $filePath, 'file', $companyId);
                    $updatedSettings['popup_file'] = $filePath;
                }
            }

            // Log the settings update
            $this->logSettingsUpdate($updatedSettings, $user, $companyId);

            // Clear popup settings cache
            \App\Services\SettingsService::clearPopupCache($companyId);

            return redirect()->route('admin.settings.index')
                ->with('success', 'Pop-up settings updated successfully!');

        } catch (\Exception $e) {
            Log::error('Pop-up settings update failed', [
                'user_id' => $user->id,
                'company_id' => $companyId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->back()
                ->with('error', 'Failed to update pop-up settings. Please try again.')
                ->withInput();
        }
    }
}
