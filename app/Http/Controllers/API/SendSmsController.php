<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreMessageAPIRequest;
use App\Http\Requests\GetBalanceAPIRequest;
use App\Http\Requests\GetDLRAPIRequest;
use App\Http\Requests\GetApiKeyRequest;
use App\Http\Services\SendSmsService;
use App\Http\Services\ApiService;

class SendSmsController extends Controller
{
    private $send;
    private $apiService;

    public function __construct(SendSmsService $send, ApiService $apiService)
    {
        $this->send = $send;
        $this->apiService = $apiService;
    }

    /**
     * Display a listing of the resource.
     */
    public function smsSend(StoreMessageAPIRequest $request)
    {
        return $this->send->smsSend($request);
    }

    /**
     * Get balance for the authenticated user/company
     */
    public function getBalance(GetBalanceAPIRequest $request, $apiKey = null)
    {
        // Merge the API key from route parameter if provided
        if ($apiKey) {
            $request->merge(['api_key' => $apiKey]);
        }
        return $this->apiService->getBalance($request);
    }

    /**
     * Get all delivery reports for the authenticated user/company
     */
    public function getDLR(GetDLRAPIRequest $request, $apiKey = null)
    {
        // Merge the API key from route parameter if provided
        if ($apiKey) {
            $request->merge(['api_key' => $apiKey]);
        }
        return $this->apiService->getDLR($request);
    }

    /**
     * Get API key for authenticated user credentials
     */
    public function getApiKey($username = null, $password = null)
    {
        // Create a new request with the route parameters
        $request = request();
        $request->merge([
            'username' => $username,
            'password' => $password
        ]);

        // Validate the request manually
        $validator = validator($request->all(), [
            'username' => 'required|string',
            'password' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors(),
            ], 422);
        }

        return $this->apiService->getApiKey($request);
    }
}
