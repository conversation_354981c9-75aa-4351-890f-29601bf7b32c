<?php

namespace App\Http\Controllers;

use App\Helpers\Traits\HierarchicalPermissions;
use App\Models\SupportTicket;
use App\Models\Payment;
use App\Models\Sender;
use App\Models\Domain;
use App\Models\User;
use App\Models\Recharge;
use App\Models\Message;
use App\Models\Company;
use App\Tenant\Scopes\TenantScope;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class AdminDashboardController extends Controller
{
    use HierarchicalPermissions;

    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware(['auth', 'verified']);
        $this->middleware(function ($request, $next) {
            if (!auth()->user()->hasAnyRole(['super-admin', 'master-reseller', 'reseller'])) {
                abort(403, 'Unauthorized access to admin dashboard.');
            }
            return $next($request);
        });
    }

    /**
     * Display the admin dashboard.
     */
    public function index()
    {
        $dashboardData = $this->getDashboardData();
        $criticalAlerts = $this->getCriticalAlerts();
        $actionQueue = $this->getActionQueue();
        $summaryCards = $this->getSummaryCards();

        // Get aggregated SMS and cost metrics for all users
        $aggregatedMetrics = $this->getAggregatedMetrics();

        // Get quick actions for admin
        $quickActions = $this->getQuickActions();

        return view('admin.dashboard.index', compact(
            'dashboardData',
            'criticalAlerts',
            'actionQueue',
            'summaryCards',
            'aggregatedMetrics',
            'quickActions'
        ));
    }

    /**
     * Get critical alerts for real-time updates.
     */
    public function getAlerts(): JsonResponse
    {
        try {
            $alerts = $this->getCriticalAlerts();
            return response()->json([
                'success' => true,
                'data' => $alerts,
                'timestamp' => now()->toISOString(),
                'count' => count($alerts)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch alerts',
                'data' => [],
                'timestamp' => now()->toISOString()
            ], 500);
        }
    }

    /**
     * Get dashboard metrics data.
     */
    public function getMetrics(): JsonResponse
    {
        try {
            $currentUser = auth()->user();
            $cacheKey = 'admin_dashboard_metrics_' . $currentUser->id . '_' . $currentUser->getRoleNames()->first();

            $metrics = Cache::remember($cacheKey, 30, function () {
                return $this->getDashboardData();
            });

            return response()->json([
                'success' => true,
                'data' => $metrics,
                'timestamp' => now()->toISOString()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch metrics',
                'data' => [],
                'timestamp' => now()->toISOString()
            ], 500);
        }
    }

    /**
     * Get action queue data for real-time updates.
     */
    public function getActionQueueData(): JsonResponse
    {
        try {
            $actionQueue = $this->getActionQueue();
            return response()->json([
                'success' => true,
                'data' => $actionQueue,
                'timestamp' => now()->toISOString(),
                'count' => count($actionQueue)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch action queue',
                'data' => [],
                'timestamp' => now()->toISOString()
            ], 500);
        }
    }

    /**
     * Get summary cards data for real-time updates.
     */
    public function getSummaryCardsData(): JsonResponse
    {
        try {
            $summaryCards = $this->getSummaryCards();
            return response()->json([
                'success' => true,
                'data' => $summaryCards,
                'timestamp' => now()->toISOString()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch summary cards',
                'data' => [],
                'timestamp' => now()->toISOString()
            ], 500);
        }
    }

    /**
     * Get aggregated metrics for real-time updates.
     */
    public function getAggregatedMetricsData(): JsonResponse
    {
        try {
            $currentUser = auth()->user();
            $cacheKey = 'admin_aggregated_metrics_' . $currentUser->id . '_' . $currentUser->getRoleNames()->first();

            $metrics = Cache::remember($cacheKey, 60, function () {
                return $this->getAggregatedMetrics();
            });

            return response()->json([
                'success' => true,
                'data' => $metrics,
                'timestamp' => now()->toISOString()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch aggregated metrics',
                'data' => [],
                'timestamp' => now()->toISOString()
            ], 500);
        }
    }

    /**
     * Get comprehensive dashboard data for real-time updates.
     */
    public function getDashboardDataApi(): JsonResponse
    {
        try {
            $data = [
                'summary_cards' => $this->getSummaryCards(),
                'action_queue' => $this->getActionQueue(),
                'alerts' => $this->getCriticalAlerts(),
                'metrics' => $this->getDashboardData(),
                'aggregated_metrics' => $this->getAggregatedMetrics(),
                'quick_actions' => $this->getQuickActions()
            ];

            return response()->json([
                'success' => true,
                'data' => $data,
                'timestamp' => now()->toISOString()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch dashboard data',
                'data' => [],
                'timestamp' => now()->toISOString()
            ], 500);
        }
    }

    /**
     * Get comprehensive dashboard data with optimized queries and hierarchical filtering.
     */
    private function getDashboardData(): array
    {
        $currentUser = auth()->user();
        $cacheKey = 'admin_dashboard_core_metrics_' . $currentUser->id . '_' . $currentUser->getRoleNames()->first();

        return Cache::remember($cacheKey, 30, function () use ($currentUser) {
            try {
                // Apply hierarchical filtering to support tickets
                $ticketQuery = SupportTicket::query();
                if (!$currentUser->hasRole('super-admin')) {
                    $ticketQuery = $this->applyHierarchicalFiltering($ticketQuery, 'user_id');
                }

                $ticketStats = $ticketQuery->selectRaw('
                    COUNT(CASE WHEN priority = "critical" AND status NOT IN ("resolved", "closed") THEN 1 END) as critical_tickets,
                    COUNT(CASE WHEN priority = "high" AND status = "open" THEN 1 END) as high_priority_tickets,
                    COUNT(CASE WHEN status = "open" THEN 1 END) as open_tickets
                ')->first();

                // Apply hierarchical filtering to payments
                $paymentQuery = Payment::query();
                if (!$currentUser->hasRole('super-admin')) {
                    $paymentQuery = $this->applyHierarchicalFiltering($paymentQuery, 'user_id');
                }

                $paymentStats = $paymentQuery->selectRaw('
                    COUNT(CASE WHEN payment_status = "pending" AND gateway = "manual" THEN 1 END) as pending_payments,
                    COALESCE(SUM(CASE WHEN payment_status = "pending" AND gateway = "manual" THEN amount END), 0) as pending_payment_amount
                ')->first();

                // Apply hierarchical filtering to senders
                $senderQuery = Sender::where('status', 'Pending Approved');
                if (!$currentUser->hasRole('super-admin')) {
                    $senderQuery = $this->applyCompanyFiltering($senderQuery, 'company_id');
                }
                $pendingSenders = $senderQuery->count();

                // Apply hierarchical filtering to domains
                $domainQuery = Domain::query();
                if (!$currentUser->hasRole('super-admin')) {
                    $domainQuery = $this->applyCompanyFiltering($domainQuery, 'company_id');
                }

                $domainStats = $domainQuery->selectRaw('
                    COUNT(CASE WHEN dns_status = "failed" THEN 1 END) as failed_domains,
                    COUNT(CASE WHEN dns_status = "pending" THEN 1 END) as pending_domains
                ')->first();

                // Apply hierarchical filtering to users
                $userQuery = User::query();
                if (!$currentUser->hasRole('super-admin')) {
                    $filteredUserIds = $this->getFilteredUserIds();
                    $userQuery = $userQuery->whereIn('id', $filteredUserIds);
                }

                $userStats = $userQuery->selectRaw('
                    COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as new_users_today,
                    COUNT(CASE WHEN is_verified = 0 THEN 1 END) as unverified_users
                ')->first();

                return [
                    'critical_tickets' => (int) $ticketStats->critical_tickets,
                    'high_priority_tickets' => (int) $ticketStats->high_priority_tickets,
                    'open_tickets' => (int) $ticketStats->open_tickets,
                    'pending_payments' => (int) $paymentStats->pending_payments,
                    'pending_payment_amount' => (float) $paymentStats->pending_payment_amount,
                    'pending_senders' => $pendingSenders,
                    'failed_domains' => (int) $domainStats->failed_domains,
                    'pending_domains' => (int) $domainStats->pending_domains,
                    'new_users_today' => (int) $userStats->new_users_today,
                    'unverified_users' => (int) $userStats->unverified_users,
                    'last_updated' => now()->toISOString(),
                ];
            } catch (\Exception $e) {
                \Log::error('Dashboard data fetch error: ' . $e->getMessage());

                // Return default values if there's an error
                return [
                    'critical_tickets' => 0,
                    'high_priority_tickets' => 0,
                    'open_tickets' => 0,
                    'pending_payments' => 0,
                    'pending_payment_amount' => 0.0,
                    'pending_senders' => 0,
                    'failed_domains' => 0,
                    'pending_domains' => 0,
                    'new_users_today' => 0,
                    'unverified_users' => 0,
                    'last_updated' => now()->toISOString(),
                ];
            }
        });
    }

    /**
     * Get critical alerts that require immediate attention with hierarchical filtering.
     * Uses cached dashboard data to avoid redundant queries.
     */
    private function getCriticalAlerts(): array
    {
        // Get cached dashboard data to avoid redundant queries (already filtered)
        $dashboardData = $this->getDashboardData();
        $alerts = [];

        // Critical support tickets
        if ($dashboardData['critical_tickets'] > 0) {
            $alerts[] = [
                'type' => 'critical',
                'icon' => 'ki-duotone ki-warning-2',
                'title' => 'Critical Support Tickets',
                'message' => "{$dashboardData['critical_tickets']} critical support ticket(s) require immediate attention",
                'count' => $dashboardData['critical_tickets'],
                'action_url' => route('admin.support-tickets.index', ['priority' => 'critical']),
                'action_text' => 'View Tickets',
                'priority' => 1
            ];
        }

        // Pending payments
        if ($dashboardData['pending_payments'] > 0) {
            $alerts[] = [
                'type' => 'financial',
                'icon' => 'ki-duotone ki-dollar',
                'title' => 'Pending Payment Approvals',
                'message' => "{$dashboardData['pending_payments']} manual payment(s) pending approval (৳" . number_format($dashboardData['pending_payment_amount'], 2) . ")",
                'count' => $dashboardData['pending_payments'],
                'action_url' => route('admin.recharges.index'),
                'action_text' => 'Review Payments',
                'priority' => 2
            ];
        }

        // Pending sender approvals
        if ($dashboardData['pending_senders'] > 0) {
            $alerts[] = [
                'type' => 'service',
                'icon' => 'ki-duotone ki-send',
                'title' => 'Sender ID Approvals',
                'message' => "{$dashboardData['pending_senders']} sender ID(s) awaiting approval",
                'count' => $dashboardData['pending_senders'],
                'action_url' => route('senders.index', ['status' => 'pending']),
                'action_text' => 'Review Senders',
                'priority' => 3
            ];
        }

        // Sort by priority
        usort($alerts, function ($a, $b) {
            return $a['priority'] <=> $b['priority'];
        });

        return $alerts;
    }

    /**
     * Get summary cards data for dashboard.
     */
    private function getSummaryCards(): array
    {
        $data = $this->getDashboardData();

        return [
            [
                'title' => 'Support Tickets',
                'value' => $data['open_tickets'],
                'subtitle' => $data['critical_tickets'] . ' critical, ' . $data['high_priority_tickets'] . ' high priority',
                'icon' => 'ki-duotone ki-questionnaire-tablet',
                'color' => $this->getUrgencyColor($data['critical_tickets'], 0, 1),
                'trend' => $this->getTicketTrend(),
                'action_url' => route('admin.support-tickets.index')
            ],
            [
                'title' => 'Pending Payments',
                'value' => '৳' . number_format($data['pending_payment_amount'], 2),
                'subtitle' => $data['pending_payments'] . ' manual payment(s) awaiting approval',
                'icon' => 'ki-duotone ki-dollar',
                'color' => $this->getUrgencyColor($data['pending_payments'], 0, 5),
                'trend' => $this->getPaymentTrend(),
                'action_url' => route('recharges')
            ],
            [
                'title' => 'Sender Approvals',
                'value' => $data['pending_senders'],
                'subtitle' => 'Sender IDs awaiting approval',
                'icon' => 'ki-duotone ki-send',
                'color' => $this->getUrgencyColor($data['pending_senders'], 0, 3),
                'trend' => $this->getSenderTrend(),
                'action_url' => route('senders.index')
            ],
            [
                'title' => 'System Health',
                'value' => $this->getSystemHealthScore(),
                'subtitle' => $data['failed_domains'] . ' failed domains, ' . $data['unverified_users'] . ' unverified users',
                'icon' => 'ki-duotone ki-shield-tick',
                'color' => $this->getSystemHealthColor(),
                'trend' => 'stable',
                'action_url' => '#'
            ]
        ];
    }

    /**
     * Get prioritized action queue with hierarchical filtering.
     */
    private function getActionQueue(): array
    {
        $currentUser = auth()->user();
        $queue = [];

        // Critical support tickets with hierarchical filtering
        $criticalTicketsQuery = SupportTicket::where('priority', 'critical')
            ->whereNotIn('status', ['resolved', 'closed'])
            ->with('user');

        if (!$currentUser->hasRole('super-admin')) {
            $criticalTicketsQuery = $this->applyHierarchicalFiltering($criticalTicketsQuery, 'user_id');
        }

        $criticalTickets = $criticalTicketsQuery->orderBy('created_at', 'asc')
            ->limit(5)
            ->get();

        foreach ($criticalTickets as $ticket) {
            $queue[] = [
                'type' => 'support_ticket',
                'priority' => 1,
                'title' => 'Critical Support Ticket #' . $ticket->id,
                'description' => $ticket->subject,
                'user' => $ticket->user->name ?? 'Unknown',
                'created_at' => $ticket->created_at,
                'urgency_class' => 'danger',
                'actions' => [
                    [
                        'text' => 'View Ticket',
                        'url' => route('admin.support-tickets.show', $ticket->id),
                        'class' => 'btn-primary'
                    ],
                    [
                        'text' => 'Mark In Progress',
                        'url' => route('admin.support-tickets.update', $ticket->id),
                        'class' => 'btn-warning',
                        'method' => 'PUT',
                        'data' => ['status' => 'in_progress']
                    ]
                ]
            ];
        }

        // Pending payments with hierarchical filtering - ONLY MANUAL PAYMENTS
        $pendingPaymentsQuery = Payment::where('payment_status', 'pending')
            ->where('gateway', 'manual')  // Only show manual payments
            ->with(['user', 'company']);

        if (!$currentUser->hasRole('super-admin')) {
            $pendingPaymentsQuery = $this->applyHierarchicalFiltering($pendingPaymentsQuery, 'user_id');
        }

        $pendingPayments = $pendingPaymentsQuery->orderBy('created_at', 'asc')
            ->limit(5)
            ->get();

        foreach ($pendingPayments as $payment) {
            $queue[] = [
                'type' => 'payment',
                'priority' => 2,
                'title' => 'Payment Approval Required',
                'description' => 'Amount: ৳' . number_format($payment->amount, 2) . ' via ' . $payment->gateway,
                'user' => $payment->company->name ?? 'Unknown Company',
                'created_at' => $payment->created_at,
                'urgency_class' => 'warning',
                'actions' => [
                    [
                        'text' => 'Approve',
                        'url' => route('payment.success', [
                            'trans_id' => $payment->transaction_id,
                            'payment_method' => 'manual',
                            'route' => 'admin.dashboard'
                        ]),
                        'class' => 'btn-success'
                    ],
                    [
                        'text' => 'View Details',
                        'url' => route('recharges.show', $payment->transaction_id),
                        'class' => 'btn-info'
                    ]
                ]
            ];
        }

        // Pending sender approvals with hierarchical filtering
        $pendingSendersQuery = Sender::where('status', 'Pending Approved')
            ->with('company');

        if (!$currentUser->hasRole('super-admin')) {
            $pendingSendersQuery = $this->applyCompanyFiltering($pendingSendersQuery, 'company_id');
        }

        $pendingSenders = $pendingSendersQuery->orderBy('created_at', 'asc')
            ->limit(3)
            ->get();

        foreach ($pendingSenders as $sender) {
            $queue[] = [
                'type' => 'sender',
                'priority' => 3,
                'title' => 'Sender ID Approval',
                'description' => 'Sender: ' . $sender->name . ' (' . ($sender->is_masking ? 'Masking' : 'Non-Masking') . ')',
                'user' => $sender->company->name ?? 'Unknown Company',
                'created_at' => $sender->created_at,
                'urgency_class' => 'info',
                'actions' => [
                    [
                        'text' => 'View Details',
                        'url' => route('senders.show', $sender->id),
                        'class' => 'btn-primary'
                    ]
                ]
            ];
        }

        // Sort by priority and creation date
        usort($queue, function ($a, $b) {
            if ($a['priority'] === $b['priority']) {
                return $a['created_at'] <=> $b['created_at'];
            }
            return $a['priority'] <=> $b['priority'];
        });

        return array_slice($queue, 0, 10); // Return top 10 items
    }

    /**
     * Get urgency color based on thresholds.
     */
    private function getUrgencyColor(int $value, int $good, int $warning): string
    {
        if ($value <= $good) {
            return 'success';
        } elseif ($value <= $warning) {
            return 'warning';
        } else {
            return 'danger';
        }
    }

    /**
     * Get ticket trend data.
     */
    private function getTicketTrend(): string
    {
        $todayTickets = SupportTicket::whereDate('created_at', today())->count();
        $yesterdayTickets = SupportTicket::whereDate('created_at', today()->subDay())->count();

        if ($yesterdayTickets == 0) {
            return $todayTickets > 0 ? 'up' : 'stable';
        }

        $change = (($todayTickets - $yesterdayTickets) / $yesterdayTickets) * 100;

        if ($change > 10) return 'up';
        if ($change < -10) return 'down';
        return 'stable';
    }

    /**
     * Get payment trend data.
     */
    private function getPaymentTrend(): string
    {
        $todayPayments = Payment::where('gateway', 'manual')->whereDate('created_at', today())->count();
        $yesterdayPayments = Payment::where('gateway', 'manual')->whereDate('created_at', today()->subDay())->count();

        if ($yesterdayPayments == 0) {
            return $todayPayments > 0 ? 'up' : 'stable';
        }

        $change = (($todayPayments - $yesterdayPayments) / $yesterdayPayments) * 100;

        if ($change > 10) return 'up';
        if ($change < -10) return 'down';
        return 'stable';
    }

    /**
     * Get sender trend data.
     */
    private function getSenderTrend(): string
    {
        $todaySenders = Sender::whereDate('created_at', today())->count();
        $yesterdaySenders = Sender::whereDate('created_at', today()->subDay())->count();

        if ($yesterdaySenders == 0) {
            return $todaySenders > 0 ? 'up' : 'stable';
        }

        $change = (($todaySenders - $yesterdaySenders) / $yesterdaySenders) * 100;

        if ($change > 10) return 'up';
        if ($change < -10) return 'down';
        return 'stable';
    }

    /**
     * Get system health score.
     */
    private function getSystemHealthScore(): string
    {
        $failedDomains = Domain::where('dns_status', 'failed')->count();
        $totalDomains = Domain::count();

        if ($totalDomains == 0) {
            return '100%';
        }

        $healthScore = (($totalDomains - $failedDomains) / $totalDomains) * 100;
        return round($healthScore) . '%';
    }

    /**
     * Get system health color.
     */
    private function getSystemHealthColor(): string
    {
        $failedDomains = Domain::where('dns_status', 'failed')->count();
        $totalDomains = Domain::count();

        if ($totalDomains == 0) {
            return 'success';
        }

        $healthScore = (($totalDomains - $failedDomains) / $totalDomains) * 100;

        if ($healthScore >= 95) return 'success';
        if ($healthScore >= 80) return 'warning';
        return 'danger';
    }

    /**
     * Get aggregated SMS and cost metrics with hierarchical filtering.
     */
    private function getAggregatedMetrics(): array
    {
        $currentUser = auth()->user();

        try {
            // Apply hierarchical filtering to messages
            $messageQuery = Message::query();
            if (!$currentUser->hasRole('super-admin')) {
                $messageQuery = $this->applyHierarchicalFiltering($messageQuery, 'user_id');
            }

            // Calculate SMS count for last week
            $smsLastWeek = (clone $messageQuery)->whereBetween('created_at', [now()->subWeek(), now()])->count();

            // Calculate cost for last week
            $costLastWeek = (clone $messageQuery)->whereBetween('created_at', [now()->subWeek(), now()])->sum('sms_cost') ?? 0;

            // Calculate SMS count for last month
            $smsInLastMonth = (clone $messageQuery)->whereBetween('created_at', [
                now()->startOfMonth()->subMonth(),
                now()->endOfMonth()->subMonth()
            ])->count();

            // Calculate cost for last month with hierarchical filtering
            $costInLastMonth = (clone $messageQuery)->whereBetween('created_at', [
                now()->startOfMonth()->subMonth(),
                now()->endOfMonth()->subMonth()
            ])->sum('sms_cost') ?? 0;

            // Get balance based on user role and hierarchical permissions
            $totalBalance = $this->getHierarchicalBalance($currentUser);

            // Calculate total balance in messages (assuming 0.5 per message)
            $totalBalanceMsgs = $totalBalance / 0.5;

            // Get last month's name
            $lastMonthName = Carbon::now()->subMonth()->format('F');

            return [
                'sms_last_week' => $smsLastWeek,
                'cost_last_week' => (float) $costLastWeek,
                'sms_in_last_month' => $smsInLastMonth,
                'cost_in_last_month' => (float) $costInLastMonth,
                'total_balance' => (float) $totalBalance,
                'total_balance_msgs' => (int) $totalBalanceMsgs,
                'last_month_name' => $lastMonthName,
            ];
        } catch (\Exception $e) {
            // Return default values if there's an error
            return [
                'sms_last_week' => 0,
                'cost_last_week' => 0.0,
                'sms_in_last_month' => 0,
                'cost_in_last_month' => 0.0,
                'total_balance' => 0.0,
                'total_balance_msgs' => 0,
                'last_month_name' => Carbon::now()->subMonth()->format('F'),
            ];
        }
    }

    /**
     * Get quick actions for admin dashboard.
     */
    private function getQuickActions(): array
    {
        return [
            [
                'title' => 'Manage Support Tickets',
                'description' => 'View and respond to customer tickets',
                'icon' => 'ki-duotone ki-questionnaire-tablet',
                'icon_paths' => ['path1', 'path2'],
                'color' => 'primary',
                'url' => route('admin.support-tickets.index')
            ],
            [
                'title' => 'Review Payments',
                'description' => 'Approve pending recharge requests',
                'icon' => 'ki-duotone ki-dollar',
                'icon_paths' => ['path1', 'path2', 'path3'],
                'color' => 'warning',
                'url' => route('admin.recharges.index')
            ],
            [
                'title' => 'Sender Approvals',
                'description' => 'Review sender ID applications',
                'icon' => 'ki-duotone ki-send',
                'icon_paths' => ['path1', 'path2'],
                'color' => 'info',
                'url' => route('senders.index')
            ],
            [
                'title' => 'User Management',
                'description' => 'Manage user accounts and roles',
                'icon' => 'ki-duotone ki-people',
                'icon_paths' => ['path1', 'path2', 'path3', 'path4', 'path5'],
                'color' => 'success',
                'url' => route('users.index')
            ]
        ];
    }

    /**
     * Get filtered user IDs based on current user's hierarchical permissions.
     */
    private function getFilteredUserIds(): array
    {
        $currentUser = auth()->user();

        // Super admin can see all users
        if ($currentUser->hasRole('super-admin')) {
            return User::pluck('id')->toArray();
        }

        // Master reseller can see users in their hierarchy
        if ($currentUser->hasRole('master-reseller')) {
            return $this->getUsersInHierarchy($currentUser)->pluck('id')->toArray();
        }

        // Reseller can see their direct clients only
        if ($currentUser->hasRole('reseller')) {
            return User::where('parent_id', $currentUser->id)
                ->where(function($query) {
                    $query->whereHas('roles', function($q) {
                        $q->where('name', 'client');
                    });
                })
                ->pluck('id')
                ->toArray();
        }

        // Default: only current user
        return [$currentUser->id];
    }

    /**
     * Get filtered company IDs based on current user's hierarchical permissions.
     */
    private function getFilteredCompanyIds(): array
    {
        $currentUser = auth()->user();

        // Super admin can see all companies
        if ($currentUser->hasRole('super-admin')) {
            return Company::pluck('id')->toArray();
        }

        // Master reseller and reseller can see their own company and sub-companies
        if ($currentUser->hasRole('master-reseller') || $currentUser->hasRole('reseller')) {
            $userCompanyIds = $currentUser->companies()->pluck('companies.id')->toArray();

            // Also include companies of users in their hierarchy
            $hierarchyUserIds = $this->getFilteredUserIds();
            $hierarchyCompanyIds = User::whereIn('id', $hierarchyUserIds)
                ->with('companies')
                ->get()
                ->flatMap(function($user) {
                    return $user->companies->pluck('id');
                })
                ->unique()
                ->toArray();

            return array_unique(array_merge($userCompanyIds, $hierarchyCompanyIds));
        }

        // Default: current user's companies only
        return $currentUser->companies()->pluck('companies.id')->toArray();
    }

    /**
     * Get users in hierarchy for master reseller.
     */
    private function getUsersInHierarchy(User $currentUser): \Illuminate\Database\Eloquent\Collection
    {
        // Get all users where current user is in their parent chain
        return User::where(function($query) use ($currentUser) {
            $query->where('parent_id', $currentUser->id)
                  ->orWhereHas('parent', function($q) use ($currentUser) {
                      $q->where('parent_id', $currentUser->id);
                  })
                  ->orWhereHas('parent.parent', function($q) use ($currentUser) {
                      $q->where('parent_id', $currentUser->id);
                  });
        })->get();
    }

    /**
     * Apply hierarchical filtering to a query builder.
     */
    private function applyHierarchicalFiltering($query, string $userIdColumn = 'user_id')
    {
        $currentUser = auth()->user();

        // Super admin sees everything
        if ($currentUser->hasRole('super-admin')) {
            return $query;
        }

        // Apply filtering based on user hierarchy
        $filteredUserIds = $this->getFilteredUserIds();
        return $query->whereIn($userIdColumn, $filteredUserIds);
    }

    /**
     * Apply company-based filtering to a query builder.
     */
    private function applyCompanyFiltering($query, string $companyIdColumn = 'company_id')
    {
        $currentUser = auth()->user();

        // Super admin sees everything
        if ($currentUser->hasRole('super-admin')) {
            return $query;
        }

        // Apply filtering based on company hierarchy
        $filteredCompanyIds = $this->getFilteredCompanyIds();
        return $query->whereIn($companyIdColumn, $filteredCompanyIds);
    }

    /**
     * Get balance based on user's hierarchical permissions.
     */
    private function getHierarchicalBalance(User $currentUser): float
    {
        // Super admin sees total balance across all companies
        if ($currentUser->hasRole('super-admin')) {
            return Company::sum('current_balance') ?? 0;
        }

        // Master reseller and reseller see their company balance
        if ($currentUser->hasRole('master-reseller') || $currentUser->hasRole('reseller')) {
            // First try to get the user's primary company balance
            $userCompany = $currentUser->company;
            if ($userCompany) {
                return $userCompany->current_balance ?? 0;
            }

            // Fallback: get balance from companies the user is associated with (many-to-many)
            $companiesBalance = $currentUser->companies()->sum('current_balance');
            if ($companiesBalance > 0) {
                return $companiesBalance;
            }

            // Last fallback: if no company association, return 0
            return 0;
        }

        // Default: current user's company balance
        $userCompany = $currentUser->company;
        return $userCompany ? ($userCompany->current_balance ?? 0) : 0;
    }

    /**
     * Handle quick actions from dashboard.
     */
    public function quickAction(Request $request): JsonResponse
    {
        $request->validate([
            'type' => 'required|in:approve_payment,approve_sender,update_ticket',
            'id' => 'required|integer',
            'action' => 'required|string'
        ]);

        try {
            switch ($request->type) {
                case 'approve_payment':
                    $payment = Payment::findOrFail($request->id);
                    // Use existing payment success logic
                    $paymentController = new \App\Http\Controllers\PaymentController();
                    $paymentController->success(new Request([
                        'trans_id' => $payment->transaction_id,
                        'status' => 'success',
                        'payment_method' => $payment->gateway, // Add payment method to trigger cache clearing
                        'route' => 'admin.dashboard'
                    ]));
                    break;

                case 'update_ticket':
                    $ticket = SupportTicket::findOrFail($request->id);
                    $ticket->update(['status' => $request->action]);
                    break;

                default:
                    return response()->json(['success' => false, 'message' => 'Invalid action type']);
            }

            return response()->json(['success' => true, 'message' => 'Action completed successfully']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Action failed: ' . $e->getMessage()]);
        }
    }
}
