<?php

namespace App\Http\Controllers;

use App\Helpers\Common;
use App\Http\Requests\StoreSenderRequest;
use App\Http\Requests\UpdateSenderRequest;
use App\Http\Services\SenderService;
use App\Models\Sender;
use App\Models\Server;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;

class SenderController extends Controller
{
    private $sender;

    public function __construct(SenderService $sender)
    {
        $this->sender = $sender;
        $this->middleware(['permission:sender-list|sender-create|sender-edit|sender-delete'], ['only' => ['index', 'show']]);
        $this->middleware(['permission:sender-create'], ['only' => ['create', 'store']]);
        $this->middleware(['permission:sender-edit'], ['only' => ['edit', 'update']]);
        $this->middleware(['permission:sender-delete'], ['only' => ['destroy']]);
    }

    /**
     * @throws \Yajra\DataTables\Exceptions\Exception
     * @throws \Exception
     */
    public function browse()
    {
        $datatable = datatables()
            ->of(Sender::with(['company:id,name', 'server:id,name']))
            ->addColumn('created_at', function (Sender $name) {
                return $name->created_at->format('F j, Y, g:i a');
            })
            ->editColumn('executed_at', function (Sender $name) {
                return ! empty($name->executed_at) ? Carbon::parse($name->executed_at)->format('F j, Y, g:i a') : '-';
            })
            ->editColumn('is_default', function (Sender $name) {
                return $name->is_default ? 'Yes' : 'No';
            })
            ->editColumn('status', function (Sender $name) {
                return ucfirst($name->status);
            });

        // Add server column only for super-admin users
        if (auth()->user()->hasRole('super-admin')) {
            $datatable->addColumn('server', function (Sender $sender) {
                return $sender->server ? $sender->server->name : 'Not Assigned';
            });
        }

        return $datatable->addColumn('action', function (Sender $sender) {
                $action = '';
                $user = auth()->user();

                // View Details button - available for admin roles
                if ($user->hasAnyRole(['super-admin', 'master-reseller', 'reseller'])) {
                    $action .= '<a href="'.route('senders.show', $sender->id).'" class="btn btn-primary btn-sm me-1" title="View Details">
                        <i class="fas fa-eye"></i> View
                    </a>';
                }

                if ($user->hasRole('super-admin') || $sender->user_id == $user->id) {
                    $action .= '<button href="#" class="btn btn-danger btn-sm me-1 delete-button" data-endpoint="'.route('senders.destroy', $sender->id).'" title="Delete">
                        <i class="fas fa-trash"></i>
                    </button>';
                }

                if (Auth::user()->hasPermissionTo('sender-edit')) {
                    $action .= '<a href="'.route('senders.edit', $sender->id).'" class="btn btn-secondary btn-sm me-1" title="Edit">
                        <i class="fas fa-edit"></i>
                    </a>';
                }

                $action .= '<button href="#" data-endpoint="'.route('webapi.default', $sender->id).'" class="btn btn-success btn-sm make-default" title="Mark as Default">
                    <i class="fas fa-star"></i>
                </button>';

                return $action;
            })
            ->rawColumns(['action'])
            ->toJson();
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $names = Sender::all();

        // Fetch servers for the dropdown, accessible only to super-admin
        $servers = [];
        if (auth()->user()->hasRole('super-admin')) {
            $servers = Server::select('id', 'name')->get();
        }

        return view('senders.index', compact('names', 'servers'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $senderStatus = Common::senderStatus();
        $senderIdFee = Common::senderIdFee();

        return view('senders.create', compact('senderStatus', 'senderIdFee'));
    }

    /**
     * Store a newly created sender.
     */
    public function store(StoreSenderRequest $request)
    {
        // Check if the sender name already exists
        $sender = Sender::where('name', $request->name)->first();
        if (! empty($sender)) {
            return response([
                'success' => false,
                'message' => 'This sender name already exists. Please contact the admin.',
            ], 404);
        }

        // Handle `is_server_default` based on user role
        $isServerDefault = Auth::user()->hasRole('super-admin') ? ($request->is_server_default ?? 0) : 0;

        // Initialize an array to store file paths
        $documentPaths = [];

        // Check if there are files in `required_documents` and upload each
        if ($request->hasFile('required_documents')) {
            foreach ($request->file('required_documents') as $file) {
                // Save to `documents` directory on the `public` disk
                $path = $file->store('documents', 'public');
                $documentPaths[] = $path; // Collect the stored file path
            }
        }

        // Create the sender with collected data
        $resp = Sender::create([
            'user_id' => Auth::user()->id,
            'server_id' => $request->server_id ?? null,
            'name' => $request->name,
            'is_default' => $request->is_default ?? 0,
            'is_server_default' => $isServerDefault,
            'is_masking' => is_numeric($request->name) ? 0 : 1,
            'status' => 'Pending Approved',
            'required_documents' => $documentPaths, // Save document paths as JSON array
        ]);

        return response([
            'success' => true,
            'message' => 'Sender has been created successfully.',
        ]);
    }

    /**
     * Display the specified sender details.
     */
    public function show(Sender $sender)
    {
        // Load relationships
        $sender->load(['company', 'server', 'user']);

        // Check permissions - only admin roles can view sender details
        $currentUser = auth()->user();
        if (!$currentUser->hasAnyRole(['super-admin', 'master-reseller', 'reseller'])) {
            abort(403, 'Unauthorized access to sender details.');
        }

        // Apply hierarchical filtering for non-super-admin users
        if (!$currentUser->hasRole('super-admin')) {
            // Check if the sender belongs to a company in the user's hierarchy
            $userCompanyIds = $this->getFilteredCompanyIds($currentUser);

            if (!in_array($sender->company_id, $userCompanyIds)) {
                abort(404, 'Sender not found or access denied.');
            }
        }

        // Get available servers for approval (only for super-admin)
        $servers = [];
        if ($currentUser->hasRole('super-admin')) {
            $servers = \App\Models\Server::select('id', 'name')->where('status', 'enabled')->get();
        }

        // Get sender status options
        $senderStatus = \App\Helpers\Common::senderStatus();

        return view('senders.show', compact('sender', 'servers', 'senderStatus'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Sender $sender)
    {
        $senderStatus = Common::senderStatus();
        $senderIdFee = Common::senderIdFee();

        // Fetch servers for the dropdown, accessible only to super-admin
        $servers = [];
        if (auth()->user()->hasRole('super-admin')) {
            $servers = Server::select('id', 'name')->get();
        }

        // Check if required_documents is cast to an array, otherwise decode it manually
        $requiredDocuments = collect($sender->required_documents)->toArray();

        return view('senders.edit', compact('sender', 'senderStatus', 'senderIdFee', 'servers', 'requiredDocuments'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @throws \App\Exceptions\ErrorMessageException
     */
    public function update(UpdateSenderRequest $request, Sender $sender)
    {

        // Check if the status is 'Approved' and server_id is required
        if ($request->status === 'Approved' && empty($request->server_id)) {
            return redirect()->back()->withErrors([
                'server_id' => 'You must select a server before approving this sender.',
            ]);
        }
        // Only super-admin can update `is_server_default`, otherwise it defaults to 0
        $isServerDefault = Auth::user()->hasRole('super-admin') ? ($request->is_server_default ?? 0) : 0;

        // Delete old documents if new ones are uploaded
        if ($request->hasFile('required_documents')) {
            foreach ($sender->required_documents ?? [] as $oldDocument) {
                if (Storage::disk('public')->exists($oldDocument)) {
                    Storage::disk('public')->delete($oldDocument);
                }
            }

            // Upload new documents
            $documentPaths = [];
            foreach ($request->file('required_documents') as $file) {
                $path = $file->store('documents', 'public'); // Store in 'public/documents' folder
                $documentPaths[] = $path;
            }

            $sender->required_documents = $documentPaths; // Update document paths in sender record
        }
        // Update other fields in sender record
        $sender->update([
            'name' => $request->name,
            'server_id' => $request->server_id,
            'is_default' => $request->is_default,
            'is_server_default' => $isServerDefault,
            'status' => $request->status,
            'executed_at' => $request->status == 'Approved' ? now() : null,
        ]);

        return redirect()->route('senders.index')
            ->with('success', 'Sender has been updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Sender $sender)
    {
        $user = auth()->user();

        if ($user->hasRole('super-admin') || $sender->user_id == $user->id) {
            $sender->delete();

            return response([
                'success' => true,
                'message' => 'Sender has been deleted successfully.',
            ]);
        }

        return response([
            'success' => false,
            'message' => 'You don\'t have permission to access.',
        ], 401);
    }

    /**
     * Mark the specified sender as default.
     */
    public function makeDefault(Sender $sender)
    {
        $this->sender->makeDefault($sender);

        return response([
            'success' => true,
            'message' => 'Sender marked as default successfully.',
        ]);
    }

    /**
     * Approve a sender.
     */
    public function approve(Request $request, Sender $sender)
    {
        // Check permissions - only admin roles can approve senders
        $currentUser = auth()->user();
        if (!$currentUser->hasAnyRole(['super-admin', 'master-reseller', 'reseller'])) {
            return response([
                'success' => false,
                'message' => 'Unauthorized access to approve senders.',
            ], 403);
        }

        // Apply hierarchical filtering for non-super-admin users
        if (!$currentUser->hasRole('super-admin')) {
            $userCompanyIds = $this->getFilteredCompanyIds($currentUser);

            if (!in_array($sender->company_id, $userCompanyIds)) {
                return response([
                    'success' => false,
                    'message' => 'Sender not found or access denied.',
                ], 404);
            }
        }

        // Validate sender status
        if ($sender->status !== 'Pending Approved') {
            return response([
                'success' => false,
                'message' => 'Only senders with "Pending Approved" status can be approved.',
            ], 400);
        }

        // Validate server assignment requirement
        $serverId = $request->input('server_id');
        if (empty($serverId)) {
            return response([
                'success' => false,
                'message' => 'You must select a server before approving this sender.',
            ], 400);
        }

        // Validate server exists and is enabled
        $server = \App\Models\Server::where('id', $serverId)->where('status', 'enabled')->first();
        if (!$server) {
            return response([
                'success' => false,
                'message' => 'Selected server is not available.',
            ], 400);
        }

        try {
            // Update sender status and assign server
            $sender->update([
                'status' => 'Approved',
                'server_id' => $serverId,
                'executed_at' => now(),
            ]);

            // Clear dashboard cache after approval
            $this->clearDashboardCache();

            return response([
                'success' => true,
                'message' => 'Sender has been approved successfully.',
            ]);

        } catch (\Exception $e) {
            \Log::error('Sender approval error: ' . $e->getMessage(), [
                'sender_id' => $sender->id,
                'user_id' => $currentUser->id,
                'server_id' => $serverId
            ]);

            return response([
                'success' => false,
                'message' => 'An error occurred while approving the sender. Please try again.',
            ], 500);
        }
    }

    /**
     * Clear dashboard cache for all admin users after sender approval
     */
    private function clearDashboardCache(): void
    {
        try {
            // Get all admin users (super-admin, master-reseller, reseller)
            $adminUsers = \App\Models\User::whereHas('roles', function($query) {
                $query->whereIn('name', ['super-admin', 'master-reseller', 'reseller']);
            })->get();

            // Clear dashboard cache for each admin user
            foreach ($adminUsers as $user) {
                $userRoles = $user->getRoleNames();
                if ($userRoles->isNotEmpty()) {
                    $roleKey = $userRoles->first();

                    // Clear all dashboard-related cache keys
                    $cacheKeys = [
                        'admin_dashboard_core_metrics_' . $user->id . '_' . $roleKey,
                        'admin_dashboard_metrics_' . $user->id . '_' . $roleKey,
                        'admin_aggregated_metrics_' . $user->id . '_' . $roleKey
                    ];

                    foreach ($cacheKeys as $cacheKey) {
                        \Cache::forget($cacheKey);
                    }
                }
            }

            \Log::info('Dashboard cache cleared after sender approval', [
                'cleared_for_users' => $adminUsers->count(),
                'timestamp' => now()
            ]);
        } catch (\Exception $e) {
            \Log::error('Failed to clear dashboard cache after sender approval', [
                'error' => $e->getMessage(),
                'timestamp' => now()
            ]);
        }
    }

    /**
     * Get filtered company IDs based on current user's hierarchical permissions.
     */
    private function getFilteredCompanyIds($currentUser): array
    {
        // Super admin can see all companies
        if ($currentUser->hasRole('super-admin')) {
            return \App\Models\Company::pluck('id')->toArray();
        }

        // Master reseller and reseller can see their own company and sub-companies
        if ($currentUser->hasRole('master-reseller') || $currentUser->hasRole('reseller')) {
            $userCompanyIds = $currentUser->companies()->pluck('companies.id')->toArray();

            // Also include companies of users in their hierarchy
            $hierarchyUserIds = $this->getFilteredUserIds($currentUser);
            $hierarchyCompanyIds = \App\Models\User::whereIn('id', $hierarchyUserIds)
                ->with('companies')
                ->get()
                ->flatMap(function($user) {
                    return $user->companies->pluck('id');
                })
                ->unique()
                ->toArray();

            return array_unique(array_merge($userCompanyIds, $hierarchyCompanyIds));
        }

        // Default: current user's companies only
        return $currentUser->companies()->pluck('companies.id')->toArray();
    }

    /**
     * Get filtered user IDs based on current user's hierarchical permissions.
     */
    private function getFilteredUserIds($currentUser): array
    {
        // Super admin can see all users
        if ($currentUser->hasRole('super-admin')) {
            return \App\Models\User::pluck('id')->toArray();
        }

        // Master reseller can see users in their hierarchy
        if ($currentUser->hasRole('master-reseller')) {
            return $this->getUsersInHierarchy($currentUser)->pluck('id')->toArray();
        }

        // Reseller can see their direct clients only
        if ($currentUser->hasRole('reseller')) {
            return \App\Models\User::where('parent_id', $currentUser->id)
                ->where(function($query) {
                    $query->whereHas('roles', function($q) {
                        $q->where('name', 'client');
                    });
                })
                ->pluck('id')
                ->toArray();
        }

        // Default: only current user
        return [$currentUser->id];
    }

    /**
     * Get users in hierarchy for master reseller.
     */
    private function getUsersInHierarchy($currentUser): \Illuminate\Database\Eloquent\Collection
    {
        // Get all users where current user is in their parent chain
        return \App\Models\User::where(function($query) use ($currentUser) {
            $query->where('parent_id', $currentUser->id)
                  ->orWhereHas('parent', function($q) use ($currentUser) {
                      $q->where('parent_id', $currentUser->id);
                  })
                  ->orWhereHas('parent.parent', function($q) use ($currentUser) {
                      $q->where('parent_id', $currentUser->id);
                  });
        })->get();
    }
}
