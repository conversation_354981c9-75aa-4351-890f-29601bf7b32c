<?php

namespace App\Http\Controllers;

use App\Models\ImpersonationLog;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;

class ImpersonationController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware(['auth', 'verified']);
        $this->middleware(function ($request, $next) {
            $user = auth()->user();
            $isImpersonating = Session::get('impersonating', false);

            // Allow stop action if currently impersonating (regardless of current user's role)
            if ($request->route()->getName() === 'impersonate.stop' && $isImpersonating) {
                return $next($request);
            }

            // For start action, require super-admin role
            if (!$user->hasRole('super-admin')) {
                abort(403, 'Unauthorized access to impersonation functionality.');
            }

            return $next($request);
        });
    }

    /**
     * Start impersonating a user
     */
    public function start(Request $request, User $user): RedirectResponse
    {
        $originalUser = Auth::user();

        // Security checks
        if (!$originalUser->hasRole('super-admin')) {
            abort(403, 'Only super-admin users can impersonate other users.');
        }

        // Prevent self-impersonation
        if ($originalUser->id === $user->id) {
            return redirect()->back()->with([
                'message' => 'You cannot impersonate yourself.',
                'status' => 'error',
            ]);
        }

        // Prevent impersonating other super-admins
        if ($user->hasRole('super-admin')) {
            return redirect()->back()->with([
                'message' => 'You cannot impersonate other super-admin users.',
                'status' => 'error',
            ]);
        }

        // Store impersonation chain in session
        $impersonationChain = Session::get('impersonation_chain', []);
        $impersonationChain[] = [
            'user_id' => $originalUser->id,
            'level' => count($impersonationChain) + 1,
            'started_at' => now()
        ];
        
        Session::put('impersonation_chain', $impersonationChain);
        Session::put('impersonator_id', $originalUser->id);
        Session::put('impersonating', true);

        // Log the impersonation start with level and chain data
        $level = count($impersonationChain);
        ImpersonationLog::logStart($originalUser, $user, $request->ip(), $request->userAgent(), $level, $impersonationChain);

        Log::info('User impersonation started', [
            'impersonator_id' => $originalUser->id,
            'impersonator_email' => $originalUser->email,
            'impersonated_id' => $user->id,
            'impersonated_email' => $user->email,
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
        ]);

        // Login as the target user
        Auth::login($user);

        return redirect()->route('home')->with([
            'message' => "You are now impersonating {$user->name}.",
            'status' => 'success',
        ]);
    }

    /**
     * Stop impersonating and return to original user
     */
    public function stop(Request $request): RedirectResponse
    {
        $impersonatorId = Session::get('impersonator_id');
        $currentUser = Auth::user();

        if (!$impersonatorId || !Session::get('impersonating')) {
            return redirect()->route('home')->with([
                'message' => 'You are not currently impersonating anyone.',
                'status' => 'error',
            ]);
        }

        $originalUser = User::find($impersonatorId);

        if (!$originalUser) {
            // Clear session and logout if original user not found
            Session::forget(['impersonator_id', 'impersonating']);
            Auth::logout();
            return redirect()->route('login')->with([
                'message' => 'Original user not found. Please login again.',
                'status' => 'error',
            ]);
        }

        // Log the impersonation stop with level and chain data
        $impersonationChain = Session::get('impersonation_chain', []);
        $level = count($impersonationChain);
        ImpersonationLog::logStop($originalUser, $currentUser, $request->ip(), $request->userAgent(), $level, $impersonationChain);

        Log::info('User impersonation stopped', [
            'impersonator_id' => $originalUser->id,
            'impersonator_email' => $originalUser->email,
            'impersonated_id' => $currentUser->id,
            'impersonated_email' => $currentUser->email,
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
        ]);

        // Handle multi-level impersonation chain
        $impersonationChain = Session::get('impersonation_chain', []);
        
        if (count($impersonationChain) > 1) {
            // Remove the last level and return to previous level
            array_pop($impersonationChain);
            $previousLevel = end($impersonationChain);
            $previousUser = User::find($previousLevel['user_id']);
            
            Session::put('impersonation_chain', $impersonationChain);
            Session::put('impersonator_id', $previousUser->id);
            
            Auth::login($previousUser);
            
            return redirect()->route('home')->with([
                'message' => "Returned to previous impersonation level: {$previousUser->name}.",
                'status' => 'success',
            ]);
        } else {
            // Clear all impersonation session data
            Session::forget(['impersonator_id', 'impersonating', 'impersonation_chain']);
            
            // Login back as original user
            Auth::login($originalUser);
            
            return redirect()->route('users.index')->with([
                'message' => 'You have stopped impersonating and returned to your account.',
                'status' => 'success',
            ]);
        }
    }

    /**
     * Check if currently impersonating
     */
    public function isImpersonating(): bool
    {
        return Session::get('impersonating', false) && Session::has('impersonator_id');
    }

    /**
     * Get the original user (impersonator)
     */
    public function getImpersonator(): ?User
    {
        if (!$this->isImpersonating()) {
            return null;
        }

        $impersonatorId = Session::get('impersonator_id');
        return User::find($impersonatorId);
    }

    /**
     * Start multi-level impersonation
     */
    public function startMultiLevel(Request $request, User $user): RedirectResponse
    {
        return $this->start($request, $user);
    }

    /**
     * Stop impersonation to a specific level
     */
    public function stopToLevel(Request $request, int $level): RedirectResponse
    {
        $impersonationChain = Session::get('impersonation_chain', []);
        $currentUser = Auth::user();

        if (empty($impersonationChain) || $level < 1 || $level > count($impersonationChain)) {
            return redirect()->back()->with([
                'message' => 'Invalid impersonation level.',
                'status' => 'error',
            ]);
        }

        // Remove levels above the target level
        $impersonationChain = array_slice($impersonationChain, 0, $level);
        
        if ($level === 1) {
            // Return to original user
            $originalUser = User::find($impersonationChain[0]['user_id']);
            Session::forget(['impersonator_id', 'impersonating', 'impersonation_chain']);
            Auth::login($originalUser);
            
            return redirect()->route('users.index')->with([
                'message' => 'Returned to original account.',
                'status' => 'success',
            ]);
        } else {
            // Return to specific level
            $targetLevel = end($impersonationChain);
            $targetUser = User::find($targetLevel['user_id']);
            
            Session::put('impersonation_chain', $impersonationChain);
            Session::put('impersonator_id', $targetUser->id);
            
            Auth::login($targetUser);
            
            return redirect()->route('home')->with([
                'message' => "Returned to impersonation level {$level}: {$targetUser->name}.",
                'status' => 'success',
            ]);
        }
    }

    /**
     * Get the current impersonation chain
     */
    public function getImpersonationChain(): array
    {
        return Session::get('impersonation_chain', []);
    }
}
