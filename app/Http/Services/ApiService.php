<?php

namespace App\Http\Services;

use App\Exceptions\SmsSendingResponseException;
use App\Helpers\SmsResponse;
use App\Http\Repositories\CompanyRepository;
use App\Models\Message;
use App\Tenant\Manager;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class ApiService
{
    private $companyRepo;

    public function __construct(?CompanyRepository $repo = null)
    {
        $this->companyRepo = $repo ?: new CompanyRepository;
    }

    /**
     * Find company by API key or username/password
     *
     * @return \App\Models\Company|null
     */
    public function findCompany(Request $request)
    {
        $company = $this->companyRepo->findByApiKey($request->api_key);

        if ($request->has('api_key') && !empty($company)) {
            return $company;
        }

        $credentials = [
            'username' => $request->username,
            'password' => $request->password,
        ];

        if (!Auth::attempt($credentials)) {
            return null;
        }

        return auth()->user()->companies->first();
    }

    /**
     * Get balance for the authenticated company
     */
    public function getBalance(Request $request)
    {
        $company = $this->findCompany($request);

        if (empty($company)) {
            throw new SmsSendingResponseException(1003);
        }

        return response()->json([
            'response_code' => '0000',
            'message' => 'Balance retrieved successfully',
            'data' => [
                'current_balance' => (float) $company->current_balance,
                'balance_expired' => $company->balance_expired ? $company->balance_expired->format('Y-m-d H:i:s') : null,
                'company_name' => $company->name,
            ]
        ]);
    }

    /**
     * Get delivery reports for the authenticated company
     */
    public function getDLR(Request $request)
    {
        $company = $this->findCompany($request);

        if (empty($company)) {
            throw new SmsSendingResponseException(1003);
        }

        // Set up query with company filtering
        $query = Message::select(
            'messages.message_id',
            'messages.phone_number',
            'messages.sms_content',
            'messages.sms_type',
            'messages.sms_count',
            'messages.sms_cost',
            'messages.status',
            'messages.created_at',
            'messages.schedule_at',
            'messages.batch_number',
            'messages.api_response',
            'senders.name AS sender_name',
            'senders.is_masking'
        )
        ->leftJoin('senders', 'messages.sender_id', '=', 'senders.id')
        ->where('messages.company_id', $company->id);

        // Apply date filters if provided
        if ($request->filled('date_from')) {
            $query->whereDate('messages.created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('messages.created_at', '<=', $request->date_to);
        }

        // Apply pagination
        $limit = $request->get('limit', 100);
        $offset = $request->get('offset', 0);

        $total = $query->count();
        $messages = $query->orderBy('messages.created_at', 'desc')
                         ->limit($limit)
                         ->offset($offset)
                         ->get();

        return response()->json([
            'response_code' => '0000',
            'message' => 'Delivery reports retrieved successfully',
            'data' => [
                'total' => $total,
                'limit' => $limit,
                'offset' => $offset,
                'messages' => $messages->map(function ($message) {
                    return [
                        'message_id' => $message->message_id,
                        'phone_number' => $message->phone_number,
                        'sender_name' => $message->sender_name,
                        'sms_content' => $message->sms_content,
                        'sms_type' => $message->sms_type,
                        'sms_count' => $message->sms_count,
                        'sms_cost' => (float) $message->sms_cost,
                        'status' => $message->status,
                        'sent_at' => $message->created_at ? $message->created_at->format('Y-m-d H:i:s') : null,
                        'scheduled_at' => $message->schedule_at ? $message->schedule_at->format('Y-m-d H:i:s') : null,
                        'batch_number' => $message->batch_number,
                        'api_response' => $message->api_response ? json_decode($message->api_response, true) : null,
                        'is_masking' => (bool) $message->is_masking,
                    ];
                })
            ]
        ]);
    }

    /**
     * Get API key for authenticated user credentials
     */
    public function getApiKey(Request $request)
    {
        $credentials = [
            'username' => $request->username,
            'password' => $request->password,
        ];

        if (!Auth::attempt($credentials)) {
            return response()->json(SmsResponse::error(1010));
        }

        $user = auth()->user();
        $company = $user->companies->first();

        if (empty($company)) {
            return response()->json(SmsResponse::error(1003));
        }

        return response()->json([
            'response_code' => '0000',
            'message' => 'API key retrieved successfully',
            'data' => [
                'api_key' => $company->api_key,
                'company_name' => $company->name,
                'user_name' => $user->name,
            ]
        ]);
    }
}
