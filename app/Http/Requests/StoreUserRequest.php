<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->hasPermissionTo('user-create');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        $currentUser = $this->user();

        // Get the current tenant/company context for scoping unique validation
        $tenantId = null;
        if ($currentUser->hasRole('super-admin')) {
            // Super admin can work across all companies, so no scoping needed
            $tenantId = null;
        } else {
            // For non-super-admin users, scope to their company
            $tenantId = $currentUser->company_id;
        }

        return [
            'name' => 'required',
            'email' => [
                'required',
                'email',
                $tenantId ? Rule::unique('users')->where('company_id', $tenantId) : 'unique:users'
            ],
            'phone' => [
                'required',
                $tenantId ? Rule::unique('users')->where('company_id', $tenantId) : 'unique:users'
            ],
            'role_id' => 'required',
        ];
    }
}
