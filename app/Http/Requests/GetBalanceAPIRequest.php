<?php

namespace App\Http\Requests;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class GetBalanceAPIRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'username' => 'required_without:api_key|string',
            'password' => 'required_without:api_key|string',
            'api_key' => 'required_without_all:username,password|string',
        ];
    }

    public function messages()
    {
        return [
            'username.required_without' => 'The username field is required when API key is not present.',
            'password.required_without' => 'The password field is required when API key is not present.',
            'api_key.required_without_all' => 'The API key field is required when neither username nor password are present.',
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $errors = $validator->errors();
        throw new HttpResponseException(response()->json([
            'success' => false,
            'message' => 'Validation errors',
            'errors' => $errors,
        ], 422));
    }
}
