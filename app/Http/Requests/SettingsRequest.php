<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class SettingsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Check if user has admin role or super-admin role
        return auth()->user()->hasAnyRole(['super-admin', 'master-reseller']);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            // Website Settings
            'website_title' => 'nullable|string|max:255',
            'website_logo' => 'nullable|file|mimes:jpg,jpeg,png,svg|max:2048', // 2MB max
            'website_favicon' => 'nullable|file|mimes:ico,png|max:512', // 512KB max for favicon

            // SEO Settings
            'meta_description' => 'nullable|string|max:160',
            'meta_keywords' => 'nullable|string|max:500',
            'og_title' => 'nullable|string|max:60',
            'og_description' => 'nullable|string|max:160',
            'google_analytics_id' => 'nullable|string|max:50|regex:/^(G-|UA-|GT-)[A-Z0-9-]+$/i',
            'robots_txt' => 'nullable|string|max:5000',

            // Pop-up Settings
            'popup_title' => 'required_if:popup_settings,"1"|string|max:255',
            'popup_description' => 'required_if:popup_settings,"1"|string|max:10000',
            'popup_file' => 'nullable|file|mimes:pdf,jpg,jpeg|max:5120', // 5MB max
            'popup_status' => 'nullable|boolean',
            'popup_settings' => 'nullable', // Hidden field to identify popup settings form
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'website_title.max' => 'Website title cannot exceed 255 characters.',
            'website_logo.mimes' => 'Website logo must be a JPG, JPEG, PNG, or SVG file.',
            'website_logo.max' => 'Website logo file size cannot exceed 2MB.',
            'website_favicon.mimes' => 'Favicon must be an ICO or PNG file.',
            'website_favicon.max' => 'Favicon file size cannot exceed 512KB.',
            'meta_description.max' => 'Meta description cannot exceed 160 characters.',
            'meta_keywords.max' => 'Meta keywords cannot exceed 500 characters.',
            'og_title.max' => 'Open Graph title cannot exceed 60 characters.',
            'og_description.max' => 'Open Graph description cannot exceed 160 characters.',
            'google_analytics_id.regex' => 'Google Analytics ID format is invalid. Should start with G-, UA-, or GT-.',
            'robots_txt.max' => 'Robots.txt content cannot exceed 5000 characters.',

            // Pop-up Settings Messages
            'popup_title.required_if' => 'Pop-up title is required.',
            'popup_title.max' => 'Pop-up title cannot exceed 255 characters.',
            'popup_description.required_if' => 'Pop-up description is required.',
            'popup_description.max' => 'Pop-up description cannot exceed 10,000 characters.',
            'popup_file.mimes' => 'Pop-up file must be a PDF, JPG, or JPEG file.',
            'popup_file.max' => 'Pop-up file size cannot exceed 5MB.',
        ];
    }

    /**
     * Get custom attribute names for validation errors.
     */
    public function attributes(): array
    {
        return [
            'website_title' => 'website title',
            'website_logo' => 'website logo',
            'website_favicon' => 'favicon',
            'meta_description' => 'meta description',
            'meta_keywords' => 'meta keywords',
            'og_title' => 'Open Graph title',
            'og_description' => 'Open Graph description',
            'google_analytics_id' => 'Google Analytics ID',
            'robots_txt' => 'robots.txt content',

            // Pop-up Settings Attributes
            'popup_title' => 'pop-up title',
            'popup_description' => 'pop-up description',
            'popup_file' => 'pop-up file',
            'popup_status' => 'pop-up status',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Custom validation for favicon dimensions if uploaded
            if ($this->hasFile('website_favicon')) {
                $favicon = $this->file('website_favicon');
                if ($favicon->isValid()) {
                    $imageInfo = getimagesize($favicon->getPathname());
                    if ($imageInfo) {
                        $width = $imageInfo[0];
                        $height = $imageInfo[1];

                        // Check if favicon is square and appropriate size
                        if ($width !== $height) {
                            $validator->errors()->add('website_favicon', 'Favicon must be square (equal width and height).');
                        }

                        if (!in_array($width, [16, 32, 48, 64, 128])) {
                            $validator->errors()->add('website_favicon', 'Favicon should be 16x16, 32x32, 48x48, 64x64, or 128x128 pixels.');
                        }
                    }
                }
            }

            // Custom validation for logo dimensions if uploaded
            if ($this->hasFile('website_logo')) {
                $logo = $this->file('website_logo');
                if ($logo->isValid()) {
                    $imageInfo = getimagesize($logo->getPathname());
                    if ($imageInfo) {
                        $width = $imageInfo[0];
                        $height = $imageInfo[1];

                        // Check reasonable logo dimensions
                        if ($width > 2000 || $height > 2000) {
                            $validator->errors()->add('website_logo', 'Logo dimensions should not exceed 2000x2000 pixels.');
                        }

                        if ($width < 50 || $height < 50) {
                            $validator->errors()->add('website_logo', 'Logo dimensions should be at least 50x50 pixels.');
                        }
                    }
                }
            }
        });
    }
}
