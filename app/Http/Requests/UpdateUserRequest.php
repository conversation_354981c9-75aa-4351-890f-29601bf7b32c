<?php

namespace App\Http\Requests;

use App\Helpers\Traits\HierarchicalPermissions;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateUserRequest extends FormRequest
{
    use HierarchicalPermissions;
    public function authorize(): bool
    {
        $currentUser = $this->user();
        $targetUser = $this->route('user');

        if (!$targetUser instanceof User) {
            return false;
        }

        if ($currentUser->hasRole('super-admin')) {
            return true;
        }

        if ($currentUser->hasRole('master-reseller')) {
            return $this->isUserInHierarchy($currentUser, $targetUser);
        }

        if ($currentUser->hasRole('reseller')) {
            return $targetUser->parent_id === $currentUser->id && $targetUser->hasRole('client');
        }

        return false;
    }

    public function rules(): array
    {
        $currentUser = $this->user();
        $targetUser = $this->route('user');

        $userId = $targetUser instanceof User ? $targetUser->id : null;

        // Get the current tenant/company context for scoping unique validation
        $tenantId = null;
        if ($currentUser->hasRole('super-admin')) {
            // Super admin can work across all companies, so no scoping needed
            $tenantId = null;
        } else {
            // For non-super-admin users, scope to their company
            $tenantId = $currentUser->company_id;
        }

        $rules = [
            'name' => 'required|string|max:255',
            'email' => [
                'required',
                'email',
                'max:255',
                $tenantId ? Rule::unique('users')->ignore($userId)->where('company_id', $tenantId) : Rule::unique('users')->ignore($userId)
            ],
            // 'phone' => [
            //     'required',
            //     'string',
            //     'max:20',
            //     $tenantId ? Rule::unique('users')->ignore($userId)->where('company_id', $tenantId) : Rule::unique('users')->ignore($userId)
            // ],
        ];

        if ($currentUser->hasAnyRole(['super-admin', 'master-reseller'])) {
            $rules['role_id'] = 'sometimes|exists:roles,id';
        }

        if ($currentUser->hasRole('super-admin')) {
            $rules['status'] = 'sometimes|in:0,1';
            $rules['remarks'] = 'nullable|string|max:500';
        }

        $rules['minimum_recharge_amount'] = 'nullable|numeric|min:0';

        return $rules;
    }



    public function messages(): array
    {
        return [
            'name.required' => 'The name field is required.',
            'email.required' => 'The email field is required.',
            'email.email' => 'Please enter a valid email address.',
            'email.unique' => 'This email address is already in use.',
            'phone.required' => 'The phone field is required.',
            'phone.unique' => 'This phone number is already in use.',
            'role_id.exists' => 'The selected role is invalid.',
            'minimum_recharge_amount.numeric' => 'The minimum recharge amount must be a number.',
            'minimum_recharge_amount.min' => 'The minimum recharge amount must be at least 0.',
        ];
    }
}
