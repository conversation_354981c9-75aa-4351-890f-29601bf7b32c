<?php

namespace App\Http\View\Composers;

use App\Services\SettingsService;
use Illuminate\View\View;

class PopupSettingsComposer
{
    /**
     * Bind data to the view.
     */
    public function compose(View $view): void
    {
        // Only add popup settings for authenticated users
        if (!auth()->check()) {
            return;
        }

        $user = auth()->user();

        // Determine company ID based on user role
        // Super admin sees global settings, others see company-specific settings
        $companyId = $user->hasRole('super-admin') ? null : $user->company_id;

        // Get popup settings
        $popupSettings = SettingsService::getPopupSettings($companyId);

        // Share with view
        $view->with('popupSettings', $popupSettings);
    }
}
