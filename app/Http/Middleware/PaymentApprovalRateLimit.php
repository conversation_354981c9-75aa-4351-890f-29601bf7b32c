<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Symfony\Component\HttpFoundation\Response;

class PaymentApprovalRateLimit
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Only apply rate limiting to manual payment approvals
        if ($request->route()->getName() === 'payment.success' && 
            $request->input('payment_method') === 'manual') {
            
            $key = 'payment-approval:' . $request->user()->id;
            
            // Allow 10 payment approvals per minute per user
            if (RateLimiter::tooManyAttempts($key, 10)) {
                $seconds = RateLimiter::availableIn($key);
                
                return response()->json([
                    'error' => 'Too many payment approval attempts. Please try again in ' . $seconds . ' seconds.'
                ], 429);
            }
            
            RateLimiter::hit($key, 60); // 60 seconds window
        }

        return $next($request);
    }
}
