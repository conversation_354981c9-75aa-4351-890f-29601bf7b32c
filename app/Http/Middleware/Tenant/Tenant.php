<?php

namespace App\Http\Middleware\Tenant;

use App\Exceptions\ErrorMessageException;
use App\Models\Company;
use App\Tenant\Manager;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class Tenant
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = auth()->user();
        $tenantId = null;

        // First try to get company from many-to-many relationship
        $company = $user->companies()->first();
        if ($company) {
            $tenantId = $company->id;
        }
        // If no many-to-many relationship, try direct company_id
        elseif ($user->company_id) {
            $tenantId = $user->company_id;
        }
        // If still no company, try session
        elseif (session()->get('tenant')) {
            $tenantId = session()->get('tenant');
        }
        // Last resort: throw error
        else {
            throw new ErrorMessageException('No company associated with user');
        }

        $tenant = $this->resolveTenant($tenantId);

        if (empty($tenant->id)) {
            throw new ErrorMessageException('Company not found');
        }

        $this->registerTenant($tenant);

        return $next($request);
    }

    /**
     * register Tenant
     *
     * @return void
     */
    protected function registerTenant($tenant)
    {
        app(Manager::class)->setTenant($tenant);

        session()->put('tenant', $tenant->id);
    }

    /**
     * Find the company by id
     *
     * @return mixed
     */
    protected function resolveTenant($id)
    {
        return Company::find($id);
    }
}
