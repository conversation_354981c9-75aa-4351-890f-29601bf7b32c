<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\View;
use Symfony\Component\HttpFoundation\Response;

class ImpersonationMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if user is currently impersonating
        $isImpersonating = Session::get('impersonating', false);
        $impersonatorId = Session::get('impersonator_id');

        // Share impersonation state with all views
        View::share('isImpersonating', $isImpersonating);
        View::share('impersonatorId', $impersonatorId);

        // Add impersonation data to request for easy access
        $request->merge([
            'is_impersonating' => $isImpersonating,
            'impersonator_id' => $impersonatorId,
        ]);

        return $next($request);
    }
}
