<?php

namespace App\Helpers\Traits;

use App\Models\Payment;
use App\Models\User;

/**
 * Trait for handling hierarchical permission checks
 * This trait provides reusable methods for checking permissions based on user hierarchy
 */
trait HierarchicalPermissions
{
    /**
     * Check if target user is in current user's hierarchy
     */
    private function isUserInHierarchy(User $currentUser, User $targetUser): bool
    {
        $parent = $targetUser->parent;

        while ($parent) {
            if ($parent->id === $currentUser->id) {
                return true;
            }
            $parent = $parent->parent;
        }

        return false;
    }

    /**
     * Check if current user can approve payment for the payment's associated user
     */
    private function canApprovePayment(User $currentUser, Payment $payment): bool
    {
        // Ensure payment has an associated user
        if (!$payment->user) {
            return false;
        }

        $targetUser = $payment->user;

        // Super admin can approve all payments
        if ($currentUser->hasRole('super-admin')) {
            return true;
        }

        // Master reseller can approve payments for users in their hierarchy
        if ($currentUser->hasRole('master-reseller')) {
            return $this->isUserInHierarchy($currentUser, $targetUser);
        }

        // Reseller can approve payments for their direct clients only
        if ($currentUser->hasRole('reseller')) {
            return $targetUser->parent_id === $currentUser->id && $targetUser->hasRole('client');
        }

        // Clients cannot approve any payments
        return false;
    }

    /**
     * Check if current user can edit coverage for target user
     */
    private function canEditCoverage(User $currentUser, User $targetUser): bool
    {
        // Super admin has full access
        if ($currentUser->hasRole('super-admin')) {
            return true;
        }

        // Master reseller can edit coverage for their hierarchy
        if ($currentUser->hasRole('master-reseller')) {
            return $this->isUserInHierarchy($currentUser, $targetUser);
        }

        // Reseller can edit coverage for direct clients only
        if ($currentUser->hasRole('reseller')) {
            return $targetUser->parent_id === $currentUser->id;
        }

        // Clients have read-only access
        return false;
    }

    /**
     * Check if current user can edit target user
     */
    private function canEditUser(User $currentUser, User $targetUser): bool
    {
        // Users cannot edit themselves through this interface
        if ($currentUser->id === $targetUser->id) {
            return false;
        }

        // Super admin can edit anyone (except themselves)
        if ($currentUser->hasRole('super-admin')) {
            return true;
        }

        // Master reseller can edit users in their hierarchy
        if ($currentUser->hasRole('master-reseller')) {
            return $this->isUserInHierarchy($currentUser, $targetUser);
        }

        // Reseller can edit their direct clients only
        if ($currentUser->hasRole('reseller')) {
            return $targetUser->parent_id === $currentUser->id && $targetUser->hasRole('client');
        }

        // Clients cannot edit other users
        return false;
    }

    /**
     * Check if current user can manage the target user's status
     */
    private function canManageUserStatus(User $currentUser, User $targetUser): bool
    {
        // Super admin can manage anyone
        if ($currentUser->hasRole('super-admin')) {
            return true;
        }

        // Master reseller can manage resellers and clients in their hierarchy
        if ($currentUser->hasRole('master-reseller')) {
            return $this->isUserInHierarchy($currentUser, $targetUser);
        }

        // Reseller can manage their direct clients
        if ($currentUser->hasRole('reseller')) {
            return $targetUser->parent_id === $currentUser->id && $targetUser->hasRole('client');
        }

        // Clients cannot manage anyone
        return false;
    }

    /**
     * Check if current user can view recharge history for target user
     */
    private function canViewUserRechargeHistory(User $currentUser, User $targetUser): bool
    {
        // Super admin can view anyone's recharge history
        if ($currentUser->hasRole('super-admin')) {
            return true;
        }

        // Master reseller can view recharge history for their hierarchy
        if ($currentUser->hasRole('master-reseller')) {
            return $this->isUserInHierarchy($currentUser, $targetUser);
        }

        // Reseller can view recharge history for direct clients only
        if ($currentUser->hasRole('reseller')) {
            return $targetUser->parent_id === $currentUser->id;
        }

        // Users can view their own recharge history
        if ($currentUser->id === $targetUser->id) {
            return true;
        }

        // Default: no access
        return false;
    }

    /**
     * Check if current user can verify target user
     */
    private function canVerifyUser(User $currentUser, User $targetUser): bool
    {
        // Super admin can verify any user
        if ($currentUser->hasRole('super-admin')) {
            return true;
        }

        // Master reseller can verify users in their hierarchy
        if ($currentUser->hasRole('master-reseller')) {
            return $this->isUserInHierarchy($currentUser, $targetUser);
        }

        // Reseller can verify their direct clients only
        if ($currentUser->hasRole('reseller')) {
            return $targetUser->parent_id === $currentUser->id && $targetUser->hasRole('client');
        }

        // Clients cannot verify other users
        return false;
    }
}
