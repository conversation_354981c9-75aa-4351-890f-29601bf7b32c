<?php

if (!function_exists('setting')) {
    /**
     * Get a setting value
     */
    function setting($key, $default = null, $companyId = null)
    {
        return \App\Services\SettingsService::get($key, $companyId, $default);
    }
}

if (!function_exists('website_title')) {
    /**
     * Get website title
     */
    function website_title($companyId = null)
    {
        return \App\Services\SettingsService::getWebsiteTitle($companyId);
    }
}

if (!function_exists('website_logo')) {
    /**
     * Get website logo URL
     */
    function website_logo($companyId = null)
    {
        return \App\Services\SettingsService::getWebsiteLogo($companyId);
    }
}

if (!function_exists('website_favicon')) {
    /**
     * Get website favicon URL
     */
    function website_favicon($companyId = null)
    {
        return \App\Services\SettingsService::getWebsiteFavicon($companyId);
    }
}

if (!function_exists('meta_description')) {
    /**
     * Get meta description
     */
    function meta_description($companyId = null)
    {
        return \App\Services\SettingsService::getMetaDescription($companyId);
    }
}

if (!function_exists('meta_keywords')) {
    /**
     * Get meta keywords
     */
    function meta_keywords($companyId = null)
    {
        return \App\Services\SettingsService::getMetaKeywords($companyId);
    }
}

if (!function_exists('google_analytics_id')) {
    /**
     * Get Google Analytics ID
     */
    function google_analytics_id($companyId = null)
    {
        return \App\Services\SettingsService::getGoogleAnalyticsId($companyId);
    }
}
