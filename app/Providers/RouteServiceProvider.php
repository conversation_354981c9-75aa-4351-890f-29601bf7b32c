<?php

namespace App\Providers;

use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Route;
use App\Models\User;
use App\Models\Coverage;

class RouteServiceProvider extends ServiceProvider
{
    /**
     * The path to the "home" route for your application.
     *
     * Typically, users are redirected here after authentication.
     *
     * @var string
     */
    public const HOME = '/';

    /**
     * Define your route model bindings, pattern filters, and other route configuration.
     */
    public function boot(): void
    {
        $this->configureRateLimiting();
        $this->configureRouteModelBinding();

        $this->routes(function () {
            Route::middleware('api')
                ->prefix('api')
                ->group(base_path('routes/api.php'));

            Route::middleware('web')
                ->group(base_path('routes/web.php'));

            Route::middleware(['web', 'tenant'])
                ->prefix('web-api')
                ->group(base_path('routes/web-api.php'));

            Route::middleware(['web', 'tenant'])
                ->prefix('datatables')
                ->group(base_path('routes/datatables.php'));

            Route::middleware(['web', 'tenant'])
                ->group(base_path('routes/tenant.php'));
        });
    }

    /**
     * Configure route model binding scoping.
     */
    protected function configureRouteModelBinding(): void
    {
        // Scope recharge to user's accessible recharges based on hierarchy
        Route::bind('recharge', function ($value, $route) {
            // Find the recharge without tenant scope first
            $recharge = \App\Models\Recharge::withoutGlobalScope(\App\Tenant\Scopes\TenantScope::class)
                ->with(['payment', 'company', 'user'])
                ->findOrFail($value);

            // Check if current user can access this recharge
            $currentUser = auth()->user();

            // Super admin can access any recharge
            if ($currentUser->hasRole('super-admin')) {
                return $recharge;
            }

            // Master reseller can access recharges in their hierarchy
            if ($currentUser->hasRole('master-reseller')) {
                if ($this->isUserInHierarchy($currentUser, $recharge->user)) {
                    return $recharge;
                }
            }

            // Reseller can access recharges for direct clients only
            if ($currentUser->hasRole('reseller')) {
                if ($recharge->user->parent_id === $currentUser->id) {
                    return $recharge;
                }
            }

            // Users can access their own recharges
            if ($currentUser->id === $recharge->user_id) {
                return $recharge;
            }

            // If none of the above conditions are met, deny access
            abort(403, 'You do not have permission to view this recharge.');
        });

        // Scope coverage to user's company
        Route::bind('coverage', function ($value, $route) {
            // Get the user parameter - it might be a string ID or User instance
            $userParam = $route->parameter('user');

            // If user is still a string (ID), resolve it to a User model
            if (is_string($userParam) || is_numeric($userParam)) {
                $user = User::findOrFail($userParam);
            } elseif ($userParam instanceof User) {
                $user = $userParam;
            } else {
                // Fallback: find coverage without scoping
                return Coverage::findOrFail($value);
            }

            // Find coverage that belongs to the user's company
            $coverage = Coverage::where('id', $value)
                ->where('company_id', $user->company_id)
                ->first();

            if (!$coverage) {
                abort(404, 'Coverage not found or does not belong to this user\'s company');
            }

            return $coverage;
        });
    }

    /**
     * Check if target user is in current user's hierarchy
     */
    private function isUserInHierarchy($currentUser, $targetUser): bool
    {
        $parent = $targetUser->parent;

        while ($parent) {
            if ($parent->id === $currentUser->id) {
                return true;
            }
            $parent = $parent->parent;
        }

        return false;
    }

    /**
     * Configure the rate limiters for the application.
     */
    protected function configureRateLimiting(): void
    {
        RateLimiter::for('api', function (Request $request) {
            return Limit::perMinute(60)->by($request->user()?->id ?: $request->ip());
        });
    }
}
