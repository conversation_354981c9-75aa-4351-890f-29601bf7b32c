# SMSFrom API Documentation

## Table of Contents
- [Overview](#overview)
- [Authentication](#authentication)
- [Base URL](#base-url)
- [Rate Limiting](#rate-limiting)
- [Response Format](#response-format)
- [Error Codes](#error-codes)
- [API Endpoints](#api-endpoints)
  - [SMS API](#sms-api)
  - [Web API](#web-api)
  - [Authentication API](#authentication-api)
  - [Payment API](#payment-api)
- [Code Examples](#code-examples)
- [SDKs and Libraries](#sdks-and-libraries)

## Overview

SMSFrom provides a comprehensive RESTful API for sending SMS messages, managing contacts, handling payments, and administering your account. The API is designed to be simple, reliable, and scalable for businesses of all sizes.

### Key Features
- Send single and bulk SMS messages
- Schedule SMS for future delivery
- Manage sender IDs and masking numbers
- Handle contacts and groups
- Process payments and recharges
- Real-time delivery reports
- Multi-tenant support

## Authentication

The API supports two authentication methods:

### Method 1: API Key Authentication (Recommended)
Include your API key in the request parameters or headers.

**Parameter:**
```
api_key: YOUR_API_KEY
```

**Header:**
```
Authorization: Bearer YOUR_API_KEY
```

### Method 2: Username/Password Authentication
Use your account credentials for authentication.

**Parameters:**
```
username: your_username
password: your_password
```

### Getting Your API Key
1. Log in to your SMSFrom dashboard
2. Navigate to **Developers** section
3. Click **Generate API Key**
4. Copy and securely store your API key

## Base URL

All API requests should be made to:
```
https://yourdomain.com/api/
```

## Rate Limiting

API requests are rate-limited to ensure fair usage:
- **Default limit:** 100 requests per minute per API key
- **Burst limit:** 1000 requests per hour
- Rate limit headers are included in all responses

## Response Format

All API responses follow a consistent JSON format:

### Success Response
```json
{
    "response_code": "0000",
    "message": "Successfully Send message"
}
```

### Error Response
```json
{
    "response_code": "1001",
    "message": "Something error is happened."
}
```

### Validation Error Response
```json
{
    "success": false,
    "message": "Validation errors",
    "errors": {
        "phone_numbers": ["The phone numbers field is required."],
        "message": ["The message field is required."]
    }
}
```

## Error Codes

| Code | Description |
|------|-------------|
| 0000 | Successfully Send message |
| 1000 | No SMS data found |
| 1001 | Something error is happened |
| 1002 | Sender Id/Masking Not Found |
| 1003 | Invalid API key |
| 1004 | SPAM Detected |
| 1005 | Internal Error |
| 1006 | Internal Error |
| 1007 | Balance Insufficient |
| 1008 | Message is empty |
| 1009 | Message Type Not Set (text/unicode) |
| 1010 | Invalid User & Password |
| 1011 | Invalid User Id |
| 1012 | Invalid Number |
| 1013 | API limit error |
| 1014 | No matching template |
| 1015 | SMS Content Validation Fails |
| 1016 | Schedule date time format is invalid |
| 1019 | SMS Purpose Missing |
| 69   | Route mobile error |

## API Endpoints

### SMS API

#### Send SMS
Send single or bulk SMS messages.

**Endpoint:** `POST|GET /api/sms-send`

**Authentication:** Required (API Key or Username/Password)

**Parameters:**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| api_key | string | conditional | Your API key (required if username/password not provided) |
| username | string | conditional | Your username (required if api_key not provided) |
| password | string | conditional | Your password (required if api_key not provided) |
| sender_id | string | required | Sender ID or masking number |
| phone_numbers | string | required | Comma-separated phone numbers (e.g., "**********,0987654321") |
| type | string | required | Message type: "text" or "unicode" |
| message | string | required | SMS content (max 1000 characters) |
| scheduled_date_time | string | optional | Schedule SMS for future delivery (ISO 8601 format) |

**Example Request:**
```bash
POST /api/sms-send
Content-Type: application/json

{
    "api_key": "your_api_key_here",
    "sender_id": "YourBrand",
    "phone_numbers": "**********,0987654321",
    "type": "text",
    "message": "Hello! This is a test message from SMSFrom API."
}
```

**Example Response:**
```json
{
    "response_code": "0000",
    "message": "Successfully Send message"
}
```

**Scheduled SMS Example:**
```bash
POST /api/sms-send
Content-Type: application/json

{
    "api_key": "your_api_key_here",
    "sender_id": "YourBrand",
    "phone_numbers": "**********",
    "type": "text",
    "message": "Scheduled message",
    "scheduled_date_time": "2024-12-25T10:00:00Z"
}
```

**Scheduled SMS Response:**
```json
{
    "response_code": "0000",
    "message": "Successfully scheduled message"
}
```

### Web API

The Web API provides authenticated endpoints for managing your account through the web interface.

**Base URL:** `/web-api/`

**Authentication:** Session-based (requires login)

#### Make Sender Default
Set a sender ID as default for your account.

**Endpoint:** `POST /web-api/senders/{sender}/make-default`

**Parameters:**
- `sender`: Sender ID

#### Add Sender
Add a new sender ID to your account.

**Endpoint:** `POST /web-api/add-sender`

**Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| name | string | required | Sender name (unique) |
| server_id | integer | optional | Server ID |
| is_server_default | boolean | optional | Set as server default |
| required_documents | array | optional | Supporting documents |

#### Add Balance
Add balance to user account.

**Endpoint:** `POST /web-api/add-balance`

#### Delete Contact Group
Delete a contact group.

**Endpoint:** `DELETE /web-api/delete-group/{group}`

#### Get User Information
Retrieve user information.

**Endpoint:** `GET /web-api/user/{user}`

**Response:**
```json
{
    "success": true,
    "data": {
        "id": 1,
        "name": "John Doe",
        "email": "<EMAIL>",
        "phone": "**********",
        "created_at": "2024-01-01T00:00:00Z"
    }
}
```

### Authentication API

Standard Laravel authentication endpoints.

**Base URL:** `/`

#### Login
**Endpoint:** `POST /login`

**Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| email | string | required | User email |
| password | string | required | User password |
| remember | boolean | optional | Remember login |

#### Register
**Endpoint:** `POST /register`

**Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| name | string | required | Full name |
| email | string | required | Email address |
| password | string | required | Password (min 8 characters) |
| password_confirmation | string | required | Password confirmation |

#### Logout
**Endpoint:** `POST /logout`

#### Password Reset
**Endpoint:** `POST /forgot-password`

**Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| email | string | required | Email address |

### Payment API

Handle payment processing and recharges.

**Base URL:** `/payment/`

**Authentication:** Required (session-based)

#### Initiate Payment
**Endpoint:** `POST /payment/pay`

#### Payment Success
**Endpoint:** `GET|POST /payment/success`

#### Payment Failure
**Endpoint:** `GET|POST /payment/fail`

#### Payment Cancel
**Endpoint:** `GET|POST /payment/cancel`

#### Payment IPN (Instant Payment Notification)
**Endpoint:** `GET|POST /payment/ipn`

## Code Examples

### PHP Example
```php
<?php

$apiKey = 'your_api_key_here';
$senderID = 'YourBrand';
$phoneNumbers = '**********,0987654321';
$message = 'Hello from SMSFrom API!';

$data = [
    'api_key' => $apiKey,
    'sender_id' => $senderID,
    'phone_numbers' => $phoneNumbers,
    'type' => 'text',
    'message' => $message
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://yourdomain.com/api/sms-send');
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
curl_close($ch);

$result = json_decode($response, true);
echo "Response: " . $result['message'];
?>
```

### Python Example
```python
import requests
import json

url = 'https://yourdomain.com/api/sms-send'
data = {
    'api_key': 'your_api_key_here',
    'sender_id': 'YourBrand',
    'phone_numbers': '**********,0987654321',
    'type': 'text',
    'message': 'Hello from SMSFrom API!'
}

headers = {
    'Content-Type': 'application/json'
}

response = requests.post(url, data=json.dumps(data), headers=headers)
result = response.json()

print(f"Response: {result['message']}")
```

### JavaScript Example
```javascript
const apiKey = 'your_api_key_here';
const senderID = 'YourBrand';
const phoneNumbers = '**********,0987654321';
const message = 'Hello from SMSFrom API!';

const data = {
    api_key: apiKey,
    sender_id: senderID,
    phone_numbers: phoneNumbers,
    type: 'text',
    message: message
};

fetch('https://yourdomain.com/api/sms-send', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify(data)
})
.then(response => response.json())
.then(result => {
    console.log('Response:', result.message);
})
.catch(error => {
    console.error('Error:', error);
});
```

### cURL Example
```bash
curl -X POST https://yourdomain.com/api/sms-send \
  -H "Content-Type: application/json" \
  -d '{
    "api_key": "your_api_key_here",
    "sender_id": "YourBrand",
    "phone_numbers": "**********,0987654321",
    "type": "text",
    "message": "Hello from SMSFrom API!"
  }'
```

## SDKs and Libraries

We provide official SDKs for popular programming languages:

- **PHP SDK**: Coming soon
- **Python SDK**: Coming soon
- **Node.js SDK**: Coming soon
- **Java SDK**: Coming soon

## Support

For API support and questions:
- **Documentation**: This document
- **Email**: <EMAIL>
- **GitHub Issues**: [Create an issue](https://github.com/yourusername/smsfrom/issues)

## Changelog

### Version 1.0.0
- Initial API release
- SMS sending functionality
- Authentication system
- Payment integration
- Multi-tenant support

---

**Last Updated:** December 2024
**API Version:** 1.0.0
