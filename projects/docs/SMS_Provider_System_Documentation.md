# SMS Provider Application System Documentation

## System Overview

This is a comprehensive SMS provider application built with <PERSON><PERSON> that supports a hierarchical multi-tenant architecture with automated balance management and intelligent SMS routing. The system manages different user types with varying permissions and implements sophisticated pricing and billing mechanisms.

## User Hierarchy & Roles

### 1. User Types and Hierarchy
The system implements a 4-level hierarchical structure:

```
Super Admin (Root Level)
├── Master Reseller (Level 1)
│   ├── Reseller (Level 2)
│   │   └── Client (Level 3)
│   └── Client (Direct under Master Reseller)
└── Client (Direct under Super Admin)
```

### 2. Role-Based Permissions
- **Super Admin**: Full system access, can manage all users and settings
- **Master Reseller**: Can create resellers and clients, manage coverage pricing, handle support tickets
- **Reseller**: Can create clients, manage coverage pricing, send SMS
- **Client**: Can only send SMS and manage sender IDs

### 3. User-Company Relationship
- Each user belongs to a company (`users.company_id`)
- Users have parent-child relationships (`users.parent_id`)
- Companies have hierarchical relationships (`companies.company_id` points to parent company)

## SMS Pricing System

### 1. Two Types of SMS Pricing

#### Masking SMS
- Uses branded sender IDs (company names, brands)
- Pricing stored in `coverages.masking_price`
- Server routing determined by sender's assigned server (`senders.server_id`)
- Each masking sender ID can only use one specific SMS provider/server

#### Non-Masking SMS  
- Uses numeric sender IDs
- Pricing stored in `coverages.non_masking_price`
- Server routing determined by coverage table (`coverages.server_id`)
- Can use multiple servers based on phone number prefix

### 2. Coverage Table Structure
```sql
coverages:
- company_id (identifies which company's pricing)
- prefix (5-digit phone number prefix)
- masking_price (price for masking SMS)
- non_masking_price (price for non-masking SMS)
- operator (telecom operator)
- server_id (which SMS gateway to use for this prefix)
```

## SMS Routing Logic

### 1. Masking SMS Routing
```php
if ($sender->is_masking) {
    $server_id = $sender->server_id ?? $coverage['server_id'];
}
```
- Uses the server assigned to the sender ID
- Falls back to coverage table server if sender has no assigned server

### 2. Non-Masking SMS Routing
```php
if (!$sender->is_masking) {
    // Use default sender for the coverage server
    $defaultSender = Sender::where('is_server_default', 1)
                          ->where('server_id', $coverage['server_id'])
                          ->first();
    $sender_id = $defaultSender->id;
}
```
- Uses server specified in coverage table for the phone prefix
- Automatically selects default sender for that server

## Balance Management System

### 1. Hierarchical Balance Deduction
When a client sends SMS, the system deducts balance from ALL levels in the hierarchy:

```php
// Example: Client sends SMS costing 50 paisa
// 1. Client account: deduct 50 paisa
// 2. Reseller account: deduct 45 paisa (their rate)
// 3. Master Reseller: deduct 40 paisa (their rate)
// Super Admin: no deduction
```

### 2. Balance Deduction Process
```php
public function deductBalanceRecursively($companies, Message $message)
{
    $prefix = substr($message->phone_number, 0, 5);
    $price = $message->sender->is_masking ? 'masking_price' : 'non_masking_price';
    
    Company::whereIn('id', $companies)->update([
        'current_balance' => DB::raw("current_balance - (
            {$message->sms_count} * 
            (SELECT {$price} FROM coverages WHERE coverages.company_id = companies.id AND prefix = '{$prefix}' LIMIT 1)
        )"),
    ]);
}
```

### 3. Company Hierarchy Traversal
```php
// Get all parent companies for balance deduction
$companies = [$this->message->company->id];
$parent = $this->message->company->parent;

if (isset($parent)) {
    Format::getRecursiveParentIds($this->message->company->parent, $companies);
}
```

## Transaction & Audit System

### 1. Automatic Transaction Recording
- Database trigger automatically creates transaction records when company balance changes
- Observer pattern also tracks balance changes
- Tracks both incoming (recharge) and outgoing (SMS cost) transactions

### 2. Transaction Structure
```sql
transactions:
- user_id
- company_id  
- amount_in (for recharges)
- amount_out (for SMS costs)
- date
- remarks
- log
```

## SMS Processing Workflow

### 1. Message Storage Process
1. Validate phone numbers
2. Get coverage data for pricing and routing
3. Determine sender and server based on masking type
4. Calculate SMS cost based on character count and pricing
5. Store message with batch number for tracking
6. Queue for immediate or scheduled sending

### 2. Message Sending Process
1. Retrieve message with sender and server details
2. Send via appropriate SMS gateway
3. Update message status based on response
4. If successful, trigger hierarchical balance deduction
5. Record transaction for audit trail

## Key Database Tables

### Core Tables
- `users` - User accounts with hierarchy
- `companies` - Company/tenant data with balance
- `messages` - SMS records
- `senders` - Sender IDs (masking/non-masking)
- `servers` - SMS gateway configurations
- `coverages` - Pricing and routing by prefix
- `transactions` - Balance change audit trail

### Relationship Tables
- `company_user` - Many-to-many user-company relationships
- `groups` - Contact groups for bulk messaging
- `contacts` - Individual contacts within groups

## Multi-Tenancy Implementation

### 1. Tenant Isolation
- Uses `ForTenants` trait for automatic company_id scoping
- Tenant middleware identifies current company context
- Global scopes ensure data segregation

### 2. Pricing Customization
- Each company can have custom pricing in coverage table
- Pricing varies by phone prefix and SMS type
- Supports different rates for each level in hierarchy

This system provides a robust, scalable SMS platform with sophisticated billing, routing, and multi-tenancy capabilities suitable for SMS reseller businesses.
