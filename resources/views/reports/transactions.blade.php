@extends('layouts.app')

@section('title', 'Transaction Ledger Report')

@section('css')
<style>
.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
}
.card-title {
    font-weight: 600;
    margin-bottom: 1rem;
}
.table th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
}

/* Transaction type styling */
.transaction-credit {
    background-color: rgba(40, 167, 69, 0.1) !important;
    color: #28a745 !important;
    border-left: 4px solid #28a745;
}
.transaction-debit {
    background-color: rgba(220, 53, 69, 0.1) !important;
    color: #dc3545 !important;
    border-left: 4px solid #dc3545;
}
.transaction-amount {
    font-weight: 600;
    font-size: 0.95rem;
}
.transaction-icon {
    font-size: 1.1rem;
    margin-right: 0.5rem;
}

/* Layout improvements */
.card-toolbar .d-flex {
    gap: 1rem;
}
.card-toolbar .form-label {
    white-space: nowrap;
    font-size: 0.875rem;
}
.card-toolbar .btn {
    white-space: nowrap;
}
#exportBtn {
    background-color: #198754;
    border-color: #198754;
}
#exportBtn:hover {
    background-color: #157347;
    border-color: #146c43;
}

/* Optimized compact toolbar layout */
.card-toolbar [data-kt-user-table-toolbar="base"] {
    gap: 0.5rem;
}

.card-toolbar .form-select-sm,
.card-toolbar .form-control-sm {
    font-size: 0.875rem;
    border-radius: 0.375rem;
    height: 32px;
}

.card-toolbar .text-nowrap {
    white-space: nowrap;
}

.card-toolbar .btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

/* Large screens - show labels */
@media (min-width: 1024px) {
    .card-toolbar [data-kt-user-table-toolbar="base"] {
        flex-wrap: nowrap;
        gap: 0.75rem;
    }

    #userRoleFilter {
        width: 110px !important;
    }

    #companyFilter {
        width: 120px !important;
    }

    #transactionTypeFilter {
        width: 100px !important;
    }

    #dateFrom,
    #dateTo {
        width: 110px !important;
    }
}

/* Medium screens - hide labels, compact layout */
@media (max-width: 1023px) and (min-width: 768px) {
    .card-toolbar [data-kt-user-table-toolbar="base"] {
        gap: 0.5rem;
    }

    #userRoleFilter,
    #companyFilter,
    #transactionTypeFilter {
        width: 100px !important;
    }

    #dateFrom,
    #dateTo {
        width: 95px !important;
    }
}

/* Small screens - allow wrapping */
@media (max-width: 767px) {
    .card-toolbar [data-kt-user-table-toolbar="base"] {
        gap: 0.4rem;
        justify-content: flex-start !important;
    }

    #userRoleFilter,
    #companyFilter,
    #transactionTypeFilter {
        width: 90px !important;
    }

    #dateFrom,
    #dateTo {
        width: 85px !important;
    }

    .card-toolbar .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
    }
}

/* Extra small screens - very compact */
@media (max-width: 575px) {
    .card-toolbar [data-kt-user-table-toolbar="base"] {
        gap: 0.3rem;
        flex-wrap: wrap;
    }

    #userRoleFilter,
    #companyFilter,
    #transactionTypeFilter,
    #dateFrom,
    #dateTo {
        width: 80px !important;
    }

    .card-toolbar .btn-sm {
        padding: 0.25rem 0.4rem;
        font-size: 0.75rem;
    }
}
</style>
@endsection

@section('content')
        <!--begin::Container-->
        <div class="container-xxl" id="kt_content_container">
        <!--begin::Card-->
        <div class="card">
            <!--begin::Card header-->
            <div class="card-header border-0 pt-6">
                <!--begin::Card title-->
                <div class="card-title">
                    <h3 class="card-title align-items-start flex-column">
                        <span class="card-label fw-bold fs-3 mb-1">Account Balance History</span>
                        <span class="text-muted mt-1 fw-semibold fs-7">Track all account balance changes and financial transactions</span>
                    </h3>
                </div>
                <!--end::Card title-->
                <!--begin::Card toolbar-->
                <div class="card-toolbar">
                    <!--begin::Toolbar-->
                    <div class="d-flex align-items-center justify-content-start flex-wrap w-100 gap-2" data-kt-user-table-toolbar="base">

                        <!--begin::Role Filter-->
                        <div class="d-flex align-items-center">
                            <label class="form-label me-1 mb-0 fw-semibold text-nowrap d-none d-lg-block">Role:</label>
                            <select id="userRoleFilter" class="form-select form-select-sm" style="width: 110px;">
                                <option value="all">All Roles</option>
                            </select>
                        </div>
                        <!--end::Role Filter-->

                        <!--begin::Account Filter-->
                        <div class="d-flex align-items-center">
                            <label class="form-label me-1 mb-0 fw-semibold text-nowrap d-none d-lg-block">Account:</label>
                            <select id="companyFilter" class="form-select form-select-sm" style="width: 120px;">
                                <option value="all">All Accounts</option>
                            </select>
                        </div>
                        <!--end::Account Filter-->

                        <!--begin::Transaction Type Filter-->
                        <div class="d-flex align-items-center">
                            <label class="form-label me-1 mb-0 fw-semibold text-nowrap d-none d-lg-block">Type:</label>
                            <select id="transactionTypeFilter" class="form-select form-select-sm" style="width: 100px;">
                                <option value="all">All Types</option>
                                <option value="credit">Credits</option>
                                <option value="debit">Debits</option>
                            </select>
                        </div>
                        <!--end::Transaction Type Filter-->

                        <!--begin::From Date-->
                        <div class="d-flex align-items-center">
                            <label class="form-label me-1 mb-0 fw-semibold text-nowrap d-none d-lg-block">From:</label>
                            <input type="date" id="dateFrom" class="form-control form-control-sm"
                                   style="width: 110px;"
                                   value="{{ date('Y-m-d', strtotime('-30 days')) }}">
                        </div>
                        <!--end::From Date-->

                        <!--begin::To Date-->
                        <div class="d-flex align-items-center">
                            <label class="form-label me-1 mb-0 fw-semibold text-nowrap d-none d-lg-block">To:</label>
                            <input type="date" id="dateTo" class="form-control form-control-sm"
                                   style="width: 110px;"
                                   value="{{ date('Y-m-d') }}">
                        </div>
                        <!--end::To Date-->



                        <!--begin::Apply Filters Button-->
                        <button type="button" id="filterBtn" class="btn btn-primary btn-sm">
                            <i class="fas fa-filter"></i>
                            <span class="d-none d-md-inline">Apply</span>
                        </button>
                        <!--end::Apply Filters Button-->

                        <!--begin::Export CSV Button-->
                        <button type="button" id="exportBtn" class="btn btn-success btn-sm">
                            <i class="fas fa-download"></i>
                            <span class="d-none d-md-inline">Export</span>
                        </button>
                        <!--end::Export CSV Button-->
                    </div>
                    <!--end::Toolbar-->
                </div>
                <!--end::Card toolbar-->
            </div>
            <!--end::Card header-->

            <div class="card-body py-4">
                <!--begin::Table-->
                <table class="table align-middle table-row-dashed fs-6 gy-5" id="kt_table_transactions">
                    <thead>
                        <tr class="text-start text-muted fw-bold fs-7 text-uppercase gs-0">
                            <th class="min-w-50px">SL</th>
                            <th class="min-w-150px">Date</th>
                            @if(auth()->user()->hasAnyRole(['super-admin', 'master-reseller', 'reseller']))
                            <th class="min-w-120px">Account</th>
                            @endif
                            <th class="min-w-100px">Transaction Type</th>
                            <th class="min-w-100px">Amount</th>
                            <th class="min-w-150px">Description</th>
                            <th class="min-w-100px">Balance Impact</th>
                            @if(auth()->user()->hasAnyRole(['super-admin', 'master-reseller', 'reseller']))
                            <th class="min-w-120px">Balance After</th>
                            @endif
                        </tr>
                    </thead>
                </table>
                <!--end::Table-->
            </div>
        </div>
        <!--end::Card-->
    </div>
    <!--end::Container-->
@stop

@section('js')
<script>
// Set user role information for DataTables configuration
window.userHasCompanyColumn = {{ auth()->user()->hasAnyRole(['super-admin', 'master-reseller', 'reseller']) ? 'true' : 'false' }};

// Enhanced filtering and export functionality
$(document).ready(function() {
    // Load filter options on page load
    loadFilterOptions();

    // Handle responsive placeholders
    handleResponsivePlaceholders();
    $(window).resize(handleResponsivePlaceholders);

    // Export functionality
    $('#exportBtn').on('click', function() {
        const dateFrom = $('#dateFrom').val();
        const dateTo = $('#dateTo').val();
        const userRole = $('#userRoleFilter').val();
        const companyId = $('#companyFilter').val();
        const transactionType = $('#transactionTypeFilter').val();

        // Build export URL with current filters
        let exportUrl = '/reports/export-transactions?';
        const params = new URLSearchParams();

        if (dateFrom) params.append('date_from', dateFrom);
        if (dateTo) params.append('date_to', dateTo);
        if (userRole && userRole !== 'all') params.append('user_role', userRole);
        if (companyId && companyId !== 'all') params.append('company_id', companyId);
        if (transactionType && transactionType !== 'all') params.append('transaction_type', transactionType);

        exportUrl += params.toString();

        // Show loading state
        const originalText = $(this).html();
        $(this).html('<i class="fas fa-spinner fa-spin"></i> Exporting...');
        $(this).prop('disabled', true);

        // Use window.open to trigger download
        try {
            console.log('Export URL:', exportUrl);
            const newWindow = window.open(exportUrl, '_blank');

            // Check if popup was blocked
            if (!newWindow || newWindow.closed || typeof newWindow.closed == 'undefined') {
                // Popup was blocked, try alternative method
                console.log('Popup blocked, trying alternative method');
                window.location.href = exportUrl;
            }

            // Reset button state after a short delay
            setTimeout(() => {
                $(this).html(originalText);
                $(this).prop('disabled', false);
            }, 2000);
        } catch (error) {
            console.error('Export error:', error);
            alert('Export failed. Please try again. Error: ' + error.message);

            // Reset button state immediately on error
            $(this).html(originalText);
            $(this).prop('disabled', false);
        }
    });

    // Handle responsive placeholders
    function handleResponsivePlaceholders() {
        const isSmallScreen = window.innerWidth < 768;

        if (isSmallScreen) {
            // Update dropdown placeholders for small screens
            updateSelectPlaceholder('#userRoleFilter', 'Role', 'All Roles');
            updateSelectPlaceholder('#companyFilter', 'Account', 'All Accounts');
            updateSelectPlaceholder('#transactionTypeFilter', 'Type', 'All Types');
        } else {
            // Restore original text for larger screens
            updateSelectPlaceholder('#userRoleFilter', 'All Roles', 'All Roles');
            updateSelectPlaceholder('#companyFilter', 'All Accounts', 'All Accounts');
            updateSelectPlaceholder('#transactionTypeFilter', 'All Types', 'All Types');
        }
    }

    // Helper function to update select placeholder
    function updateSelectPlaceholder(selector, placeholderText, defaultText) {
        const $select = $(selector);
        const $defaultOption = $select.find('option[value="all"]');

        if ($defaultOption.length) {
            $defaultOption.text(placeholderText);
        }
    }

    // Load filter options function
    function loadFilterOptions() {
        $.ajax({
            url: '/datatables/filter-options',
            method: 'GET',
            success: function(data) {
                // Populate role filter
                const roleFilter = $('#userRoleFilter');
                roleFilter.empty().append('<option value="all">All Roles</option>');
                $.each(data.roles, function(key, value) {
                    roleFilter.append('<option value="' + key + '">' + value + '</option>');
                });

                // Populate company filter
                const companyFilter = $('#companyFilter');
                companyFilter.empty().append('<option value="all">All Accounts</option>');
                $.each(data.companies, function(key, value) {
                    companyFilter.append('<option value="' + key + '">' + value + '</option>');
                });

                // Apply responsive placeholders after loading options
                handleResponsivePlaceholders();
            },
            error: function(xhr, status, error) {
                console.error('Failed to load filter options:', error);
            }
        });
    }
});
</script>
@stop
