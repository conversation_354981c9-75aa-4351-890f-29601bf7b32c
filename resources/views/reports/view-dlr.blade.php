@extends('layouts.app')

@section('title', 'Reports & Statistics')

@section('css')
<style>
.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
}
.modal-lg {
    max-width: 900px;
}
.card-title {
    font-weight: 600;
    margin-bottom: 1rem;
}
.table th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
}
.view-details-btn {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}
pre {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;
    padding: 0.75rem;
    font-size: 0.875rem;
    line-height: 1.5;
}
.bg-light-primary {
    background-color: rgba(54, 162, 235, 0.1) !important;
}
.bg-light-success {
    background-color: rgba(75, 192, 192, 0.1) !important;
}
.bg-light-info {
    background-color: rgba(255, 206, 86, 0.1) !important;
}
.bg-light-warning {
    background-color: rgba(255, 159, 64, 0.1) !important;
}
.bg-light-secondary {
    background-color: rgba(153, 102, 255, 0.1) !important;
}
.text-primary { color: #007bff !important; }
.text-success { color: #28a745 !important; }
.text-info { color: #17a2b8 !important; }
.text-warning { color: #ffc107 !important; }
.text-secondary { color: #6c757d !important; }

/* Layout improvements */
.card-toolbar .d-flex {
    gap: 1rem;
}
.card-toolbar .form-label {
    white-space: nowrap;
    font-size: 0.875rem;
}
.card-toolbar .btn {
    white-space: nowrap;
}
#exportBtn {
    background-color: #198754;
    border-color: #198754;
}
#exportBtn:hover {
    background-color: #157347;
    border-color: #146c43;
}

/* Optimized compact toolbar layout */
.card-toolbar [data-kt-user-table-toolbar="base"] {
    gap: 0.5rem;
}

.card-toolbar .form-select-sm,
.card-toolbar .form-control-sm {
    font-size: 0.875rem;
    border-radius: 0.375rem;
    height: 32px;
}

.card-toolbar .text-nowrap {
    white-space: nowrap;
}

.card-toolbar .btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

/* Large screens - show labels */
@media (min-width: 1024px) {
    .card-toolbar [data-kt-user-table-toolbar="base"] {
        flex-wrap: nowrap;
        gap: 0.75rem;
    }

    #searchInput {
        width: 200px !important;
    }

    #userRoleFilter {
        width: 110px !important;
    }

    #companyFilter {
        width: 120px !important;
    }

    #dateFrom,
    #dateTo {
        width: 110px !important;
    }
}

/* Medium screens - hide labels, compact layout */
@media (max-width: 1023px) and (min-width: 768px) {
    .card-toolbar [data-kt-user-table-toolbar="base"] {
        gap: 0.5rem;
    }

    #searchInput {
        width: 180px !important;
    }

    #userRoleFilter,
    #companyFilter {
        width: 100px !important;
    }

    #dateFrom,
    #dateTo {
        width: 95px !important;
    }
}

/* Small screens - allow wrapping */
@media (max-width: 767px) {
    .card-toolbar [data-kt-user-table-toolbar="base"] {
        gap: 0.4rem;
        justify-content: flex-start !important;
    }

    #searchInput {
        width: 160px !important;
    }

    #userRoleFilter,
    #companyFilter {
        width: 90px !important;
    }

    #dateFrom,
    #dateTo {
        width: 85px !important;
    }

    .card-toolbar .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
    }
}

/* Extra small screens - very compact */
@media (max-width: 575px) {
    .card-toolbar [data-kt-user-table-toolbar="base"] {
        gap: 0.3rem;
        flex-wrap: wrap;
    }

    #searchInput {
        width: 140px !important;
    }

    #userRoleFilter,
    #companyFilter,
    #dateFrom,
    #dateTo {
        width: 80px !important;
    }

    .card-toolbar .btn-sm {
        padding: 0.25rem 0.4rem;
        font-size: 0.75rem;
    }
}
</style>
@endsection

@section('content')
        <!--begin::Container-->
        <div class="container-xxl" id="kt_content_container">
        <!--begin::Card-->
        <div class="card">
            <!--begin::Card header-->
            <div class="card-header border-0 pt-6">
                <!--begin::Card title-->
                <div class="card-title">
                    <h3 class="card-title align-items-start flex-column">
                        <span class="card-label fw-bold fs-3 mb-1">SMS Delivery Report</span>
                        <span class="text-muted mt-1 fw-semibold fs-7">Comprehensive SMS delivery status and details</span>
                    </h3>
                </div>
                <!--end::Card title-->
                <!--begin::Card toolbar-->
                <div class="card-toolbar">
                    <!--begin::Toolbar-->
                    <div class="d-flex align-items-center justify-content-start flex-wrap w-100 gap-2" data-kt-user-table-toolbar="base">
                        <!--begin::Search Input-->
                        <div class="d-flex align-items-center position-relative">
                            <span class="svg-icon svg-icon-1 position-absolute ms-6">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2" rx="1" transform="rotate(45 17.0365 15.1223)" fill="currentColor"/>
                                    <path d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z" fill="currentColor"/>
                                </svg>
                            </span>
                            <input id="searchInput" type="text" data-kt-user-table-filter="search"
                                   class="form-control form-control-solid ps-14"
                                   style="width: 200px;"
                                   placeholder="Search..."/>
                        </div>
                        <!--end::Search Input-->

                        <!--begin::Role Filter-->
                        <div class="d-flex align-items-center">
                            <label class="form-label me-1 mb-0 fw-semibold text-nowrap d-none d-lg-block">Role:</label>
                            <select id="userRoleFilter" class="form-select form-select-sm" style="width: 110px;">
                                <option value="all">All Roles</option>
                            </select>
                        </div>
                        <!--end::Role Filter-->

                        <!--begin::Account Filter-->
                        <div class="d-flex align-items-center">
                            <label class="form-label me-1 mb-0 fw-semibold text-nowrap d-none d-lg-block">Account:</label>
                            <select id="companyFilter" class="form-select form-select-sm" style="width: 120px;">
                                <option value="all">All Accounts</option>
                            </select>
                        </div>
                        <!--end::Account Filter-->

                        <!--begin::From Date-->
                        <div class="d-flex align-items-center">
                            <label class="form-label me-1 mb-0 fw-semibold text-nowrap d-none d-lg-block">From:</label>
                            <input type="date" id="dateFrom" class="form-control form-control-sm"
                                   style="width: 110px;"
                                   value="{{ date('Y-m-d') }}">
                        </div>
                        <!--end::From Date-->

                        <!--begin::To Date-->
                        <div class="d-flex align-items-center">
                            <label class="form-label me-1 mb-0 fw-semibold text-nowrap d-none d-lg-block">To:</label>
                            <input type="date" id="dateTo" class="form-control form-control-sm"
                                   style="width: 110px;"
                                   value="{{ date('Y-m-d') }}">
                        </div>
                        <!--end::To Date-->

                        <!--begin::Apply Filters Button-->
                        <button type="button" id="filterBtn" class="btn btn-primary btn-sm">
                            <i class="fas fa-filter"></i>
                            <span class="d-none d-md-inline">Apply</span>
                        </button>
                        <!--end::Apply Filters Button-->

                        <!--begin::Export CSV Button-->
                        <button type="button" id="exportBtn" class="btn btn-success btn-sm">
                            <i class="fas fa-download"></i>
                            <span class="d-none d-md-inline">Export</span>
                        </button>
                        <!--end::Export CSV Button-->
                    </div>
                    <!--end::Toolbar-->
                </div>
                <!--end::Card toolbar-->
            </div>
            <!--end::Card header-->

            <div class="card-body py-4">
                <!--begin::Table-->
                <table class="table align-middle table-row-dashed fs-6 gy-5" id="kt_table_today-details">
                    <thead>
                        <tr class="text-start text-muted fw-bold fs-7 text-uppercase gs-0">
                            <th class="min-w-50px">SL</th>
                            <th class="min-w-150px">Sent Time</th>
                            <th class="min-w-100px">Sender</th>
                            @if(auth()->user()->hasAnyRole(['super-admin', 'master-reseller', 'reseller']))
                            <th class="min-w-120px">Company</th>
                            @endif
                            <th class="min-w-100px">Schedule Status</th>
                            <th class="min-w-100px">Charge/SMS</th>
                            <th class="min-w-80px">Total Sent</th>
                            <th class="min-w-100px">Total Cost</th>
                            <th class="min-w-100px">API Status</th>
                            <th class="min-w-100px text-end">Actions</th>
                        </tr>
                    </thead>
                </table>
                <!--end::Table-->
            </div>
        </div>
        <!--end::Card-->
    </div>
    <!--end::Container-->

    <!--begin::SMS Details Modal-->
    <div class="modal fade" id="smsDetailsModal" tabindex="-1" aria-labelledby="smsDetailsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="smsDetailsModalLabel">
                        <i class="fas fa-sms me-2"></i>SMS Details
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="smsDetailsContent">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Loading SMS details...</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
    <!--end::SMS Details Modal-->
@stop

@section('js')
<script>
// Set user role information for DataTables configuration
window.userHasCompanyColumn = {{ auth()->user()->hasAnyRole(['super-admin', 'master-reseller', 'reseller']) ? 'true' : 'false' }};

// Enhanced filtering and export functionality
$(document).ready(function() {
    // Load filter options on page load
    loadFilterOptions();

    // Handle responsive placeholders
    handleResponsivePlaceholders();
    $(window).resize(handleResponsivePlaceholders);

    // Export functionality
    $('#exportBtn').on('click', function() {
        const dateFrom = $('#dateFrom').val();
        const dateTo = $('#dateTo').val();
        const search = $('#searchInput').val();
        const userRole = $('#userRoleFilter').val();
        const companyId = $('#companyFilter').val();

        // Build export URL with current filters
        let exportUrl = '/reports/export-dlr?';
        const params = new URLSearchParams();

        if (dateFrom) params.append('date_from', dateFrom);
        if (dateTo) params.append('date_to', dateTo);
        if (search) params.append('search', search);
        if (userRole && userRole !== 'all') params.append('user_role', userRole);
        if (companyId && companyId !== 'all') params.append('company_id', companyId);

        exportUrl += params.toString();

        // Show loading state
        const originalText = $(this).html();
        $(this).html('<i class="fas fa-spinner fa-spin"></i> Exporting...');
        $(this).prop('disabled', true);

        // Use window.open to trigger download
        try {
            console.log('Export URL:', exportUrl);
            const newWindow = window.open(exportUrl, '_blank');

            // Check if popup was blocked
            if (!newWindow || newWindow.closed || typeof newWindow.closed == 'undefined') {
                // Popup was blocked, try alternative method
                console.log('Popup blocked, trying alternative method');
                window.location.href = exportUrl;
            }

            // Reset button state after a short delay
            setTimeout(() => {
                $(this).html(originalText);
                $(this).prop('disabled', false);
            }, 2000);
        } catch (error) {
            console.error('Export error:', error);
            alert('Export failed. Please try again. Error: ' + error.message);

            // Reset button state immediately on error
            $(this).html(originalText);
            $(this).prop('disabled', false);
        }
    });

    // Handle responsive placeholders
    function handleResponsivePlaceholders() {
        const isSmallScreen = window.innerWidth < 768;

        if (isSmallScreen) {
            // Update dropdown placeholders for small screens
            updateSelectPlaceholder('#userRoleFilter', 'Select Role', 'All Roles');
            updateSelectPlaceholder('#companyFilter', 'Select Account', 'All Accounts');
        } else {
            // Restore original text for larger screens
            updateSelectPlaceholder('#userRoleFilter', 'All Roles', 'All Roles');
            updateSelectPlaceholder('#companyFilter', 'All Accounts', 'All Accounts');
        }
    }

    // Helper function to update select placeholder
    function updateSelectPlaceholder(selector, placeholderText, defaultText) {
        const $select = $(selector);
        const $defaultOption = $select.find('option[value="all"]');

        if ($defaultOption.length) {
            $defaultOption.text(placeholderText);
        }
    }

    // Load filter options function
    function loadFilterOptions() {
        $.ajax({
            url: '/datatables/filter-options',
            method: 'GET',
            success: function(data) {
                // Populate role filter
                const roleFilter = $('#userRoleFilter');
                roleFilter.empty().append('<option value="all">All Roles</option>');
                $.each(data.roles, function(key, value) {
                    roleFilter.append('<option value="' + key + '">' + value + '</option>');
                });

                // Populate company filter
                const companyFilter = $('#companyFilter');
                companyFilter.empty().append('<option value="all">All Accounts</option>');
                $.each(data.companies, function(key, value) {
                    companyFilter.append('<option value="' + key + '">' + value + '</option>');
                });

                // Apply responsive placeholders after loading options
                handleResponsivePlaceholders();
            },
            error: function(xhr, status, error) {
                console.error('Failed to load filter options:', error);
            }
        });
    }
});
</script>
@stop
