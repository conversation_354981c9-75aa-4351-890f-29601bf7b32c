@extends('layouts.app')

@section('content')
    <!--begin::Container-->
    <div class="container-xxl" id="kt_content_container">
        @include('account.navbar')

        <div class="row">
            <!--begin::Col-->
            <div class="col-xxl-12">
                <!--begin::Security summary-->
                <div class="card mb-5 mb-lg-10">
                    <!--begin::Card header-->
                    <div class="card-header">
                        <!--begin::Heading-->
                        <div class="card-title">
                            <h3>Security Summary</h3>
                        </div>
                        <!--end::Heading-->
                        <!--end::Title-->
                        <!--begin::Toolbar-->
                        <div class="card-toolbar">
                            <div class="my-1 me-4">
                                {{--    <!--begin::Select-->
                                    <select class="form-select form-select-sm form-select-solid w-125px"
                                            data-control="select2" data-placeholder="Select Hours" data-hide-search="true">
                                        <option value="1" selected="selected">1 Hours</option>
                                        <option value="2">6 Hours</option>
                                        <option value="3">12 Hours</option>
                                        <option value="4">24 Hours</option>
                                    </select>
                                    <!--end::Select-->--}}
                            </div>
                            {{--  <a href="#" class="btn btn-sm btn-primary my-1">View All</a>--}}
                        </div>
                        <!--end::Toolbar-->
                    </div>
                    <!--end::Header-->
                    <!--begin::Body-->
                    <div class="card-body pt-7 pb-0 px-0">
                        <div class="row p-0 mb-5 px-9">
                            <!--begin::Col-->
                            <div class="col">
                                <div
                                    class="border border-dashed border-gray-300 text-center min-w-125px rounded pt-4 pb-2 my-3">
                                    <span class="fs-4 fw-semibold text-success d-block">User Sign-in</span>
                                    <span class="fs-2hx fw-bold text-gray-900" data-kt-countup="true"
                                          data-kt-countup-value="25">0</span>
                                </div>
                            </div>
                            <!--end::Col-->
                            <!--begin::Col-->
                            <div class="col">
                                <div
                                    class="border border-dashed border-gray-300 text-center min-w-125px rounded pt-4 pb-2 my-3">
                                    <span class="fs-4 fw-semibold text-primary d-block">Admin Sign-in</span>
                                    <span class="fs-2hx fw-bold text-gray-900" data-kt-countup="true"
                                          data-kt-countup-value="1">0</span>
                                </div>
                            </div>
                            <!--end::Col-->
                            <!--begin::Col-->
                            <div class="col">
                                <div
                                    class="border border-dashed border-gray-300 text-center min-w-125px rounded pt-4 pb-2 my-3">
                                    <span class="fs-4 fw-semibold text-danger d-block">Failed Attempts</span>
                                    <span class="fs-2hx fw-bold text-gray-900" data-kt-countup="true"
                                          data-kt-countup-value="1">0</span>
                                </div>
                            </div>
                            <!--end::Col-->
                        </div>
                    </div>
                    <!--end::Body-->
                </div>
                <!--end::Security summary-->
            </div>
            <!--end::Col-->
        </div>
        <!--begin::Login sessions-->
        <div class="card mb-5 mb-lg-10">
            <!--begin::Card header-->
            <div class="card-header">
                <!--begin::Heading-->
                <div class="card-title">
                    <h3>Login Sessions</h3>
                </div>
                <!--end::Heading-->
            </div>
            <!--end::Card header-->
            <!--begin::Card body-->
            <div class="card-body p-0">
                <!--begin::Table wrapper-->
                <div class="table-responsive">
                    <!--begin::Table-->
                    <table class="table align-middle table-row-bordered table-row-solid gy-4 gs-9">
                        <!--begin::Thead-->
                        <thead class="border-gray-200 fs-5 fw-semibold bg-lighten">
                        <tr>
                            <th class="min-w-150px">IP Address</th>
                            <th class="min-w-150px">Browser</th>
                            <th class="min-w-150px">Location</th>
                            <th class="min-w-150px">Login At</th>
                            <th class="min-w-150px">Login Successful</th>
                            <th class="min-w-150px">Logout At</th>
                            <th class="min-w-150px">Cleared By User</th>
                        </tr>
                        </thead>
                        <!--end::Thead-->
                        <!--begin::Tbody-->
                        <tbody class="fw-6 fw-semibold text-gray-600">
                        @foreach($logs as $log)
                            @php
                                $localtion = $log->location['city'].','.$log->location['country'];
                            @endphp
                            <tr>
                                <td>{{$log->ip_address}}</td>
                                <td>{{$log->user_agent}}</td>
                                <td>{{$localtion}}</td>
                                <td>{{$log->login_at}}</td>
                                <td>{{$log->login_successful?'Yes':'No'}}</td>
                                <td>{{$log->logout_at}}</td>
                                <td>{{$log->cleared_by_user?'Yes':'No'}}</td>
                            </tr>
                        @endforeach
                        </tbody>
                        <!--end::Tbody-->
                    </table>
                    <!--end::Table-->
                </div>
                <!--end::Table wrapper-->
            </div>
            <!--end::Card body-->
        </div>
        <!--end::Login sessions-->
    </div>
    <!--end::Container-->
@stop
