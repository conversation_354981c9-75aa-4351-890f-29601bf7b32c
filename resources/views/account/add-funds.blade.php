@extends('layouts.app')

@section('content')
    <!--begin::Container-->
    <!--begin::Container-->
    <div class="container-xxl" id="kt_content_container">
        @include('account.navbar')
        <!--begin::Basic info-->
        <div class="card mb-5 mb-xl-10">
            <!--begin::Card header-->
            <div class="card-header border-0 cursor-pointer" role="button" data-bs-toggle="collapse"
                 data-bs-target="#kt_account_profile_details" aria-expanded="true"
                 aria-controls="kt_account_profile_details">
                <!--begin::Card title-->
                <div class="card-title m-0">
                    <h3 class="fw-bold m-0">Add Funds</h3>
                </div>
                <!--end::Card title-->
            </div>
            <!--begin::Card header-->
            <!--begin::Content-->
            <div id="kt_account_settings_profile_details" class="collapse show">
                <!--begin::Tab pane sms recipient-->
                <form class="tab-pane fade show active" id="sms_recipient" role="tab-panel"
                      method="POST" action="{{ route('payment.pay') }}"
                >
                    @csrf
                    <div class="d-flex flex-column gap-7 gap-lg-10">
                        <!--begin::General options-->
                        <div class="card card-flush py-4">
                            <!--begin::Card body-->
                            <div class="card-body pt-5">
                                <!--begin::Input group-->
                                <div class="fv-row mb-5">
                                    <!--begin::Label-->
                                    <label class="required form-label">Payment Method</label>
                                    <!--End::Label-->
                                    <!--begin::Select2-->
                                    <select class="form-select mb-2" name="payment_method" id="payment_method">
                                        <option value="">Select Method</option>
                                        @foreach($gateways as $gateway)
                                        <option data-fee="{{$gateway->gateway_fee}}" value="{{$gateway->class_name}}">{{$gateway->name}}</option>
                                        @endforeach
                                    </select>
                                    <!--end::Select2-->
                                </div>
                                <!--end::Input group-->
                                <!--begin::Input group-->
                                <div class="mb-5 fv-row" id="remarks_section" style="display: none;">
                                    <!--begin::Label-->
                                    <label class="form-label">Remarks</label>
                                    <!--end::Label-->
                                    <!--begin::Input-->
                                    <input id="remarks" name="remarks" class="form-control mb-2" placeholder="Enter your payment information" />
                                </div>

                                <!--begin::Input group-->
                                <div class="mb-5 fv-row">
                                    <!--begin::Label-->
                                    <label class="required form-label">Amount</label>
                                    <!--end::Label-->
                                    <!--begin::Input-->
                                    <input id="amount" name="recharge_amount" class="form-control mb-2" required/>
                                </div>
                                <!--end::Input group-->
                                <!--begin::Input group-->
                                <div class="mb-5 fv-row">
                                    <!--begin::Label-->
                                    <label class="required form-label">Gateway fee</label>
                                    <!--end::Label-->
                                    <!--begin::Input-->
                                    <input readonly id="gateway_fee" name="gateway_fee" class="form-control mb-2" required/>
                                </div>
                                <!--end::Input group-->
                                <!--begin::Input group-->
                                <div class="mb-5 fv-row">
                                    <!--begin::Label-->
                                    <label class="required form-label">Total Amount</label>
                                    <!--end::Label-->
                                    <!--begin::Input-->
                                    <input readonly id="final_amount" name="final_amount" class="form-control mb-2"
                                           required/>
                                </div>
                                <!--end::Input group-->

                                <!--end::Tab content-->
                                <div class="d-flex justify-content-end">
                                    <!--begin::Button-->
                                    <a href="{{route('account.recharge-history')}}"
                                       id="kt_ecommerce_add_product_cancel" class="btn btn-light me-5">Cancel</a>
                                    <!--end::Button-->
                                    <!--begin::Button-->
                                    @csrf
                                    <button type="submit" id="kt_ecommerce_add_product_submit"
                                            class="btn btn-primary">
                                        <span class="indicator-label">Pay</span>
                                        <span class="indicator-progress">Please wait...
											<span
                                                class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                                    </button>
                                    <!--end::Button-->
                                </div>

                            </div>
                            <!--end::Card header-->
                        </div>
                        <!--end::Pricing-->
                    </div>
                </form>
                <!--end::Tab pane sms recipient-->
            </div>
            <!--end::Content-->
        </div>
        <!--end::Basic info-->
    </div>
    <!--end::Container-->
@stop


@section('js')
    <script>
        $(document).ready(function () {
            // Set the default payment method to the first option
            $('#payment_method').val($('#payment_method option:first').val()).change();

            // Event handler for payment_method change
            $('#payment_method').change(function () {
                if ($(this).val() === 'Manual') {
                    $('#remarks_section').show();
                } else {
                    $('#remarks_section').hide();
                }

                // Get the selected payment method
                var selectedPaymentMethod = $(this).val();
                // Get the gateway_fee associated with the selected payment method
                var gateway_fee = $('option:selected', this).data('fee');
                // Set the gateway_fee input value
                $('#gateway_fee').val(gateway_fee);

                // Trigger the amount input event to recalculate total amount
                $('#amount').trigger('input');
            });

            // Event handler for amount input
            $('#amount').on('input', function () {
                // Get the entered amount
                var amount = parseFloat($(this).val()) || 0;
                // Get the gateway_fee from the gateway_fee input
                var gateway_fee = parseFloat($('#gateway_fee').val()) || 0;
                // Calculate the total amount
                var totalAmount = amount + (amount * gateway_fee);
                // Set the total amount input value
                $('#final_amount').val(totalAmount.toFixed(2)); // Adjust the precision as needed
            });
        });
    </script>

@stop

