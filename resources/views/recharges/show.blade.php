@extends('layouts.app')

@section('title', 'Recharge Details')

@section('content')
<!--begin::Container-->
<div class="container-xxl" id="kt_content_container">
    <!--begin::Navbar-->
    <div class="card mb-5 mb-xl-10">
        <div class="card-body pt-9 pb-0">
            <!--begin::Details-->
            <div class="d-flex flex-wrap flex-sm-nowrap">
                <!--begin::Image-->
                <div class="me-7 mb-4">
                    <div class="symbol symbol-100px symbol-lg-160px symbol-fixed position-relative">
                        <div class="symbol-label fs-2 fw-semibold text-success bg-light-success">
                            <i class="ki-duotone ki-wallet fs-2x">
                                <span class="path1"></span>
                                <span class="path2"></span>
                                <span class="path3"></span>
                                <span class="path4"></span>
                            </i>
                        </div>
                    </div>
                </div>
                <!--end::Image-->
                <!--begin::Info-->
                <div class="flex-grow-1">
                    <!--begin::Title-->
                    <div class="d-flex justify-content-between align-items-start flex-wrap mb-2">
                        <!--begin::User-->
                        <div class="d-flex flex-column">
                            <!--begin::Name-->
                            <div class="d-flex align-items-center mb-2">
                                <a href="#" class="text-gray-900 text-hover-primary fs-2 fw-bold me-1">
                                    Recharge #{{ $recharge->id }}
                                </a>
                                @php
                                    $statusClass = match($recharge->status) {
                                        'completed', 'success' => 'badge-light-success',
                                        'pending' => 'badge-light-warning',
                                        'failed' => 'badge-light-danger',
                                        default => 'badge-light-secondary'
                                    };
                                @endphp
                                <span class="badge {{ $statusClass }} fs-8 fw-bold">{{ ucfirst($recharge->status) }}</span>
                            </div>
                            <!--end::Name-->
                            <!--begin::Info-->
                            <div class="d-flex flex-wrap fw-semibold fs-6 mb-4 pe-2">
                                <a href="#" class="d-flex align-items-center text-gray-400 text-hover-primary me-5 mb-2">
                                    <i class="ki-duotone ki-profile-circle fs-4 me-1">
                                        <span class="path1"></span>
                                        <span class="path2"></span>
                                        <span class="path3"></span>
                                    </i>
                                    {{ $recharge->user->name }}
                                </a>
                                <a href="#" class="d-flex align-items-center text-gray-400 text-hover-primary me-5 mb-2">
                                    <i class="ki-duotone ki-sms fs-4 me-1">
                                        <span class="path1"></span>
                                        <span class="path2"></span>
                                    </i>
                                    {{ $recharge->user->email }}
                                </a>
                                <a href="#" class="d-flex align-items-center text-gray-400 text-hover-primary mb-2">
                                    <i class="ki-duotone ki-calendar-8 fs-4 me-1">
                                        <span class="path1"></span>
                                        <span class="path2"></span>
                                        <span class="path3"></span>
                                        <span class="path4"></span>
                                        <span class="path5"></span>
                                        <span class="path6"></span>
                                    </i>
                                    {{ $recharge->recharge_date ? $recharge->recharge_date->format('M d, Y h:i A') : 'N/A' }}
                                </a>
                            </div>
                            <!--end::Info-->
                        </div>
                        <!--end::User-->
                        <!--begin::Actions-->
                        <div class="d-flex my-4">
                            <a href="{{ url()->previous() }}" class="btn btn-sm btn-light me-2" id="kt_user_follow_button">
                                <i class="ki-duotone ki-arrow-left fs-3">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                </i>
                                Back
                            </a>
                            @if($recharge->payment && $recharge->payment->payment_status === 'pending' && auth()->user()->hasRole('super-admin'))
                            <div class="me-2">
                                <button class="btn btn-sm btn-primary" id="kt_user_follow_button">
                                    <i class="ki-duotone ki-check fs-3">
                                        <span class="path1"></span>
                                        <span class="path2"></span>
                                    </i>
                                    Approve
                                </button>
                            </div>
                            @endif
                        </div>
                        <!--end::Actions-->
                    </div>
                    <!--end::Title-->
                    <!--begin::Stats-->
                    <div class="d-flex flex-wrap flex-stack">
                        <!--begin::Wrapper-->
                        <div class="d-flex flex-column flex-grow-1 pe-8">
                            <!--begin::Stats-->
                            <div class="d-flex flex-wrap">
                                <!--begin::Stat-->
                                <div class="border border-gray-300 border-dashed rounded min-w-125px py-3 px-4 me-6 mb-3">
                                    <!--begin::Number-->
                                    <div class="d-flex align-items-center">
                                        <div class="fs-2 fw-bold text-success">{{ number_format($recharge->recharge_amount, 2) }}</div>
                                    </div>
                                    <!--end::Number-->
                                    <!--begin::Label-->
                                    <div class="fw-semibold fs-6 text-gray-400">Recharge Amount</div>
                                    <!--end::Label-->
                                </div>
                                <!--end::Stat-->
                                <!--begin::Stat-->
                                <div class="border border-gray-300 border-dashed rounded min-w-125px py-3 px-4 me-6 mb-3">
                                    <!--begin::Number-->
                                    <div class="d-flex align-items-center">
                                        <div class="fs-2 fw-bold text-primary">{{ number_format($recharge->gateway_fee, 2) }}</div>
                                    </div>
                                    <!--end::Number-->
                                    <!--begin::Label-->
                                    <div class="fw-semibold fs-6 text-gray-400">Gateway Fee</div>
                                    <!--end::Label-->
                                </div>
                                <!--end::Stat-->
                                <!--begin::Stat-->
                                <div class="border border-gray-300 border-dashed rounded min-w-125px py-3 px-4 me-6 mb-3">
                                    <!--begin::Number-->
                                    <div class="d-flex align-items-center">
                                        <div class="fs-2 fw-bold text-info">{{ $recharge->payment ? $recharge->payment->gateway : 'N/A' }}</div>
                                    </div>
                                    <!--end::Number-->
                                    <!--begin::Label-->
                                    <div class="fw-semibold fs-6 text-gray-400">Payment Gateway</div>
                                    <!--end::Label-->
                                </div>
                                <!--end::Stat-->
                            </div>
                            <!--end::Stats-->
                        </div>
                        <!--end::Wrapper-->
                    </div>
                    <!--end::Stats-->
                </div>
                <!--end::Info-->
            </div>
            <!--end::Details-->
        </div>
    </div>
    <!--end::Navbar-->

    <!--begin::Layout-->
    <div class="d-flex flex-column flex-lg-row">
        <!--begin::Sidebar-->
        <div class="flex-column flex-lg-row-auto w-lg-250px w-xl-350px mb-10">
            <!--begin::Card-->
            <div class="card mb-5 mb-xl-8">
                <!--begin::Card body-->
                <div class="card-body">
                    <!--begin::Summary-->
                    <div class="d-flex flex-center flex-column py-5">
                        <!--begin::Avatar-->
                        <div class="symbol symbol-100px symbol-circle mb-7">
                            @if($recharge->user->photo)
                                <img src="{{ asset($recharge->user->photo) }}" alt="{{ $recharge->user->name }}" />
                            @else
                                <div class="symbol-label fs-3 bg-light-primary text-primary">
                                    {{ strtoupper(substr($recharge->user->name, 0, 1)) }}
                                </div>
                            @endif
                        </div>
                        <!--end::Avatar-->
                        <!--begin::Name-->
                        <a href="{{ route('users.show', $recharge->user->id) }}" class="fs-3 text-gray-800 text-hover-primary fw-bold mb-3">{{ $recharge->user->name }}</a>
                        <!--end::Name-->
                        <!--begin::Position-->
                        <div class="mb-9">
                            @foreach($recharge->user->roles as $role)
                                <div class="badge badge-lg badge-light-primary d-inline">{{ ucfirst(str_replace('-', ' ', $role->name)) }}</div>
                            @endforeach
                        </div>
                        <!--end::Position-->
                    </div>
                    <!--end::Summary-->
                    <!--begin::Details toggle-->
                    <div class="d-flex flex-stack fs-4 py-3">
                        <div class="fw-bold rotate collapsible" data-bs-toggle="collapse" href="#kt_user_view_details" role="button" aria-expanded="false" aria-controls="kt_user_view_details">
                            User Details
                            <span class="ms-2 rotate-180">
                                <i class="ki-duotone ki-down fs-3"></i>
                            </span>
                        </div>
                    </div>
                    <!--end::Details toggle-->
                    <div class="separator"></div>
                    <!--begin::Details content-->
                    <div id="kt_user_view_details" class="collapse show">
                        <div class="pb-5 fs-6">
                            <!--begin::Details item-->
                            <div class="fw-bold mt-5">Username</div>
                            <div class="text-gray-600">{{ $recharge->user->username }}</div>
                            <!--end::Details item-->
                            <!--begin::Details item-->
                            <div class="fw-bold mt-5">Email</div>
                            <div class="text-gray-600">
                                <a href="mailto:{{ $recharge->user->email }}" class="text-gray-600 text-hover-primary">{{ $recharge->user->email }}</a>
                            </div>
                            <!--end::Details item-->
                            <!--begin::Details item-->
                            <div class="fw-bold mt-5">Phone</div>
                            <div class="text-gray-600">{{ $recharge->user->phone }}</div>
                            <!--end::Details item-->
                            <!--begin::Details item-->
                            <div class="fw-bold mt-5">Company</div>
                            <div class="text-gray-600">{{ $recharge->company->name }}</div>
                            <!--end::Details item-->
                            <!--begin::Details item-->
                            <div class="fw-bold mt-5">Current Balance</div>
                            <div class="text-gray-600">{{ number_format($recharge->company->current_balance, 2) }}</div>
                            <!--end::Details item-->
                        </div>
                    </div>
                    <!--end::Details content-->
                </div>
                <!--end::Card body-->
            </div>
            <!--end::Card-->
        </div>
        <!--end::Sidebar-->
        <!--begin::Content-->
        <div class="flex-lg-row-fluid ms-lg-15">
            <!--begin::Recharge Details Card-->
            <div class="card pt-4 mb-6 mb-xl-9">
                <!--begin::Card header-->
                <div class="card-header border-0">
                    <!--begin::Card title-->
                    <div class="card-title">
                        <h2>Recharge Information</h2>
                    </div>
                    <!--end::Card title-->
                </div>
                <!--end::Card header-->
                <!--begin::Card body-->
                <div class="card-body pt-0 pb-5">
                    <!--begin::Table wrapper-->
                    <div class="table-responsive">
                        <!--begin::Table-->
                        <table class="table align-middle table-row-dashed gy-5">
                            <tbody class="fs-6 fw-semibold text-gray-600">
                                <tr>
                                    <td class="text-muted">Recharge ID</td>
                                    <td class="fw-bold text-end">#{{ $recharge->id }}</td>
                                </tr>
                                <tr>
                                    <td class="text-muted">Recharge Amount</td>
                                    <td class="fw-bold text-end text-success">{{ number_format($recharge->recharge_amount, 2) }}</td>
                                </tr>
                                <tr>
                                    <td class="text-muted">Gateway Fee</td>
                                    <td class="fw-bold text-end">{{ number_format($recharge->gateway_fee, 2) }}</td>
                                </tr>
                                <tr>
                                    <td class="text-muted">Balance Before</td>
                                    <td class="fw-bold text-end">{{ number_format($recharge->balance_before, 2) }}</td>
                                </tr>
                                <tr>
                                    <td class="text-muted">Balance After</td>
                                    <td class="fw-bold text-end text-primary">{{ number_format($recharge->balance_after, 2) }}</td>
                                </tr>
                                <tr>
                                    <td class="text-muted">Recharge Date</td>
                                    <td class="fw-bold text-end">{{ $recharge->recharge_date ? $recharge->recharge_date->format('M d, Y h:i A') : 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <td class="text-muted">Status</td>
                                    <td class="text-end">
                                        <span class="badge {{ $statusClass }} fs-7">{{ ucfirst($recharge->status) }}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-muted">Created</td>
                                    <td class="fw-bold text-end">{{ $recharge->created_at->format('M d, Y h:i A') }}</td>
                                </tr>
                                @if($recharge->updated_at != $recharge->created_at)
                                <tr>
                                    <td class="text-muted">Last Updated</td>
                                    <td class="fw-bold text-end">{{ $recharge->updated_at->format('M d, Y h:i A') }}</td>
                                </tr>
                                @endif
                            </tbody>
                        </table>
                        <!--end::Table-->
                    </div>
                    <!--end::Table wrapper-->
                </div>
                <!--end::Card body-->
            </div>
            <!--end::Recharge Details Card-->

            @if($recharge->payment)
            <!--begin::Payment Details Card-->
            <div class="card pt-4 mb-6 mb-xl-9">
                <!--begin::Card header-->
                <div class="card-header border-0">
                    <!--begin::Card title-->
                    <div class="card-title">
                        <h2>Payment Information</h2>
                    </div>
                    <!--end::Card title-->
                </div>
                <!--end::Card header-->
                <!--begin::Card body-->
                <div class="card-body pt-0 pb-5">
                    <!--begin::Table wrapper-->
                    <div class="table-responsive">
                        <!--begin::Table-->
                        <table class="table align-middle table-row-dashed gy-5">
                            <tbody class="fs-6 fw-semibold text-gray-600">
                                <tr>
                                    <td class="text-muted">Payment ID</td>
                                    <td class="fw-bold text-end">#{{ $recharge->payment->id }}</td>
                                </tr>
                                <tr>
                                    <td class="text-muted">Transaction ID</td>
                                    <td class="fw-bold text-end">{{ $recharge->payment->transaction_id }}</td>
                                </tr>
                                <tr>
                                    <td class="text-muted">Gateway</td>
                                    <td class="fw-bold text-end">{{ ucfirst($recharge->payment->gateway) }}</td>
                                </tr>
                                <tr>
                                    <td class="text-muted">Amount</td>
                                    <td class="fw-bold text-end text-success">{{ number_format($recharge->payment->amount, 2) }}</td>
                                </tr>
                                <tr>
                                    <td class="text-muted">Payment Status</td>
                                    <td class="text-end">
                                        @php
                                            $paymentStatusClass = match($recharge->payment->payment_status) {
                                                'completed', 'success' => 'badge-light-success',
                                                'pending' => 'badge-light-warning',
                                                'failed' => 'badge-light-danger',
                                                default => 'badge-light-secondary'
                                            };
                                        @endphp
                                        <span class="badge {{ $paymentStatusClass }} fs-7">{{ ucfirst($recharge->payment->payment_status) }}</span>
                                    </td>
                                </tr>
                                @if($recharge->payment->remarks)
                                <tr>
                                    <td class="text-muted">Remarks</td>
                                    <td class="fw-bold text-end">{{ $recharge->payment->remarks }}</td>
                                </tr>
                                @endif
                                <tr>
                                    <td class="text-muted">Payment Created</td>
                                    <td class="fw-bold text-end">{{ $recharge->payment->created_at->format('M d, Y h:i A') }}</td>
                                </tr>
                                @if($recharge->payment->updated_at != $recharge->payment->created_at)
                                <tr>
                                    <td class="text-muted">Payment Updated</td>
                                    <td class="fw-bold text-end">{{ $recharge->payment->updated_at->format('M d, Y h:i A') }}</td>
                                </tr>
                                @endif
                            </tbody>
                        </table>
                        <!--end::Table-->
                    </div>
                    <!--end::Table wrapper-->

                    @if($recharge->payment->api_response && auth()->user()->hasRole('super-admin'))
                    <!--begin::API Response-->
                    <div class="separator my-10"></div>
                    <div class="d-flex flex-stack fs-4 py-3">
                        <div class="fw-bold rotate collapsible" data-bs-toggle="collapse" href="#kt_payment_api_response" role="button" aria-expanded="false" aria-controls="kt_payment_api_response">
                            API Response (Admin Only)
                            <span class="ms-2 rotate-180">
                                <i class="ki-duotone ki-down fs-3"></i>
                            </span>
                        </div>
                    </div>
                    <div class="separator"></div>
                    <div id="kt_payment_api_response" class="collapse">
                        <div class="py-5">
                            <pre class="bg-light-dark p-5 rounded"><code>{{ $recharge->payment->api_response }}</code></pre>
                        </div>
                    </div>
                    <!--end::API Response-->
                    @endif
                </div>
                <!--end::Card body-->
            </div>
            <!--end::Payment Details Card-->
            @endif

            @if($relatedTransactions->count() > 0)
            <!--begin::Related Transactions Card-->
            <div class="card pt-4 mb-6 mb-xl-9">
                <!--begin::Card header-->
                <div class="card-header border-0">
                    <!--begin::Card title-->
                    <div class="card-title">
                        <h2>Related Transactions</h2>
                    </div>
                    <!--end::Card title-->
                </div>
                <!--end::Card header-->
                <!--begin::Card body-->
                <div class="card-body pt-0 pb-5">
                    <!--begin::Table wrapper-->
                    <div class="table-responsive">
                        <!--begin::Table-->
                        <table class="table align-middle table-row-dashed fs-6 gy-5">
                            <thead>
                                <tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
                                    <th class="min-w-100px">Date</th>
                                    <th class="min-w-100px text-end">Amount In</th>
                                    <th class="min-w-100px text-end">Amount Out</th>
                                    <th class="min-w-100px text-end">Balance After</th>
                                    <th class="min-w-150px">Remarks</th>
                                </tr>
                            </thead>
                            <tbody class="fw-semibold text-gray-600">
                                @foreach($relatedTransactions as $transaction)
                                <tr>
                                    <td>{{ $transaction->date ? $transaction->date->format('M d, Y h:i A') : 'N/A' }}</td>
                                    <td class="text-end text-success">
                                        {{ $transaction->amount_in > 0 ? number_format($transaction->amount_in, 2) : '-' }}
                                    </td>
                                    <td class="text-end text-danger">
                                        {{ $transaction->amount_out > 0 ? number_format($transaction->amount_out, 2) : '-' }}
                                    </td>
                                    <td class="text-end text-primary">
                                        {{ $transaction->balance_after ? number_format($transaction->balance_after, 2) : 'N/A' }}
                                    </td>
                                    <td>{{ $transaction->remarks ?: 'N/A' }}</td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                        <!--end::Table-->
                    </div>
                    <!--end::Table wrapper-->
                </div>
                <!--end::Card body-->
            </div>
            <!--end::Related Transactions Card-->
            @endif
        </div>
        <!--end::Content-->
    </div>
    <!--end::Layout-->
</div>
<!--end::Container-->
@stop
