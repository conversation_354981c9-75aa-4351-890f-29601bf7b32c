@extends('layouts.app')

@section('title', 'Test Popup')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3>Popup Test Page</h3>
                </div>
                <div class="card-body">
                    <p>This page is for testing the popup notification functionality.</p>
                    
                    <div class="alert alert-info">
                        <h5>Popup Settings Status:</h5>
                        @if(isset($popupSettings) && $popupSettings)
                            <ul>
                                <li><strong>Title:</strong> {{ $popupSettings['title'] }}</li>
                                <li><strong>Enabled:</strong> {{ $popupSettings['enabled'] ? 'Yes' : 'No' }}</li>
                                <li><strong>Has File:</strong> {{ isset($popupSettings['file']) ? 'Yes' : 'No' }}</li>
                                <li><strong>Description Length:</strong> {{ strlen($popupSettings['description']) }} characters</li>
                            </ul>
                        @else
                            <p>No popup settings found or popup is disabled.</p>
                        @endif
                    </div>

                    <div class="mt-4">
                        <button type="button" class="btn btn-primary" onclick="testPopup()">
                            Test Show Popup
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="clearPopupSession()">
                            Clear Session Storage
                        </button>
                        <button type="button" class="btn btn-info" onclick="testModalBlocking()">
                            Test Modal Blocking
                        </button>
                        <button type="button" class="btn btn-warning" onclick="testFileDisplay()">
                            Test File Display
                        </button>
                    </div>

                    <div class="mt-4">
                        <h5>Debug Information:</h5>
                        <div id="debug-info" class="alert alert-light">
                            <p>Check browser console for detailed logs.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function testPopup() {
    if (typeof KTPopupNotification !== 'undefined') {
        // Clear session storage first
        clearPopupSession();
        // Then show popup
        setTimeout(() => {
            KTPopupNotification.show();
        }, 100);
    } else {
        alert('KTPopupNotification not available');
    }
}

function clearPopupSession() {
    // Clear all popup-related session storage
    for (let i = sessionStorage.length - 1; i >= 0; i--) {
        const key = sessionStorage.key(i);
        if (key && key.startsWith('popup_shown_')) {
            sessionStorage.removeItem(key);
        }
    }
    console.log('Popup session storage cleared');
    
    // Update debug info
    document.getElementById('debug-info').innerHTML = `
        <p>Session storage cleared at: ${new Date().toLocaleTimeString()}</p>
        <p>Window popup settings available: ${typeof window.popupSettings !== 'undefined'}</p>
        <p>KTPopupNotification available: ${typeof KTPopupNotification !== 'undefined'}</p>
    `;
}

function testModalBlocking() {
    alert('After clicking OK, a popup will appear. Try to close it using ESC, clicking outside, or any method other than the Agree button. It should be impossible to close.');
    clearPopupSession();
    setTimeout(() => {
        if (typeof KTPopupNotification !== 'undefined') {
            KTPopupNotification.show();
        }
    }, 1000);
}

function testFileDisplay() {
    const hasFile = window.popupSettings && window.popupSettings.file;
    if (hasFile) {
        alert('File is attached. The popup should show file content.');
    } else {
        alert('No file is attached. The popup should NOT show any file section.');
    }
    clearPopupSession();
    setTimeout(() => {
        if (typeof KTPopupNotification !== 'undefined') {
            KTPopupNotification.show();
        }
    }, 1000);
}

// Auto-update debug info on page load
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        clearPopupSession();
    }, 500);
});
</script>
@endsection
