@extends('layouts.guest')

@section('js')
    <!--begin::Javascript-->
    <script>var hostUrl = "assets/";</script>
    <!--begin::Custom Javascript(used for this page only)-->
    <script src="{{ asset('js/custom/authentication/sign-in/general.js') }}"></script>
    <!--end::Custom Javascript-->
    <!--end::Javascript-->
@endsection

@section('content')
<!-- Login Tabs -->
<div class="login-tabs">
    <div class="login-tab active" data-tab="login">Login</div>
    <div class="login-tab" data-tab="register">Register</div>
</div>

<!-- Login Form -->
<form class="form w-100" id="login_form" method="post" action="{{ route('login') }}" style="display: block;">
    @csrf

    <div class="input-icon">
        <input type="text"
               placeholder="Enter your username/email/phone"
               name="username"
               autocomplete="username"
               class="futuristic-input"
               required
               minlength="3"
               value="{{ old('username') }}" />
        @error('username')
        <div class="alert alert-danger">{{ $message }}</div>
        @enderror
    </div>

    <div class="input-icon password">
        <input type="password"
               placeholder="Enter your password"
               name="password"
               autocomplete="current-password"
               class="futuristic-input"
               required
               minlength="6" />
        @error('password')
        <div class="alert alert-danger">{{ $message }}</div>
        @enderror
    </div>

    <!-- Forgot Password Link -->
    <div class="forgot-password-link">
        <a href="{{ route('password.request') }}" class="forgot-password-text">
            Forgot your password?
        </a>
    </div>

    <button type="submit" id="login_submit" class="futuristic-button">
        <span class="indicator-label">Sign In</span>
        <span class="indicator-progress" style="display: none;">Please wait...
            <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
        </span>
    </button>
</form>

<!-- Registration Form -->
<form class="form w-100" id="register_form" method="post" action="{{ route('register') }}" style="display: none;">
    @csrf

    <div class="input-icon name">
        <input type="text"
               placeholder="Enter your full name"
               name="name"
               autocomplete="name"
               class="futuristic-input"
               required
               minlength="2"
               maxlength="255"
               pattern="[A-Za-z\s]+"
               title="Please enter a valid name (letters and spaces only)"
               value="{{ old('name') }}" />
        @error('name')
        <div class="alert alert-danger">{{ $message }}</div>
        @enderror
    </div>

    <div class="input-icon email">
        <input type="email"
               placeholder="Enter your email address"
               name="email"
               autocomplete="email"
               class="futuristic-input"
               required
               pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$"
               title="Please enter a valid email address"
               value="{{ old('email') }}" />
        @error('email')
        <div class="alert alert-danger">{{ $message }}</div>
        @enderror
    </div>

    <div class="input-icon phone">
        <input type="tel"
               placeholder="Enter your phone number"
               name="phone"
               autocomplete="tel"
               class="futuristic-input"
               required
               pattern="[0-9+\-\s\(\)]+"
               minlength="10"
               maxlength="15"
               title="Please enter a valid phone number"
               value="{{ old('phone') }}" />
        @error('phone')
        <div class="alert alert-danger">{{ $message }}</div>
        @enderror
    </div>

    <div class="input-icon password">
        <input type="password"
               placeholder="Enter your password"
               name="password"
               autocomplete="new-password"
               class="futuristic-input"
               required
               minlength="8"
               pattern="(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}"
               title="Password must be at least 8 characters long and contain at least one uppercase letter, one lowercase letter, and one number"
               id="register_password" />
        @error('password')
        <div class="alert alert-danger">{{ $message }}</div>
        @enderror
    </div>

    <div class="input-icon password">
        <input type="password"
               placeholder="Confirm your password"
               name="password_confirmation"
               autocomplete="new-password"
               class="futuristic-input"
               required
               minlength="8"
               id="register_password_confirmation" />
        @error('password_confirmation')
        <div class="alert alert-danger">{{ $message }}</div>
        @enderror
    </div>

    <!-- Terms and Conditions -->
    <div class="terms-checkbox">
        <div class="form-check">
            <input class="form-check-input" type="checkbox" name="terms" id="terms" required>
            <label class="form-check-label" for="terms" style="color: rgba(255, 255, 255, 0.8); font-size: 14px;">
                I agree to the <a href="#" style="color: #00ffff;">Terms and Conditions</a>
            </label>
        </div>
    </div>

    <button type="submit" id="register_submit" class="futuristic-button">
        <span class="indicator-label">Create Account</span>
        <span class="indicator-progress" style="display: none;">Please wait...
            <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
        </span>
    </button>
</form>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const loginTab = document.querySelector('[data-tab="login"]');
    const registerTab = document.querySelector('[data-tab="register"]');
    const loginForm = document.getElementById('login_form');
    const registerForm = document.getElementById('register_form');

    loginTab.addEventListener('click', function() {
        loginTab.classList.add('active');
        registerTab.classList.remove('active');
        loginForm.style.display = 'block';
        registerForm.style.display = 'none';
    });

    registerTab.addEventListener('click', function() {
        registerTab.classList.add('active');
        loginTab.classList.remove('active');
        registerForm.style.display = 'block';
        loginForm.style.display = 'none';
    });

    // Password confirmation validation
    const registerPassword = document.getElementById('register_password');
    const registerPasswordConfirmation = document.getElementById('register_password_confirmation');

    function validatePasswordMatch() {
        if (registerPassword.value !== registerPasswordConfirmation.value) {
            registerPasswordConfirmation.setCustomValidity("Passwords don't match");
        } else {
            registerPasswordConfirmation.setCustomValidity('');
        }
    }

    registerPassword.addEventListener('change', validatePasswordMatch);
    registerPasswordConfirmation.addEventListener('keyup', validatePasswordMatch);
});
</script>
@endsection