@extends('layouts.guest')

@section('js')
    <!--begin::Javascript-->
    <script>var hostUrl = "assets/";</script>
    <!--begin::Custom Javascript(used for this page only)-->
    <script src="{{ asset('js/custom/authentication/reset-password/reset-password.js') }}"></script>
    <!--end::Custom Javascript-->
    <!--end::Javascript-->
@endsection

@section('content')
<!-- Forgot Password Form -->
<div class="forgot-description">
    Enter your email address and we'll send you a link to reset your password.
</div>

@if (session('status'))
    <div class="alert alert-success">
        {{ session('status') }}
    </div>
@endif

<form method="POST" action="{{ route('password.email') }}">
    @csrf

    <div class="input-icon email">
        <input type="email"
               placeholder="Enter your email address"
               name="email"
               autocomplete="email"
               class="futuristic-input"
               required
               value="{{ old('email') }}" />
        @error('email')
        <div class="alert alert-danger">{{ $message }}</div>
        @enderror
    </div>

    <button type="submit" class="futuristic-button">
        Send Password Reset Link
    </button>
</form>

<div class="back-link">
    <a href="{{ route('login') }}">← Back to Login</a>
</div>

<style>
.forgot-description {
    color: rgba(255, 255, 255, 0.8);
    text-align: center;
    margin-bottom: 25px;
    font-size: 14px;
    line-height: 1.5;
}

.back-link {
    text-align: center;
    margin-top: 20px;
}

.back-link a {
    color: rgba(255, 255, 255, 0.6);
    text-decoration: none;
    font-size: 14px;
    transition: all 0.3s ease;
}

.back-link a:hover {
    color: #00ffff;
    text-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
}
</style>
@endsection