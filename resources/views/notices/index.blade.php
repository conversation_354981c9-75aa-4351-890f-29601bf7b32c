@extends('layouts.app')

@section('title', 'Notices')

@section('content')
    <!--begin::Container-->
    <div class="container-xxl" id="kt_content_container">
        <!--begin::Card-->
        <div class="card">
            <!--begin::Card header-->
            <div class="card-header border-0 pt-6">
                <!--begin::Card title-->
                <div class="card-title">
                    <h3 class="fw-bold m-0">Notices</h3>
                </div>
                <!--begin::Card title-->
            </div>
            <!--end::Card header-->
            <!--begin::Card body-->
            <div class="card-body py-4">
                @if($notices->count() > 0)
                    <!--begin::Notices List-->
                    <div class="d-flex flex-column gap-7">
                        @foreach($notices as $notice)
                        <!--begin::Notice Item-->
                        <div class="card card-flush border">
                            <div class="card-body p-6">
                                <!--begin::Header-->
                                <div class="d-flex align-items-center justify-content-between mb-4">
                                    <div class="d-flex align-items-center">
                                        <!--begin::Icon-->
                                        <div class="symbol symbol-40px me-4">
                                            <div class="symbol-label bg-light-primary">
                                                <i class="ki-duotone ki-notification-bing fs-2 text-primary">
                                                    <span class="path1"></span>
                                                    <span class="path2"></span>
                                                    <span class="path3"></span>
                                                </i>
                                            </div>
                                        </div>
                                        <!--end::Icon-->
                                        <!--begin::Title and Date-->
                                        <div>
                                            <h4 class="fw-bold text-gray-800 mb-1">{{ $notice->title }}</h4>
                                            <div class="text-muted fs-7">
                                                <span>{{ $notice->created_at->format('M d, Y') }}</span>
                                                <span class="bullet bullet-dot mx-2"></span>
                                                <span>By {{ $notice->creator->name }}</span>
                                            </div>
                                        </div>
                                        <!--end::Title and Date-->
                                    </div>
                                    <!--begin::Actions-->
                                    <div class="d-flex align-items-center">
                                        <span class="badge badge-light-success me-3">Active</span>
                                        <button type="button" class="btn btn-sm btn-light-primary" 
                                                data-bs-toggle="modal" 
                                                data-bs-target="#notice_modal_{{ $notice->id }}">
                                            <i class="ki-duotone ki-eye fs-4">
                                                <span class="path1"></span>
                                                <span class="path2"></span>
                                                <span class="path3"></span>
                                            </i>
                                            View Notice
                                        </button>
                                    </div>
                                    <!--end::Actions-->
                                </div>
                                <!--end::Header-->
                                <!--begin::Description Preview-->
                                <div class="text-gray-600 fs-6">
                                    {{ Str::limit($notice->description, 200) }}
                                    @if(strlen($notice->description) > 200)
                                        <button type="button" class="btn btn-link p-0 text-primary" 
                                                data-bs-toggle="modal" 
                                                data-bs-target="#notice_modal_{{ $notice->id }}">
                                            Read more...
                                        </button>
                                    @endif
                                </div>
                                <!--end::Description Preview-->
                            </div>
                        </div>
                        <!--end::Notice Item-->
                        @endforeach
                    </div>
                    <!--end::Notices List-->
                @else
                    <!--begin::Empty state-->
                    <div class="text-center py-10">
                        <div class="mb-10">
                            <span class="svg-icon svg-icon-5x svg-icon-muted">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path opacity="0.3" d="M22 12C22 17.5 17.5 22 12 22C6.5 22 2 17.5 2 12C2 6.5 6.5 2 12 2C17.5 2 22 6.5 22 12ZM12 7C10.3 7 9 8.3 9 10C9 11.7 10.3 13 12 13C13.7 13 15 11.7 15 10C15 8.3 13.7 7 12 7Z" fill="currentColor"/>
                                    <path d="M12 22C17.5 22 22 17.5 22 12C22 6.5 17.5 2 12 2C6.5 2 2 6.5 2 12C2 17.5 6.5 22 12 22ZM19 10C19 11.7 17.7 13 16 13C14.3 13 13 11.7 13 10C13 8.3 14.3 7 16 7C17.7 7 19 8.3 19 10ZM9 10C9 8.3 7.7 7 6 7C4.3 7 3 8.3 3 10C3 11.7 4.3 13 6 13C7.7 13 9 11.7 9 10Z" fill="currentColor"/>
                                </svg>
                            </span>
                        </div>
                        <h2 class="fw-bold mb-5">No Notices Available</h2>
                        <p class="text-gray-600 fs-6 mb-8">
                            There are currently no active notices to display.
                        </p>
                    </div>
                    <!--end::Empty state-->
                @endif
            </div>
            <!--end::Card body-->
        </div>
        <!--end::Card-->
    </div>
    <!--end::Container-->

    <!--begin::Notice Modals-->
    @foreach($notices as $notice)
    <div class="modal fade" id="notice_modal_{{ $notice->id }}" tabindex="-1" aria-hidden="true">
        <!--begin::Modal dialog-->
        <div class="modal-dialog modal-dialog-centered mw-650px">
            <!--begin::Modal content-->
            <div class="modal-content">
                <!--begin::Modal header-->
                <div class="modal-header" id="notice_modal_header_{{ $notice->id }}">
                    <!--begin::Modal title-->
                    <h2 class="fw-bold">{{ $notice->title }}</h2>
                    <!--end::Modal title-->
                    <!--begin::Close-->
                    <div class="btn btn-icon btn-sm btn-active-icon-primary" data-bs-dismiss="modal">
                        <i class="ki-duotone ki-cross fs-1">
                            <span class="path1"></span>
                            <span class="path2"></span>
                        </i>
                    </div>
                    <!--end::Close-->
                </div>
                <!--end::Modal header-->
                <!--begin::Modal body-->
                <div class="modal-body scroll-y mx-5 mx-xl-15 my-7">
                    <!--begin::Notice Details-->
                    <div class="d-flex flex-column gap-5">
                        <!--begin::Meta Info-->
                        <div class="d-flex align-items-center gap-3 pb-3 border-bottom">
                            <div class="symbol symbol-35px">
                                <div class="symbol-label bg-light-primary">
                                    <i class="ki-duotone ki-notification-bing fs-3 text-primary">
                                        <span class="path1"></span>
                                        <span class="path2"></span>
                                        <span class="path3"></span>
                                    </i>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <div class="text-muted fs-7">
                                    <span>Published on {{ $notice->created_at->format('F d, Y \a\t g:i A') }}</span>
                                    <span class="bullet bullet-dot mx-2"></span>
                                    <span>By {{ $notice->creator->name }}</span>
                                </div>
                                <div class="mt-1">
                                    <span class="badge badge-light-success">Active</span>
                                </div>
                            </div>
                        </div>
                        <!--end::Meta Info-->
                        <!--begin::Description-->
                        <div class="fs-6 text-gray-700 lh-lg">
                            {!! nl2br(e($notice->description)) !!}
                        </div>
                        <!--end::Description-->
                    </div>
                    <!--end::Notice Details-->
                </div>
                <!--end::Modal body-->
                <!--begin::Modal footer-->
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Close</button>
                </div>
                <!--end::Modal footer-->
            </div>
            <!--end::Modal content-->
        </div>
        <!--end::Modal dialog-->
    </div>
    @endforeach
    <!--end::Notice Modals-->
@stop