@extends('layouts.app')

@section('title', 'Send SMS')

@section('content')

    <!--begin::Container-->
    <div class="container-xxl" id="kt_content_container">
        <!--begin::Form-->
        <div id="kt_ecommerce_add_product_form" class="form d-flex flex-column flex-lg-row">
            <!--begin::Main column-->
            <div class="d-flex flex-column flex-row-fluid gap-7 gap-lg-10">
                <!--begin:::Tabs-->
                <ul class="nav nav-custom nav-tabs nav-line-tabs nav-line-tabs-2x border-0 fs-4 fw-semibold mb-n2">
                    <!--begin:::Tab item-->
                    <li class="nav-item">
                        <a class="nav-link text-active-primary pb-4 active" data-bs-toggle="tab"
                           href="#sms_recipient">SMS Recipient</a>
                    </li>
                    <!--end:::Tab item-->
                    <!--begin:::Tab item-->
                    <li class="nav-item">
                        <a class="nav-link text-active-primary pb-4" data-bs-toggle="tab"
                           href="#group_contact">Group Contact</a>
                    </li>
                    {{-- <!--end:::Tab item-->
                     <li class="nav-item">
                         <a class="nav-link text-active-primary pb-4" data-bs-toggle="tab"
                            href="#upload_file">Upload File</a>
                     </li>--}}
                    <li class="nav-item">
                        <a class="nav-link text-active-primary pb-4"
                           href="{{route('messages.dynamic-sms.get')}}">Dynamic SMS</a>
                    </li>
                </ul>
                <!--end:::Tabs-->
                <!--begin::Tab content-->
                <div class="tab-content">
                    <!--begin::Tab pane sms recipient-->
                    <form class="tab-pane fade show active" id="sms_recipient" role="tab-panel"
                          method="POST" action="{{ route('messages.store') }}">
                        <div class="d-flex flex-column gap-7 gap-lg-10">
                            <!--begin::General options-->
                            <div class="card card-flush py-4">
                                <!--begin::Card body-->
                                <div class="card-body pt-5">
                                    <!--begin::Input group-->
                                    <div class="mb-5 fv-row">
                                        <!--begin::Label-->
                                        <label class="required form-label">Select Sender ID</label>
                                        <!--end::Label-->
                                        <select class="form-select mb-2" name="sender_id" data-control="select2"
                                                data-hide-search="true" data-placeholder="Select an option">
                                            <option></option>
                                            @foreach($senders as $sender)
                                                <option value="{{ $sender->id }}"
                                                    {{ old('sender_id', $sender->is_default ? $sender->id : '') == $sender->id ? 'selected' : '' }}>
                                                    {{ $sender->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                        <div class="form-group">
                                            <a href="{{route('senders.index')}}" class="btn btn-sm btn-light-primary">
                            <span class="svg-icon svg-icon-2">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                     xmlns="http://www.w3.org/2000/svg">
                                    <rect opacity="0.5" x="11" y="18" width="12"
                                          height="2" rx="1" transform="rotate(-90 11 18)"
                                          fill="currentColor"></rect>
                                    <rect x="6" y="11" width="12" height="2" rx="1"
                                          fill="currentColor"></rect>
                                </svg>
                            </span>
                                                Request New Sender ID
                                            </a>
                                        </div>
                                    </div>
                                    <!--end::Input group-->

                                    <!--begin::Input group-->
                                    <div class="mb-5 fv-row">
                                        <label class="required form-label">Enter Mobile Numbers</label>
                                        <textarea name="phone_numbers" class="form-control mb-2"
                                                  placeholder="Please start with country code">{{ old('phone_numbers') }}</textarea>
                                        <div class="text-gray-600 text-dark-gray-400 fs-7">New line separated</div>
                                    </div>
                                    <!--end::Input group-->

                                    <!--begin::Input group-->
                                    <div class="fv-row mb-5">
                                        <label class="fs-6 fw-semibold mb-2">Select SMS Type *</label>
                                        <div class="row row-cols-1 row-cols-md-3 row-cols-lg-4 row-cols-xl-4 g-3">
                                            @php
                                                $oldSmsType = old('sms_type', 'text'); // Default to 'text' if no old input
                                            @endphp
                                            <div class="col">
                                                <label
                                                    class="btn btn-outline btn-outline-dashed btn-active-light-primary d-flex text-start p-6">
                                                    <input class="form-check-input" type="radio" name="sms_type"
                                                           value="text"
                                                        {{ $oldSmsType == 'text' ? 'checked' : '' }}/>
                                                    <span class="ms-5">
                                    <span class="fs-4 fw-bold text-gray-800 d-block">Text</span>
                                </span>
                                                </label>
                                            </div>
                                            <div class="col">
                                                <label
                                                    class="btn btn-outline btn-outline-dashed btn-active-light-primary d-flex text-start p-6">
                                                    <input class="form-check-input" type="radio" name="sms_type"
                                                           value="flash"
                                                        {{ $oldSmsType == 'flash' ? 'checked' : '' }}/>
                                                    <span class="ms-5">
                                    <span class="fs-4 fw-bold text-gray-800 d-block">Flash</span>
                                </span>
                                                </label>
                                            </div>
                                            <div class="col">
                                                <label
                                                    class="btn btn-outline btn-outline-dashed btn-active-light-primary d-flex text-start p-6">
                                                    <input class="form-check-input" type="radio" name="sms_type"
                                                           value="flash_unicode"
                                                        {{ $oldSmsType == 'flash_unicode' ? 'checked' : '' }}/>
                                                    <span class="ms-5">
                                    <span class="fs-4 fw-bold text-gray-800 d-block">Flash Unicode</span>
                                </span>
                                                </label>
                                            </div>
                                            <div class="col">
                                                <label
                                                    class="btn btn-outline btn-outline-dashed btn-active-light-primary d-flex text-start p-6">
                                                    <input class="form-check-input" type="radio" name="sms_type"
                                                           value="unicode"
                                                        {{ $oldSmsType == 'unicode' ? 'checked' : '' }}/>
                                                    <span class="ms-5">
                                    <span class="fs-4 fw-bold text-gray-800 d-block">Unicode</span>
                                </span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <!--end::Input group-->

                                    <!--begin::Input group-->
                                <div class="mb-5 fv-row">
                                    <!--begin::Label-->
                                    <label class="required form-label">Enter SMS Content</label>
                                    <!--end::Label-->
                                    <!--begin::Input-->
                                    <textarea id="smsContent" name="sms_content" class="form-control mb-2"
                                              placeholder=""></textarea>
                                    <!--end::Input-->
                                    <!--begin::Description-->
                                    <div class="row">
                                        <div class="col-6">
                                            <div class="text-gray-600 text-dark-gray-400 fs-7 fw-semibold" id="smsCountInfo">CHECK YOUR SMS COUNT
                                            </div>
                                        </div>
                                        <div class="col-6" style="text-align: right">
                                            <button type="button" class="btn btn-sm btn-light-primary"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#kt_modal_show_template">Use Template
                                            </button>
                                        </div>
                                    </div>
                                    <!--end::Description-->
                                </div>
                                <!--end::Input group-->


                                    <!--begin::Input group-->
                                    <div class="fv-row mb-5">
                                        <label class="required form-label">Schedule SMS</label>
                                        <div class="row g-3">
                                            @php
                                                $oldIsScheduled = old('is_scheduled', '0'); // Default to "Send Now"
                                            @endphp
                                            <div class="col">
                                                <label
                                                    class="btn btn-outline btn-outline-dashed btn-active-light-primary d-flex text-start p-6">
                                                    <input class="form-check-input" type="radio" name="is_scheduled"
                                                           value="0"
                                                        {{ $oldIsScheduled == '0' ? 'checked' : '' }}/>
                                                    <span class="ms-5">
                                    <span class="fs-4 fw-bold text-gray-800 d-block">Send Now</span>
                                </span>
                                                </label>
                                            </div>
                                            <div class="col">
                                                <label
                                                    class="btn btn-outline btn-outline-dashed btn-active-light-primary d-flex text-start p-6">
                                                    <input class="form-check-input" type="radio" name="is_scheduled"
                                                           value="1"
                                                        {{ $oldIsScheduled == '1' ? 'checked' : '' }}/>
                                                    <span class="ms-5">
                                    <span class="fs-4 fw-bold text-gray-800 d-block">Send Later</span>
                                </span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <!--end::Input group-->

                                    <!--begin::Input group: Scheduled Date (Shown Only If "Send Later" is Selected) -->
                                    <div class="fv-row mb-5 scheduled_at"
                                         style="{{ $oldIsScheduled == '1' ? 'display:block;' : 'display:none;' }}">
                                        <input type="text" class="date_time_picker form-control" name="scheduled_at"
                                               value="{{ old('scheduled_at') }}" placeholder="Select Date">
                                    </div>
                                    <!--end::Input group-->

                                    <!--begin::Submit Button-->
                                    <div class="d-flex justify-content-end">
                                        <a href="{{ route('messages.create') }}" class="btn btn-light me-5">Cancel</a>
                                        @csrf
                                        <button type="submit" class="btn btn-primary">
                                            <span class="indicator-label">Send SMS</span>
                                            <span class="indicator-progress">Please wait...
                            <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                        </span>
                                        </button>
                                    </div>
                                    <!--end::Submit Button-->

                                </div>
                            </div>
                        </div>
                    </form>

                    <!--end::Tab pane sms recipient-->
                    <!--begin::Tab pane group contact-->
                    <div class="tab-pane fade" id="group_contact" role="tab-panel">
                        <form class="d-flex flex-column gap-7 gap-lg-10"
                              method="POST" action="{{ route('messages.group.store') }}"
                        >
                            <div class="d-flex flex-column gap-7 gap-lg-10">
                                <!--begin::General options-->
                                <div class="card card-flush py-4">
                                    <!--begin::Card body-->
                                    <div class="card-body pt-5">
                                        <!--begin::Input group-->
                                        <div class="mb-5 fv-row">
                                            <!--begin::Label-->
                                            <label class="required form-label">Select Sender ID</label>
                                            <!--end::Label-->
                                            <!--begin::Select2-->
                                            <select class="form-select mb-2" name="sender_id" data-control="select2"
                                                    data-hide-search="true" data-placeholder="Select an option">
                                                <option></option>
                                                @foreach($senders as $sender)
                                                    <option value="{{ $sender->id }}"
                                                            @if($sender->is_default) selected @endif
                                                    >{{ $sender->name }}</option>
                                                @endforeach
                                            </select>
                                            <!--end::Select2-->
                                            <div class="form-group">
                                                <a href="{{route('senders.index')}}"
                                                   class="btn btn-sm btn-light-primary">
                                                    <!--begin::Svg Icon | path: icons/duotune/arrows/arr087.svg-->
                                                    <span class="svg-icon svg-icon-2">
                                                                        <svg width="24" height="24" viewBox="0 0 24 24"
                                                                             fill="none"
                                                                             xmlns="http://www.w3.org/2000/svg">
                                                                            <rect opacity="0.5" x="11" y="18" width="12"
                                                                                  height="2" rx="1"
                                                                                  transform="rotate(-90 11 18)"
                                                                                  fill="currentColor"></rect>
                                                                            <rect x="6" y="11" width="12" height="2"
                                                                                  rx="1"
                                                                                  fill="currentColor"></rect>
                                                                        </svg>
                                                                    </span>
                                                    <!--end::Svg Icon-->Request New Sender ID
                                                </a>
                                            </div>
                                        </div>
                                        <!--end::Input group-->

                                        <!--end:Tax-->

                                        <!--begin::Input group-->
                                        <div class="mb-5 fv-row">
                                            <!--begin::Label-->
                                            <label class="required form-label">Select Contact Group</label>
                                            <!--end::Label-->
                                            <!--begin::Select2-->
                                            <select class="form-select mb-2" name="group" data-control="select2"
                                                    data-hide-search="true" data-placeholder="Select an option">
                                                @forelse($groups as $group)
                                                    <option value="{{ $group->id }}">{{ $group->name }}</option>
                                                @empty
                                                @endforelse
                                            </select>
                                            <!--end::Select2-->
                                        </div>
                                        <!--end::Input group-->

                                        <!--begin::Input group-->
                                        <div class="fv-row mb-5">
                                            <!--begin::Label-->
                                            <label class="fs-6 fw-semibold mb-2">Select SMS Type *
                                                <i class="fas fa-exclamation-circle ms-2 fs-7" data-bs-toggle="tooltip"
                                                   title="Select SMS Type"></i></label>
                                            <!--End::Label-->
                                            <!--begin::Row-->

                                            <div class="row row-cols-1 row-cols-md-3 row-cols-lg-4 row-cols-xl-4 g-3"
                                                 data-kt-buttons="true"
                                                 data-kt-buttons-target="[data-kt-button='true']">
                                                <!--begin::Col-->
                                                <div class="col">
                                                    <!--begin::Option-->
                                                    <label
                                                        class="btn btn-outline btn-outline-dashed btn-active-light-primary active d-flex text-start p-6"
                                                        data-kt-button="true">
                                                        <!--begin::Radio-->
                                                        <span
                                                            class="form-check form-check-custom form-check-solid form-check-sm align-items-start mt-1">
                                                            <input class="form-check-input" type="radio"
                                                                   name="sms_type" value="text" checked="checked"/>
                                                        </span>
                                                        <!--end::Radio-->
                                                        <!--begin::Info-->
                                                        <span class="ms-5">
                                                            <span class="fs-4 fw-bold text-gray-800 d-block">Text</span>
                                                        </span>
                                                        <!--end::Info-->
                                                    </label>
                                                    <!--end::Option-->
                                                </div>
                                                <!--end::Col-->
                                                <!--begin::Col-->
                                                <div class="col">
                                                    <!--begin::Option-->
                                                    <label
                                                        class="btn btn-outline btn-outline-dashed btn-active-light-primary d-flex text-start p-6"
                                                        data-kt-button="true">
                                                        <!--begin::Radio-->
                                                        <span
                                                            class="form-check form-check-custom form-check-solid form-check-sm align-items-start mt-1">
                                                            <input class="form-check-input" type="radio"
                                                                   name="sms_type" value="flash"/>
                                                        </span>
                                                        <!--end::Radio-->
                                                        <!--begin::Info-->
                                                        <span class="ms-5">
                                                            <span
                                                                class="fs-4 fw-bold text-gray-800 d-block">Flash</span>
                                                        </span>
                                                        <!--end::Info-->
                                                    </label>
                                                    <!--end::Option-->
                                                </div>
                                                <!--end::Col-->
                                                <!--begin::Col-->
                                                <div class="col">
                                                    <!--begin::Option-->
                                                    <label
                                                        class="btn btn-outline btn-outline-dashed btn-active-light-primary d-flex text-start p-6"
                                                        data-kt-button="true">
                                                        <!--begin::Radio-->
                                                        <span
                                                            class="form-check form-check-custom form-check-solid form-check-sm align-items-start mt-1">
                                                            <input class="form-check-input" type="radio"
                                                                   name="sms_type" value="flash_unicode"/>
                                                        </span>
                                                        <!--end::Radio-->
                                                        <!--begin::Info-->
                                                        <span class="ms-5">
                                                            <span
                                                                class="fs-4 fw-bold text-gray-800 d-block">Flash Unicode</span>
                                                        </span>
                                                        <!--end::Info-->
                                                    </label>
                                                    <!--end::Option-->
                                                </div>
                                                <!--end::Col-->

                                                <!--begin::Col-->
                                                <div class="col">
                                                    <!--begin::Option-->
                                                    <label
                                                        class="btn btn-outline btn-outline-dashed btn-active-light-primary d-flex text-start p-6"
                                                        data-kt-button="true">
                                                        <!--begin::Radio-->
                                                        <span
                                                            class="form-check form-check-custom form-check-solid form-check-sm align-items-start mt-1">
                                                            <input class="form-check-input" type="radio"
                                                                   name="sms_type" value="unicode"/>
                                                        </span>
                                                        <!--end::Radio-->
                                                        <!--begin::Info-->
                                                        <span class="ms-5">
                                                            <span
                                                                class="fs-4 fw-bold text-gray-800 d-block">Unicode</span>
                                                        </span>
                                                        <!--end::Info-->
                                                    </label>
                                                    <!--end::Option-->
                                                </div>
                                                <!--end::Col-->
                                            </div>
                                            <!--end::Row-->
                                        </div>
                                        <!--end::Input group-->

                                        <!--begin::Input group-->
                                        <div class="mb-5 fv-row">
                                            <!--begin::Label-->
                                            <label class="required form-label">Enter SMS Content</label>
                                            <!--end::Label-->
                                            <!--begin::Input-->
                                            <textarea id="smsContent" name="sms_content" class="form-control mb-2"
                                                      placeholder=""></textarea>
                                            <!--end::Input-->
                                            <!--begin::Description-->
                                            <div class="row">
                                                <div class="col-6">
                                                    <div class="text-gray-600 text-dark-gray-400 fs-7 fw-semibold" id="smsCountInfo">CHECK YOUR SMS COUNT
                                                    </div>
                                                </div>
                                                <div class="col-6" style="text-align: right">
                                                    <button type="button" class="btn btn-sm btn-light-primary"
                                                            data-bs-toggle="modal"
                                                            data-bs-target="#kt_modal_show_template">Use Template
                                                    </button>
                                                </div>
                                            </div>
                                            <!--end::Description-->
                                        </div>
                                        <!--end::Input group-->

                                        <!--begin::Input group-->
                                        <div class="fv-row mb-5">
                                            <!--begin::Label-->
                                            <label class="required form-label">Schedule SMS</label>
                                            <!--end::Label-->
                                            <!--begin::Row-->
                                            <div class="row row-cols-1 row-cols-md-2 row-cols-lg-2 row-cols-xl-2 g-3"
                                                 data-kt-buttons="true"
                                                 data-kt-buttons-target="[data-kt-button='true']">
                                                <!--begin::Col-->
                                                <div class="col">
                                                    <!--begin::Option-->
                                                    <label
                                                        class="btn btn-outline btn-outline-dashed btn-active-light-primary active d-flex text-start p-6"
                                                        data-kt-button="true">
                                                        <!--begin::Radio-->
                                                        <span
                                                            class="form-check form-check-custom form-check-solid form-check-sm align-items-start mt-1">
                                                            <input class="form-check-input" type="radio"
                                                                   name="is_scheduled"
                                                                   value="0" checked="checked"/>
                                                        </span>
                                                        <!--end::Radio-->
                                                        <!--begin::Info-->
                                                        <span class="ms-5">
                                                            <span
                                                                class="fs-4 fw-bold text-gray-800 d-block">Send Now</span>
                                                        </span>
                                                        <!--end::Info-->
                                                    </label>
                                                    <!--end::Option-->
                                                </div>
                                                <!--end::Col-->
                                                <!--begin::Col-->
                                                <div class="col">
                                                    <!--begin::Option-->
                                                    <label
                                                        class="btn btn-outline btn-outline-dashed btn-active-light-primary d-flex text-start p-6"
                                                        data-kt-button="true">
                                                        <!--begin::Radio-->
                                                        <span
                                                            class="form-check form-check-custom form-check-solid form-check-sm align-items-start mt-1">
                                                            <input class="form-check-input" type="radio"
                                                                   name="is_scheduled"
                                                                   value="1"/>
                                                        </span>
                                                        <!--end::Radio-->
                                                        <!--begin::Info-->
                                                        <span class="ms-5">
                                                            <span
                                                                class="fs-4 fw-bold text-gray-800 d-block">Send Later</span>
                                                        </span>
                                                        <!--end::Info-->
                                                    </label>
                                                    <!--end::Option-->
                                                </div>
                                                <!--end::Col-->
                                            </div>
                                            <!--end::Row-->
                                        </div>
                                        <!--end::Input group-->

                                        <!--begin::Input group-->
                                        <div class="fv-row mb-5 scheduled_at" style="display:none;">
                                            <!--begin::Label-->
                                            <input type="text" class="date_time_picker form-control" name="scheduled_at"
                                                   required="" placeholder="Select Date">
                                            <!--end::Label-->
                                        </div>
                                        <!--end::Input group-->

                                        <!--end::Tab content-->
                                        <div class="d-flex justify-content-end">
                                            <!--begin::Button-->
                                            <a href="/apps/ecommerce/catalog/products.html"
                                               id="kt_ecommerce_add_product_cancel"
                                               class="btn btn-light me-5">Cancel</a>
                                            <!--end::Button-->
                                            <!--begin::Button-->
                                            @csrf
                                            <button type="submit" id="kt_ecommerce_add_product_submit"
                                                    class="btn btn-primary">
                                                <span class="indicator-label">Send SMS</span>
                                                <span class="indicator-progress">Please wait...
                                                <span
                                                    class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                                            </button>
                                            <!--end::Button-->
                                        </div>

                                    </div>
                                    <!--end::Card header-->
                                </div>
                                <!--end::Pricing-->
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <!--end::Main column-->

            <!--begin::Aside column-->
            <div class="ms-8 d-flex flex-column gap-6 gap-lg-10 w-lg-300px">
                <!--begin::Help Section-->
                <div class="card custom-card card-flush py-4">
                    <!--begin::Card header-->
                    <div class="card-header">
                        <!--begin::Card title-->
                        <div class="card-title">
                            <h4>UNSUB</h4>
                        </div>
                        <!--end::Card title-->
                    </div>
                    <!--end::Card header-->
                    <!--begin::Card body-->
                    <div class="card-body pt-0">
                        <!--begin::Description-->
                        <div class="fs-7">UAE Users please ensure that you put "UNSUB 3811 or OPTOUT 3811" at end of
                            each SMS. As per TRA optout option is mandatory. We will not be responsible for any
                            non-delivery arising because of this.
                        </div>
                        <!--end::Description-->
                    </div>
                    <!--end::Card body-->
                </div>
                <!--end::Help Section-->
                <!--begin::Help Section-->
                <div class="card custom-card card-flush py-4">
                    <!--begin::Card header-->
                    <div class="card-header">
                        <!--begin::Card title-->
                        <div class="card-title">
                            <h4>SMS Recipient</h4>
                        </div>
                        <!--end::Card title-->
                    </div>
                    <!--end::Card header-->
                    <!--begin::Card body-->
                    <div class="card-body pt-0">
                        <!--begin::Description-->
                        <div class="fs-7">Before doing any campaign we recommend you to do a testing with the sender id
                            to your number to ensure the sender id is working fine.
                        </div>
                        <!--end::Description-->
                    </div>
                    <!--end::Card body-->
                </div>
                <!--end::Help Section-->
                <!--begin::Help Section-->
                <div class="card custom-card card-flush py-4">
                    <!--begin::Card header-->
                    <div class="card-header">
                        <!--begin::Card title-->
                        <div class="card-title">
                            <h4>SMS Content</h4>
                        </div>
                        <!--end::Card title-->
                    </div>
                    <!--end::Card header-->
                    <!--begin::Card body-->
                    <div class="card-body pt-0">
                        <!--begin::Description-->
                        <div class="fs-7">
                            * 160 Characters are counted as 1 SMS in case of English language & 70 in other language.
                            * One simple text message containing extended GSM character set (~^{}[]\|) is of 70
                            characters long. Check your SMS count before pushing SMS.
                        </div>
                        <!--end::Description-->
                    </div>
                    <!--end::Card body-->
                </div>
                <!--end::Help Section-->
            </div>
        </div>
        <!--end::Modal - New Card-->
        @include('messaging.show-template')
        <!--end::Form-->
    </div>
    <!--end::Container-->
@stop

@section('js')
    <script src="{{ asset('js/script.message.js') }}"></script>
@stop
