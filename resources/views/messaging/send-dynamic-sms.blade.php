@extends('layouts.app')

@section('title', 'Send Dynamic SMS')

@section('content')

    <!--begin::Container-->
    <div class="container-xxl" id="kt_content_container">
        <!--begin::Form-->
        <div id="kt_ecommerce_add_product_form" class="form d-flex flex-column flex-lg-row">
            <!--begin::Main column-->
            <div class="d-flex flex-column flex-row-fluid gap-7 gap-lg-10">
                    <!--begin::General options-->
                    <div class="card card-flush py-4">
                        <!--begin::Card body-->
                        <div class="card-body pt-5">
                            <form class="form" id="sms_recipient" role="tab-panel"
                                  method="POST" action="{{ route('messages.dynamic-sms.post') }}" enctype="multipart/form-data">
                                <!--begin::Input group-->
                                <div class="mb-5 fv-row">
                                    <!--begin::Label-->
                                    <label class="required form-label">Select Sender ID</label>
                                    <!--end::Label-->
                                    <!--begin::Select2-->
                                    <select class="form-select mb-2" name="sender_id" data-control="select2"
                                            data-hide-search="true" data-placeholder="Select an option">
                                        <option></option>
                                        @foreach($senders as $sender)
                                            <option value="{{ $sender->id }}"
                                                    @if($sender->is_default) selected @endif
                                            >{{ $sender->name }}</option>
                                        @endforeach
                                    </select>
                                    <!--end::Select2-->
                                    <div class="form-group">
                                        <a href="{{route('senders.index')}}"
                                           class="btn btn-sm btn-light-primary">
                                            <!--begin::Svg Icon | path: icons/duotune/arrows/arr087.svg-->
                                            <span class="svg-icon svg-icon-2">
                                                                        <svg width="24" height="24" viewBox="0 0 24 24"
                                                                             fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                            <rect opacity="0.5" x="11" y="18" width="12"
                                                                                  height="2" rx="1"
                                                                                  transform="rotate(-90 11 18)"
                                                                                  fill="currentColor"></rect>
                                                                            <rect x="6" y="11" width="12" height="2" rx="1"
                                                                                  fill="currentColor"></rect>
                                                                        </svg>
                                                                    </span>
                                            <!--end::Svg Icon-->Request New Sender ID
                                        </a>
                                    </div>
                                </div>
                                <!--end::Input group-->

                                <!--begin::Input group-->
                                <div class="mb-5 fv-row">
                                    <!--begin::Label-->
                                    <label class="fs-6 fw-semibold mb-2">Select SMS Type *
                                        <i class="fas fa-exclamation-circle ms-2 fs-7" data-bs-toggle="tooltip"
                                           title="Select SMS Type"></i></label>
                                    <!--End::Label-->
                                    <!--begin::Row-->

                                    <div class="row row-cols-1 row-cols-md-3 row-cols-lg-4 row-cols-xl-4 g-3"
                                         data-kt-buttons="true" data-kt-buttons-target="[data-kt-button='true']">
                                        <!--begin::Col-->
                                        <div class="col">
                                            <!--begin::Option-->
                                            <label
                                                class="btn btn-outline btn-outline-dashed btn-active-light-primary active d-flex text-start p-6"
                                                data-kt-button="true">
                                                <!--begin::Radio-->
                                                <span
                                                    class="form-check form-check-custom form-check-solid form-check-sm align-items-start mt-1">
                                                            <input class="form-check-input" type="radio"
                                                                   name="sms_type" value="text" checked="checked"/>
                                                        </span>
                                                <!--end::Radio-->
                                                <!--begin::Info-->
                                                <span class="ms-5">
                                                            <span class="fs-4 fw-bold text-gray-800 d-block">Text</span>
                                                        </span>
                                                <!--end::Info-->
                                            </label>
                                            <!--end::Option-->
                                        </div>
                                        <!--end::Col-->
                                        <!--begin::Col-->
                                        <div class="col">
                                            <!--begin::Option-->
                                            <label
                                                class="btn btn-outline btn-outline-dashed btn-active-light-primary d-flex text-start p-6"
                                                data-kt-button="true">
                                                <!--begin::Radio-->
                                                <span
                                                    class="form-check form-check-custom form-check-solid form-check-sm align-items-start mt-1">
                                                            <input class="form-check-input" type="radio"
                                                                   name="sms_type" value="flash"/>
                                                        </span>
                                                <!--end::Radio-->
                                                <!--begin::Info-->
                                                <span class="ms-5">
                                                            <span class="fs-4 fw-bold text-gray-800 d-block">Flash</span>
                                                        </span>
                                                <!--end::Info-->
                                            </label>
                                            <!--end::Option-->
                                        </div>
                                        <!--end::Col-->
                                        <!--begin::Col-->
                                        <div class="col">
                                            <!--begin::Option-->
                                            <label
                                                class="btn btn-outline btn-outline-dashed btn-active-light-primary d-flex text-start p-6"
                                                data-kt-button="true">
                                                <!--begin::Radio-->
                                                <span
                                                    class="form-check form-check-custom form-check-solid form-check-sm align-items-start mt-1">
                                                            <input class="form-check-input" type="radio"
                                                                   name="sms_type" value="flash_unicode"/>
                                                        </span>
                                                <!--end::Radio-->
                                                <!--begin::Info-->
                                                <span class="ms-5">
                                                            <span
                                                                class="fs-4 fw-bold text-gray-800 d-block">Flash Unicode</span>
                                                        </span>
                                                <!--end::Info-->
                                            </label>
                                            <!--end::Option-->
                                        </div>
                                        <!--end::Col-->

                                        <!--begin::Col-->
                                        <div class="col">
                                            <!--begin::Option-->
                                            <label
                                                class="btn btn-outline btn-outline-dashed btn-active-light-primary d-flex text-start p-6"
                                                data-kt-button="true">
                                                <!--begin::Radio-->
                                                <span
                                                    class="form-check form-check-custom form-check-solid form-check-sm align-items-start mt-1">
                                                            <input class="form-check-input" type="radio"
                                                                   name="sms_type" value="unicode"/>
                                                        </span>
                                                <!--end::Radio-->
                                                <!--begin::Info-->
                                                <span class="ms-5">
                                                            <span class="fs-4 fw-bold text-gray-800 d-block">Unicode</span>
                                                        </span>
                                                <!--end::Info-->
                                            </label>
                                            <!--end::Option-->
                                        </div>
                                        <!--end::Col-->
                                    </div>
                                    <!--end::Row-->
                                </div>
                                <!--end::Input group-->

                                <!--begin::Input group-->
                                <div class="mb-5 fv-row">
                                    <!--begin::Label-->
                                    <label class="required form-label">Select File (xls, xlsx, ods, txt, csv)</label>
                                    <!--end::Label-->
                                    <!--begin::Input-->
                                    <input type="file" class="form-control" name="file" id="file">
                                    <!--end::Input-->
                                    <!--begin::Description-->
                                    <div class="row">
                                        <div class="text-muted fs-7" id="smsCountInfo">
                                            <a target="_blank" href="{{asset('/media/FileDataSample.xlsx')}}">File data
                                                instructions</a>
                                        </div>
                                    </div>
                                    <!--end::Description-->
                                </div>
                                <!--end::Input group-->

                                <!--begin::Input group-->
                                <div class="mb-5 fv-row">
                                    <!--begin::Label-->
                                    <label class="required form-label">Enter SMS Content</label>
                                    <!--end::Label-->
                                    <!--begin::Input-->
                                    <textarea id="smsContent" name="sms_content" class="form-control mb-2"
                                              placeholder=""></textarea>
                                    <!--end::Input-->
                                    <!--begin::Description-->
                                    <div class="row">
                                        <div class="col-6">
                                            <div class="text-muted fs-7" id="smsCountInfo">0 Characters | 1530 Characters Left | 1 SMS (160 Char./SMS)
                                            </div>
                                        </div>
                                        <div class="col-6" style="text-align: right">
                                            <button type="button" class="btn btn-sm btn-light-primary"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#kt_modal_show_template">Use Template
                                            </button>
                                        </div>
                                    </div>
                                    <!--end::Description-->
                                </div>
                                <!--end::Input group-->

                                <!--begin::Input group-->
                                <div class="mb-5 fv-row">
                                    <!--begin::Label-->
                                    <label class="required form-label">Schedule SMS</label>
                                    <!--end::Label-->
                                    <!--begin::Row-->
                                    <div class="row row-cols-1 row-cols-md-2 row-cols-lg-2 row-cols-xl-2 g-3"
                                         data-kt-buttons="true" data-kt-buttons-target="[data-kt-button='true']">
                                        <!--begin::Col-->
                                        <div class="col">
                                            <!--begin::Option-->
                                            <label
                                                class="btn btn-outline btn-outline-dashed btn-active-light-primary active d-flex text-start p-6"
                                                data-kt-button="true">
                                                <!--begin::Radio-->
                                                <span
                                                    class="form-check form-check-custom form-check-solid form-check-sm align-items-start mt-1">
                                                            <input class="form-check-input" type="radio" name="is_scheduled"
                                                                   value="0" checked="checked"/>
                                                        </span>
                                                <!--end::Radio-->
                                                <!--begin::Info-->
                                                <span class="ms-5">
                                                            <span class="fs-4 fw-bold text-gray-800 d-block">Send Now</span>
                                                        </span>
                                                <!--end::Info-->
                                            </label>
                                            <!--end::Option-->
                                        </div>
                                        <!--end::Col-->
                                        <!--begin::Col-->
                                        <div class="col">
                                            <!--begin::Option-->
                                            <label
                                                class="btn btn-outline btn-outline-dashed btn-active-light-primary d-flex text-start p-6"
                                                data-kt-button="true">
                                                <!--begin::Radio-->
                                                <span
                                                    class="form-check form-check-custom form-check-solid form-check-sm align-items-start mt-1">
                                                            <input class="form-check-input" type="radio" name="is_scheduled"
                                                                   value="1"/>
                                                        </span>
                                                <!--end::Radio-->
                                                <!--begin::Info-->
                                                <span class="ms-5">
                                                            <span
                                                                class="fs-4 fw-bold text-gray-800 d-block">Send Later</span>
                                                        </span>
                                                <!--end::Info-->
                                            </label>
                                            <!--end::Option-->
                                        </div>
                                        <!--end::Col-->
                                    </div>
                                    <!--end::Row-->
                                </div>
                                <!--end::Input group-->

                                <!--begin::Input group-->
                                <div class="mb-5 fv-row" id="scheduled_at" style="display:none;">
                                    <!--begin::Label-->
                                    <input type="text" class="date_time_picker form-control" name="scheduled_at"
                                           required="" placeholder="Select Date">
                                    <!--end::Label-->
                                </div>
                                <!--end::Input group-->

                                <!--end::Tab content-->
                                <div class="d-flex justify-content-end">
                                    <!--begin::Button-->
                                    <a href=""
                                       id="kt_ecommerce_add_product_cancel" class="btn btn-light me-5">Cancel</a>
                                    <!--end::Button-->
                                    <!--begin::Button-->
                                    @csrf
                                    <button type="submit" id="kt_ecommerce_add_product_submit"
                                            class="btn btn-primary">
                                        <span class="indicator-label">Send SMS</span>
                                        <span class="indicator-progress">Please wait...
                                                <span
                                                    class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                                    </button>
                                    <!--end::Button-->
                                </div>
                            </form>

                        </div>
                        <!--end::Card header-->
                    </div>
                    <!--end::Pricing-->
                </div>
            <!--end::Main column-->
            <!--begin::Aside column-->
            <div class="ms-8 d-flex flex-column gap-6 gap-lg-10 w-lg-300px">
                <!--begin::Help Section-->
                <div class="card custom-card card-flush py-4">
                    <!--begin::Card header-->
                    <div class="card-header">
                        <!--begin::Card title-->
                        <div class="card-title">
                            <h4>UNSUB</h4>
                        </div>
                        <!--end::Card title-->
                    </div>
                    <!--end::Card header-->
                    <!--begin::Card body-->
                    <div class="card-body pt-0">
                        <!--begin::Description-->
                        <div class="fs-7">UAE Users please ensure that you put "UNSUB 3811 or OPTOUT 3811" at end of
                            each SMS. As per TRA optout option is mandatory. We will not be responsible for any
                            non-delivery arising because of this.
                        </div>
                        <!--end::Description-->
                    </div>
                    <!--end::Card body-->
                </div>
                <!--end::Help Section-->
                <!--begin::Help Section-->
                <div class="card custom-card card-flush py-4">
                    <!--begin::Card header-->
                    <div class="card-header">
                        <!--begin::Card title-->
                        <div class="card-title">
                            <h4>SMS Recipient</h4>
                        </div>
                        <!--end::Card title-->
                    </div>
                    <!--end::Card header-->
                    <!--begin::Card body-->
                    <div class="card-body pt-0">
                        <!--begin::Description-->
                        <div class="fs-7">Before doing any campaign we recommend you to do a testing with the sender id
                            to your number to ensure the sender id is working fine.
                        </div>
                        <!--end::Description-->
                    </div>
                    <!--end::Card body-->
                </div>
                <!--end::Help Section-->
                <!--begin::Help Section-->
                <div class="card custom-card card-flush py-4">
                    <!--begin::Card header-->
                    <div class="card-header">
                        <!--begin::Card title-->
                        <div class="card-title">
                            <h4>SMS Content</h4>
                        </div>
                        <!--end::Card title-->
                    </div>
                    <!--end::Card header-->
                    <!--begin::Card body-->
                    <div class="card-body pt-0">
                        <!--begin::Description-->
                        <div class="fs-7">
                            * 160 Characters are counted as 1 SMS in case of English language & 70 in other language.
                            * One simple text message containing extended GSM character set (~^{}[]\|) is of 70
                            characters long. Check your SMS count before pushing SMS.
                        </div>
                        <!--end::Description-->
                    </div>
                    <!--end::Card body-->
                </div>
                <!--end::Help Section-->
            </div>
        </div>
        <!--end::Modal - New Card-->
        @include('messaging.show-template')
        <!--end::Form-->
    </div>
    <!--end::Container-->
@stop

@section('js')
    <script src="{{ asset('js/script.message.js') }}"></script>
@stop
