@extends('layouts.app')

@section('title', 'Phonebook')

@section('content')
    <!--begin::Container-->
    <div class="container-xxl" id="kt_content_container">
        <!--begin::Contacts App- Getting Started-->
        <div class="row g-7">
            @include('contacts.content')
            <!--begin::Search-->
            <div class="col-lg-6 col-xl-9">
                <!--begin::Contacts-->
                <div class="card card-flush" id="kt_contacts_list">
                    <!--begin::Card header-->
                    <div class="card-header border-0 pt-6">
                        <div class="card-title">
                            <div class="row form-group my-1">
                                <div class="col-md-3 mb-2">
                                    <input id="searchInput" type="text" class="form-control form-control-solid" placeholder="Search"/>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <input id="filterByNameOperator" type="text" class="form-control form-control-solid" placeholder="Search Operators"/>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <select class="form-select" id="filterByGroup">
                                        <option value="">Select Groups</option>
                                        @foreach($groups as $group)
                                            <option value="{{$group->id}}">{{$group->name}}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <select class="form-select" id="filterByStatus">
                                        <option value="">Select Status</option>
                                        <option value="enabled">Enabled</option>
                                        <option value="disabled">Disabled</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--end::Card header-->
                    <!--begin::Card body-->
                    <div class="card-body pt-5" id="kt_contacts_list_body">
                        <!--begin::Table-->
                        <table class="table align-middle table-row-dashed fs-6 gy-5" id="kt_table_contacts">
                        </table>
                        <!--end::Table-->
                    </div>
                    <!--end::Card body-->
                </div>
                <!--end::Contacts-->
            </div>
        </div>
        <!--end::Contacts App- Getting Started-->
    </div>
    <!--end::Container-->
@stop

@section('js')
    <script src="{{ asset('js/script.contact.js') }}"></script>
@stop
