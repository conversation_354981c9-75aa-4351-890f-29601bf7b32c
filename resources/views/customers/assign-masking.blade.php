@extends('layouts.app')

@section('title', 'Assign Masking Number')

@section('content')
    <!--begin::Container-->
    <div class="container-xxl" id="kt_content_container">
        <div class="row">
            <div class="col-lg-12 col-xl-12">
                <div class="card card-flush h-lg-100" id="kt_contacts_main">
                    <div class="card-header pt-7" id="kt_chat_contacts_header">
                        <div class="card-title">
                            <span class="svg-icon svg-icon-1 me-2">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M20 14H18V10H20C20.6 10 21 10.4 21 11V13C21 13.6 20.6 14 20 14ZM21 19V17C21 16.4 20.6 16 20 16H18V20H20C20.6 20 21 19.6 21 19ZM21 7V5C21 4.4 20.6 4 20 4H18V8H20C20.6 8 21 7.6 21 7Z" fill="currentColor"/>
                                    <path opacity="0.3" d="M17 22H3C2.4 22 2 21.6 2 21V3C2 2.4 2 2 3 2H17C17.6 2 18 2.4 18 3V21C18 21.6 17.6 22 17 22ZM10 7C8.9 7 8 7.9 8 9C8 10.1 8.9 11 10 11C11.1 11 12 10.1 12 9C12 7.9 11.1 7 10 7ZM13.3 16C14 16 14.5 15.3 14.3 14.7C13.7 13.2 12 12 10.1 12C8.10001 12 6.49999 13.1 5.89999 14.7C5.59999 15.3 6.19999 16 7.39999 16H13.3Z" fill="currentColor"/>
                                </svg>
                            </span>
                            <h2>Assign Masking Number</h2>
                        </div>
                    </div>

                    <div class="card-body pt-5">
                        <form action="{{ route('admin.assign.masking.post') }}" method="POST">
                            @csrf
                            <!--begin::Default example-->
                            <div class="fv-row mb-7">
                                <label class="fs-6 fw-semibold form-label mt-3">
                                    <span class="required">Select Customer</span>
                                    <i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip" title="Select the Customer."></i>
                                </label>
                                <div class="input-group flex-nowrap">
                                    <span class="input-group-text">
                                        <i class="ki-duotone ki-notepad-bookmark fs-3"><span class="path1"></span><span class="path2"></span><span class="path3"></span><span class="path4"></span><span class="path5"></span><span class="path6"></span></i>
                                    </span>
                                    <div class="overflow-hidden flex-grow-1">
                                        <select required name="user_id" class="form-select rounded-start-0" data-control="select2" data-placeholder="Select a Customer">
                                            <option value="">Select Customer</option>
                                            @foreach($customers as $customer)
                                                <option value="{{ $customer->id }}" {{ $user->id == $customer->id ? 'selected' : '' }}>
                                                    {{ $customer->name }} ({{ $customer->username }} - {{ $customer->phone }} - {{ $customer->email }})
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- Masking Number Dropdown -->
                            <div class="fv-row mb-7">
                                <label class="fs-6 fw-semibold form-label mt-3">
                                    <span class="required">Masking Number</span>
                                    <i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip" title="Select Masking Number."></i>
                                </label>
                                <div class="input-group flex-nowrap">
                                    <span class="input-group-text">
                                        <i class="ki-duotone ki-notepad-bookmark fs-3"><span class="path1"></span><span class="path2"></span><span class="path3"></span><span class="path4"></span><span class="path5"></span><span class="path6"></span></i>
                                    </span>
                                    <div class="overflow-hidden flex-grow-1">
                                        <select required name="sender_id" class="form-select rounded-start-0" data-control="select2" data-placeholder="Select Masking Number.">
                                            <option value="">Select Masking Number</option>
                                            @foreach($maskingNumbers as $mask)
                                                <option value="{{ $mask->id }}">
                                                    {{ $mask->name }} {{ $mask->server ? '('.$mask->server->name.'-'.$mask->status.')' : ''}}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="separator mb-6"></div>

                            <div class="d-flex justify-content-end">
                                <button type="reset" class="btn btn-light me-3">Cancel</button>
                                <button type="submit" class="btn btn-primary">
                                    <span class="indicator-label">Save</span>
                                    <span class="indicator-progress">Please wait...
                                    <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                                    </span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop
