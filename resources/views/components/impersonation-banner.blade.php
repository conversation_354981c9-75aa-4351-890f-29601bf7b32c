@if(isset($isImpersonating) && $isImpersonating)
    @php
        $impersonator = \App\Models\User::find($impersonatorId);
        $currentUser = auth()->user();
    @endphp

    <div class="container-xxl">
        <div class="alert alert-warning d-flex align-items-center py-2 py-md-3 px-3 px-md-4 mb-3" style="position: relative; border-left: 4px solid #f1c40f; min-height: auto; box-shadow: 0 2px 6px rgba(0,0,0,0.1); border-radius: 0.475rem; margin-bottom: 10px !important;">
        <!--begin::Icon-->
        <i class="ki-duotone ki-user-tick fs-4 fs-md-3 text-warning me-2 me-md-3 flex-shrink-0">
            <span class="path1"></span>
            <span class="path2"></span>
            <span class="path3"></span>
        </i>
        <!--end::Icon-->

        <!--begin::Content-->
        <div class="flex-grow-1 min-w-0">
            <div class="d-flex flex-column flex-sm-row align-items-start align-items-sm-center">
                <div class="me-sm-3 mb-1 mb-sm-0 text-truncate">
                    <span class="fw-bold text-dark fs-8 fs-md-7 me-1 me-md-2">Impersonating:</span>
                    <span class="text-gray-800 fs-8 fs-md-7">{{ $currentUser->name }}</span>
                    @if($impersonator)
                        <span class="text-muted fs-9 fs-md-8 ms-1 ms-md-2 d-none d-sm-inline">(Admin: {{ $impersonator->name }})</span>
                    @endif
                </div>
            </div>
        </div>
        <!--end::Content-->

        <!--begin::Actions-->
        <div class="ms-2 ms-md-auto flex-shrink-0">
            <form method="POST" action="{{ route('impersonate.stop') }}" style="display: inline;">
                @csrf
                <button type="submit" class="btn btn-light-warning btn-sm px-2 px-md-3 py-1 py-md-2" onclick="return confirm('Stop impersonating and return to your account?')" title="Stop Impersonation">
                    <i class="ki-duotone ki-exit-left fs-7 fs-md-6 me-0 me-md-1">
                        <span class="path1"></span>
                        <span class="path2"></span>
                    </i>
                    <span class="d-none d-md-inline">Stop</span>
                </button>
            </form>
        </div>
        <!--end::Actions-->
        </div>
    </div>
@endif
