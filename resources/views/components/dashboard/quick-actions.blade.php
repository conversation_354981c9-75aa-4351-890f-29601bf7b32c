{{-- 
    Dashboard Quick Actions Component
    Displays common admin tasks with icons and descriptions
    
    Props:
    - $actions: Array of quick action items with properties:
      - icon: Icon class name
      - title: Action title
      - description: Action description
      - url: Action URL
      - color: Color theme (primary, warning, info, success)
--}}

@props([
    'actions' => []
])

<!-- Quick Actions Panel (Right Column) -->
<div class="col-xl-4">
    <!--begin::Card-->
    <div class="card card-flush h-lg-100">
        <!--begin::Header-->
        <div class="card-header pt-5">
            <!--begin::Title-->
            <h3 class="card-title align-items-start flex-column">
                <span class="card-label fw-bold text-dark">Quick Actions</span>
                <span class="text-gray-400 mt-1 fw-semibold fs-6">Common admin tasks</span>
            </h3>
            <!--end::Title-->
        </div>
        <!--end::Header-->
        <!--begin::Body-->
        <div class="card-body pt-5">
            @if(count($actions) > 0)
                @foreach($actions as $action)
                    <!--begin::Item-->
                    <div class="d-flex align-items-center {{ !$loop->last ? 'mb-7' : '' }}">
                        <!--begin::Symbol-->
                        <div class="symbol symbol-50px me-5">
                            <span class="symbol-label bg-light-{{ $action['color'] ?? 'primary' }}">
                                <i class="{{ $action['icon'] }} fs-2x text-{{ $action['color'] ?? 'primary' }}">
                                    @if(isset($action['icon_paths']))
                                        @foreach($action['icon_paths'] as $path)
                                            <span class="path{{ $loop->iteration }}"></span>
                                        @endforeach
                                    @endif
                                </i>
                            </span>
                        </div>
                        <!--end::Symbol-->
                        <!--begin::Text-->
                        <div class="d-flex flex-column">
                            <a href="{{ $action['url'] }}" class="text-dark text-hover-primary fs-6 fw-bold">{{ $action['title'] }}</a>
                            <span class="text-gray-400 fw-semibold fs-7">{{ $action['description'] }}</span>
                        </div>
                        <!--end::Text-->
                    </div>
                    <!--end::Item-->
                @endforeach
            @else
                <div class="text-center py-10">
                    <i class="ki-duotone ki-setting-2 fs-4x text-gray-400 mb-5">
                        <span class="path1"></span>
                        <span class="path2"></span>
                    </i>
                    <h3 class="text-gray-800 fw-bold mb-3">No Quick Actions</h3>
                    <p class="text-gray-400 fw-semibold fs-6">Quick actions will appear here when available.</p>
                </div>
            @endif
        </div>
        <!--end::Body-->
    </div>
    <!--end::Card-->
</div>
