{{--
    Dashboard Balance Summary Component
    Displays balance information in a purple gradient card

    Props:
    - $balance: Current balance amount
    - $balanceMsgs: Balance converted to message count
    - $showChart: Whether to show the chart (default: true)
--}}

@props([
    'balance' => 0,
    'balanceMsgs' => 0,
    'smsLastWeek' => 0,
    'costLastWeek' => 0,
    'smsInLastMonth' => 0,
    'costInLastMonth' => 0,
    'lastMonthName' => 'Last Month',
    'showChart' => true
])

<!--begin::Col-->
<div class="col-12 col-xl-4 mb-5 mb-xl-0">
    <!--begin::Card widget 1-->
    <div class="card card-flush border-0 h-100" data-bs-theme="light" style="background-color: #7239EA">
        <!--begin::Header-->
        <div class="card-header pt-4 pb-2">
            <!--begin::Title-->
            <h3 class="card-title">
                <span class="text-white fs-5 fw-bold me-2">
                    @if(auth()->user()->hasRole('super-admin'))
                        Total System Balance
                    @elseif(auth()->user()->hasRole('master-reseller'))
                        Company Balance
                    @elseif(auth()->user()->hasRole('reseller'))
                        Company Balance
                    @else
                        Balance Summary
                    @endif
                </span>
            </h3>
            <!--end::Title-->
        </div>
        <!--end::Header-->
        <!--begin::Body-->
        <div class="card-body d-flex justify-content-between flex-column pt-2 px-4 pb-4">
            <!--begin::Current Balance Row-->
            <div class="d-flex flex-column flex-sm-row gap-3 mb-4">
                <!--begin::Balance Stat-->
                <div class="rounded flex-fill py-3 px-4" style="border: 1px dashed rgba(255, 255, 255, 0.2)">
                    <div class="d-flex align-items-center">
                        <div class="text-white fs-2x fs-sm-3 fw-bold" data-metric="total_balance">৳{{ number_format($balance, 2) }}</div>
                    </div>
                    <div class="fw-semibold fs-7 text-white opacity-50">
                        @if(auth()->user()->hasRole('super-admin'))
                            Total System Balance
                        @else
                            Current Balance
                        @endif
                    </div>
                </div>
                <!--end::Balance Stat-->
                <!--begin::Messages Stat-->
                <div class="rounded flex-fill py-3 px-4" style="border: 1px dashed rgba(255, 255, 255, 0.2)">
                    <div class="d-flex align-items-center">
                        <div class="text-white fs-2x fs-sm-3 fw-bold" data-metric="total_balance_msgs">{{ number_format($balanceMsgs) }}</div>
                    </div>
                    <div class="fw-semibold fs-7 text-white opacity-50">Balance (MSGS)</div>
                </div>
                <!--end::Messages Stat-->
            </div>
            <!--end::Current Balance Row-->

            <!--begin::Usage Statistics-->
            <div class="px-2 mb-3">
                <div class="separator separator-dashed border-white opacity-25 mb-3"></div>

                <!--begin::Last Week Stats-->
                <div class="mb-3">
                    <div class="text-white fs-7 fw-semibold opacity-75 mb-2">Last Week Usage</div>
                    <div class="d-flex flex-column flex-sm-row gap-2">
                        <div class="flex-fill">
                            <span class="text-white fs-6 fw-bold" data-metric="sms_last_week">{{ number_format($smsLastWeek) }}</span>
                            <span class="text-white fs-8 opacity-50 ms-1">SMS</span>
                        </div>
                        <div class="flex-fill">
                            <span class="text-white fs-6 fw-bold" data-metric="cost_last_week">৳{{ number_format($costLastWeek, 2) }}</span>
                            <span class="text-white fs-8 opacity-50 ms-1">Cost</span>
                        </div>
                    </div>
                </div>
                <!--end::Last Week Stats-->

                <!--begin::Last Month Stats-->
                <div class="mb-2">
                    <div class="text-white fs-7 fw-semibold opacity-75 mb-2">{{ $lastMonthName }} Usage</div>
                    <div class="d-flex flex-column flex-sm-row gap-2">
                        <div class="flex-fill">
                            <span class="text-white fs-6 fw-bold" data-metric="sms_in_last_month">{{ number_format($smsInLastMonth) }}</span>
                            <span class="text-white fs-8 opacity-50 ms-1">SMS</span>
                        </div>
                        <div class="flex-fill">
                            <span class="text-white fs-6 fw-bold" data-metric="cost_in_last_month">৳{{ number_format($costInLastMonth, 2) }}</span>
                            <span class="text-white fs-8 opacity-50 ms-1">Cost</span>
                        </div>
                    </div>
                </div>
                <!--end::Last Month Stats-->
            </div>
            <!--end::Usage Statistics-->
            @if($showChart)
                <!--begin::Chart-->
                <div id="kt_card_widget_1_chart" data-kt-chart-color="#8F5FF4" style="height: 60px"></div>
                <!--end::Chart-->
            @endif
        </div>
        <!--end::Body-->
    </div>
    <!--end::Card widget 1-->
</div>
<!--end::Col-->
