{{--
    Dashboard Action Queue Component
    Displays priority action items requiring immediate attention

    Props:
    - $actionQueue: Array of action items with properties:
      - priority: Priority number (1-5)
      - urgency_class: CSS class for urgency (danger, warning, info)
      - title: Action title
      - description: Action description
      - user: User who created the item
      - created_at: Creation timestamp
      - actions: Array of action buttons with url, class, text
--}}

@props([
    'actionQueue' => []
])

<!-- Action Queue (Left Column) -->
<div class="col-12 col-xl-8 mb-5 mb-xl-0">
    <!--begin::Card-->
    <div class="card card-flush h-lg-100" style="overflow: hidden;">
        <!--begin::Header-->
        <div class="card-header pt-5 pb-3">
            <!--begin::Title-->
            <h3 class="card-title align-items-start flex-column">
                <span class="card-label fw-bold text-dark fs-5">Priority Action Queue</span>
                <span class="text-gray-400 mt-1 fw-semibold fs-7 d-none d-sm-block">Items requiring immediate attention</span>
            </h3>
            <!--end::Title-->
            <!--begin::Toolbar-->
            <div class="card-toolbar">
                <button type="button" class="btn btn-sm btn-light-primary px-3" onclick="refreshActionQueue()">
                    <i class="ki-duotone ki-arrows-circle fs-3">
                        <span class="path1"></span>
                        <span class="path2"></span>
                    </i>
                    <span class="d-none d-sm-inline ms-1">Refresh</span>
                </button>
            </div>
            <!--end::Toolbar-->
        </div>
        <!--end::Header-->
        <!--begin::Body-->
        <div class="card-body pt-3 overflow-hidden" id="action-queue-container" style="max-width: 100%; overflow-x: hidden;">
            @if(count($actionQueue) > 0)
                @foreach($actionQueue as $item)
                    <div class="action-queue-item d-flex flex-column flex-sm-row align-items-start align-items-sm-center border border-dashed border-gray-300 rounded px-4 py-3 mb-4">
                        <!--begin::Priority indicator-->
                        <div class="d-flex align-items-center mb-2 mb-sm-0 me-sm-3">
                            <div class="badge badge-light-{{ $item['urgency_class'] }} fw-bold fs-8 px-2 py-1">
                                P{{ $item['priority'] }}
                            </div>
                        </div>
                        <!--end::Priority indicator-->

                        <!--begin::Info-->
                        <div class="flex-grow-1 mb-3 mb-sm-0 me-sm-3">
                            <!--begin::Title-->
                            <a href="#" class="text-gray-800 text-hover-primary fs-6 fw-bold d-block">{{ $item['title'] }}</a>
                            <!--end::Title-->
                            <!--begin::Description-->
                            <span class="text-gray-400 fw-semibold d-block fs-7 mt-1">{{ $item['description'] }}</span>
                            <!--end::Description-->
                            <!--begin::User and time-->
                            <span class="text-gray-400 fw-semibold d-block fs-8 mt-1">
                                <span class="d-inline d-sm-none">{{ $item['user'] }}</span>
                                <span class="d-none d-sm-inline">{{ $item['user'] }} • {{ $item['created_at']->diffForHumans() }}</span>
                            </span>
                            <!--end::User and time-->
                        </div>
                        <!--end::Info-->

                        <!--begin::Actions-->
                        <div class="d-flex flex-wrap gap-2 w-100 w-sm-auto">
                            @foreach($item['actions'] as $action)
                                <a href="{{ $action['url'] }}" class="btn {{ $action['class'] }} btn-sm flex-fill flex-sm-grow-0">
                                    {{ $action['text'] }}
                                </a>
                            @endforeach
                        </div>
                        <!--end::Actions-->
                    </div>
                @endforeach
            @else
                <div class="text-center py-10">
                    <i class="ki-duotone ki-check-circle fs-4x text-success mb-5">
                        <span class="path1"></span>
                        <span class="path2"></span>
                    </i>
                    <h3 class="text-gray-800 fw-bold mb-3">All caught up!</h3>
                    <p class="text-gray-400 fw-semibold fs-6">No urgent items require your attention right now.</p>
                </div>
            @endif
        </div>
        <!--end::Body-->
    </div>
    <!--end::Card-->
</div>
