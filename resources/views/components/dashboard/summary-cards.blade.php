{{--
    Dashboard Summary Cards Component
    Displays summary statistics in gradient cards

    Props:
    - $cards: Array of summary card objects with properties:
      - value: Main value to display
      - title: Card title
      - subtitle: Card subtitle
      - color: Color theme (success, warning, danger)
      - icon: Icon class name
      - action_url: URL for action button
--}}

@props([
    'cards' => []
])

@if(count($cards) > 0)
    <!-- Summary Cards Row -->
    <div class="row g-3 g-sm-4 g-xl-5 mb-3 mb-xl-4">
        @foreach($cards as $card)
            <div class="col-6 col-sm-6 col-xl-3 mb-2 mb-xl-0">
                <!--begin::Card widget-->
                <div class="card card-flush border-0 h-100 summary-card-compact" data-bs-theme="light" style="background: linear-gradient(112.14deg, {{
                    $card['color'] === 'success' ? '#00A3FF 0%, #0077B6 100%' :
                    ($card['color'] === 'warning' ? '#FFB800 0%, #FF8A00 100%' :
                    ($card['color'] === 'info' ? '#009EF7 0%, #0077B6 100%' :
                    ($card['color'] === 'primary' ? '#7239EA 0%, #5014D0 100%' :
                    '#F1416C 0%, #E4002B 100%'))) }})">
                    <!--begin::Header-->
                    <div class="card-header pt-2 pt-sm-3 pb-1">
                        <!--begin::Title-->
                        <div class="card-title d-flex flex-column">
                            <!--begin::Amount-->
                            <span class="fs-2 fs-sm-1 fw-bold text-white me-2 lh-1 ls-n1">{{ $card['value'] }}</span>
                            <!--end::Amount-->
                            <!--begin::Subtitle-->
                            <span class="text-white opacity-75 fw-semibold fs-9 fs-sm-7 mt-1">{{ $card['title'] }}</span>
                            <!--end::Subtitle-->
                        </div>
                        <!--end::Title-->
                    </div>
                    <!--end::Header-->
                    <!--begin::Card body-->
                    <div class="card-body d-flex flex-column justify-content-end pe-0 pt-1 pb-2 pb-sm-3">
                        <!--begin::Title-->
                        <span class="fs-9 fs-sm-7 fw-bolder text-white opacity-75 d-none d-sm-block">{{ $card['subtitle'] }}</span>
                        <!--end::Title-->
                        <!--begin::Progress-->
                        <div class="d-flex align-items-center justify-content-between mb-1 mb-sm-2">
                        </div>
                        <!--end::Progress-->
                        <!--begin::Action Center-->
                        <div class="">
                            <a href="{{ $card['action_url'] }}" class="btn btn-sm btn-light text-dark fw-bold px-2 px-sm-3 py-1">
                                <span class="d-none d-sm-inline fs-8">View Details</span>
                                <span class="d-inline d-sm-none fs-9">View</span>
                            </a>
                        </div>
                        <!--end::Action Center-->
                    </div>
                    <!--end::Card body-->
                </div>
                <!--end::Card widget-->
            </div>
        @endforeach
    </div>
@endif
