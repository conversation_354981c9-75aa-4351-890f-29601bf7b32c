{{-- 
    Dashboard Critical Alerts Component
    Displays critical alerts with different types and actions
    
    Props:
    - $alerts: Array of alert objects with properties:
      - type: 'critical', 'financial', 'info'
      - icon: Icon class name
      - title: Alert title
      - message: Alert message
      - action_url: URL for action button
      - action_text: Text for action button
--}}

@props([
    'alerts' => []
])

@if(count($alerts) > 0)
    <!-- Critical Alerts Section -->
    <div class="alert-section mb-4" id="critical-alerts">
        <div class="row g-2 g-sm-3">
            @foreach($alerts as $alert)
                @php
                    $alertColor = match($alert['type']) {
                        'critical' => 'danger',
                        'financial' => 'warning',
                        'service' => 'info',
                        default => 'info'
                    };
                    $bgGradient = match($alert['type']) {
                        'critical' => 'linear-gradient(135deg, #F1416C 0%, #E4002B 100%)',
                        'financial' => 'linear-gradient(135deg, #FFC700 0%, #FF8A00 100%)',
                        'service' => 'linear-gradient(135deg, #009EF7 0%, #0077B6 100%)',
                        default => 'linear-gradient(135deg, #009EF7 0%, #0077B6 100%)'
                    };
                @endphp

                <div class="col-12 col-sm-6 col-xl-4 mb-2 mb-sm-3">
                    <!--begin::Alert Card-->
                    <div class="card card-flush border-0 h-100 alert-card" data-alert-type="{{ $alert['type'] }}">
                        <!--begin::Card Header-->
                        <div class="card-header align-items-center py-2 px-3" style="background: {{ $bgGradient }}; min-height: 45px;">
                            <div class="d-flex align-items-center w-100">
                                <!--begin::Icon-->
                                <div class="symbol symbol-25px symbol-sm-30px me-2">
                                    <div class="symbol-label bg-white bg-opacity-20">
                                        <i class="{{ $alert['icon'] }} fs-4 fs-sm-3 text-white"></i>
                                    </div>
                                </div>
                                <!--end::Icon-->

                                <!--begin::Title and Count-->
                                <div class="flex-grow-1">
                                    <h6 class="text-white fw-bold mb-0 fs-8 fs-sm-7">{{ $alert['title'] }}</h6>
                                    @if(isset($alert['count']) && $alert['count'] > 0)
                                        <span class="badge badge-light-{{ $alertColor }} fs-9 mt-1">{{ $alert['count'] }}</span>
                                    @endif
                                </div>
                                <!--end::Title and Count-->

                                <!--begin::Priority Indicator-->
                                @if(isset($alert['priority']))
                                    <div class="priority-indicator priority-{{ $alert['priority'] }} d-none d-sm-block"></div>
                                @endif
                                <!--end::Priority Indicator-->
                            </div>
                        </div>
                        <!--end::Card Header-->

                        <!--begin::Card Body-->
                        <div class="card-body p-2 p-sm-3">
                            <!--begin::Message-->
                            <p class="text-gray-700 fs-9 fs-sm-8 mb-2 lh-base">{{ $alert['message'] }}</p>
                            <!--end::Message-->

                            <!--begin::Action-->
                            <div class="d-flex justify-content-end">
                                <a href="{{ $alert['action_url'] }}"
                                   class="btn btn-sm btn-{{ $alertColor }} btn-active-light-{{ $alertColor }} fw-semibold px-2 px-sm-3 py-1">
                                    <span class="d-none d-sm-inline">{{ $alert['action_text'] }}</span>
                                    <span class="d-inline d-sm-none">View</span>
                                    <i class="ki-duotone ki-arrow-right fs-8 ms-1">
                                        <span class="path1"></span>
                                        <span class="path2"></span>
                                    </i>
                                </a>
                            </div>
                            <!--end::Action-->
                        </div>
                        <!--end::Card Body-->
                    </div>
                    <!--end::Alert Card-->
                </div>
            @endforeach
        </div>
    </div>
@endif
