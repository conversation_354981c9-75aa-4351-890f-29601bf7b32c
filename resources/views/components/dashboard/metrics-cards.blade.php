{{--
    Dashboard Metrics Cards Component
    Displays SMS and cost metrics in card format

    Props:
    - $smsLastWeek: Number of SMS sent last week
    - $costLastWeek: Cost of SMS last week
    - $smsInMonth: Number of SMS sent in specified month
    - $costInMonth: Cost of SMS in specified month
    - $monthName: Name of the month for display
--}}

@props([
    'smsLastWeek' => 0,
    'costLastWeek' => 0,
    'smsInMonth' => 0,
    'costInMonth' => 0,
    'monthName' => 'May'
])

<!--begin::SMS Last Week Card-->
<div class="col-sm-6 col-xl-2 mb-3">
        <!--begin::Card widget 2-->
        <div class="card h-100">
            <!--begin::Body-->
            <div class="card-body d-flex justify-content-center align-items-center flex-column py-3 px-3">
                <!--begin::Section-->
                <div class="d-flex flex-column text-center w-100">
                    <!--begin::Number-->
                    <span class="fw-semibold fs-2x text-gray-800 lh-1 ls-n2 mb-1">{{ number_format($smsLastWeek) }}</span>
                    <!--end::Number-->
                    <!--begin::Follower-->
                    <span class="fw-semibold fs-8 text-gray-400">SMS LAST WEEK</span>
                    <!--end::Follower-->
                </div>
                <!--end::Section-->
            </div>
            <!--end::Body-->
        </div>
        <!--end::Card widget 2-->
    </div>
    <!--end::SMS Last Week Card-->

    <!--begin::Cost Last Week Card-->
    <div class="col-sm-6 col-xl-2 mb-3">
        <!--begin::Card widget 2-->
        <div class="card h-100">
            <!--begin::Body-->
            <div class="card-body d-flex justify-content-center align-items-center flex-column py-3 px-3">
                <!--begin::Section-->
                <div class="d-flex flex-column text-center w-100">
                    <!--begin::Number-->
                    <span class="fw-semibold fs-2x text-gray-800 lh-1 ls-n2 mb-1">৳{{ number_format($costLastWeek, 2) }}</span>
                    <!--end::Number-->
                    <!--begin::Follower-->
                    <span class="fw-semibold fs-8 text-gray-400">COST LAST WEEK</span>
                    <!--end::Follower-->
                </div>
                <!--end::Section-->
            </div>
            <!--end::Body-->
        </div>
        <!--end::Card widget 2-->
    </div>
    <!--end::Cost Last Week Card-->

    <!--begin::SMS In Month Card-->
    <div class="col-sm-6 col-xl-2 mb-3">
        <!--begin::Card widget 2-->
        <div class="card h-100">
            <!--begin::Body-->
            <div class="card-body d-flex justify-content-center align-items-center flex-column py-3 px-3">
                <!--begin::Section-->
                <div class="d-flex flex-column text-center w-100">
                    <!--begin::Number-->
                    <span class="fw-semibold fs-2x text-gray-800 lh-1 ls-n2 mb-1">{{ number_format($smsInMonth) }}</span>
                    <!--end::Number-->
                    <!--begin::Follower-->
                    <span class="fw-semibold fs-8 text-gray-400">SMS IN {{ strtoupper($monthName) }}</span>
                    <!--end::Follower-->
                </div>
                <!--end::Section-->
            </div>
            <!--end::Body-->
        </div>
        <!--end::Card widget 2-->
    </div>
    <!--end::SMS In Month Card-->

    <!--begin::Cost In Month Card-->
    <div class="col-sm-6 col-xl-2 mb-3">
        <!--begin::Card widget 2-->
        <div class="card h-100">
            <!--begin::Body-->
            <div class="card-body d-flex justify-content-center align-items-center flex-column py-3 px-3">
                <!--begin::Section-->
                <div class="d-flex flex-column text-center w-100">
                    <!--begin::Number-->
                    <span class="fw-semibold fs-2x text-gray-800 lh-1 ls-n2 mb-1">৳{{ number_format($costInMonth, 2) }}</span>
                    <!--end::Number-->
                    <!--begin::Follower-->
                    <span class="fw-semibold fs-8 text-gray-400">COST IN {{ strtoupper($monthName) }}</span>
                    <!--end::Follower-->
                </div>
                <!--end::Section-->
            </div>
            <!--end::Body-->
        </div>
        <!--end::Card widget 2-->
    </div>
    <!--end::Cost In Month Card-->
