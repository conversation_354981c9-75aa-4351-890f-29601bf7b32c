<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>SMS API Documentation</title>
    @php
        $baseUrl = config('app.url', request()->getSchemeAndHttpHost() ?? 'https://your-domain.com');
    @endphp
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 20px;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .api-section {
            margin-bottom: 40px;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
        }
        .api-title {
            background-color: #f8f9fa;
            padding: 15px;
            margin: -20px -20px 20px -20px;
            border-radius: 8px 8px 0 0;
            font-size: 18px;
            font-weight: bold;
            color: #007bff;
        }
        .endpoint {
            background-color: #f1f3f4;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
        }
        .method-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 5px;
        }
        .method-get { background-color: #28a745; color: white; }
        .method-post { background-color: #007bff; color: white; }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .code-example {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        .error-table {
            font-size: 14px;
        }
        .success-row { background-color: #d4edda; }
        .warning-row { background-color: #fff3cd; }
        .danger-row { background-color: #f8d7da; }
        .section-title {
            color: #007bff;
            font-size: 16px;
            font-weight: bold;
            margin: 20px 0 10px 0;
        }
        .page-break {
            page-break-before: always;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>SMS API Documentation</h1>
        <p>Complete API Reference for SMS Services</p>
        <p>Generated on: {{ date('Y-m-d H:i:s') }}</p>
    </div>

    <!-- SMS Send API -->
    <div class="api-section">
        <div class="api-title">📤 SMS Send API</div>
        
        <div class="section-title">Endpoint</div>
        <div class="endpoint">{{ $baseUrl }}/api/sms-send</div>
        
        <div class="section-title">HTTP Methods</div>
        <span class="method-badge method-get">GET</span>
        <span class="method-badge method-post">POST</span>
        
        <div class="section-title">Authentication</div>
        <p>This endpoint supports two authentication methods:</p>
        <ul>
            <li><strong>API Key:</strong> Use your API key in the request</li>
            <li><strong>Username/Password:</strong> Use your login credentials in the request</li>
        </ul>
        
        <div class="section-title">Request Parameters</div>
        <table>
            <thead>
                <tr>
                    <th>Parameter</th>
                    <th>Type</th>
                    <th>Required</th>
                    <th>Description</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>api_key</td>
                    <td>string</td>
                    <td>Optional*</td>
                    <td>Your API key for authentication</td>
                </tr>
                <tr>
                    <td>username</td>
                    <td>string</td>
                    <td>Optional*</td>
                    <td>Your username (required if api_key not provided)</td>
                </tr>
                <tr>
                    <td>password</td>
                    <td>string</td>
                    <td>Optional*</td>
                    <td>Your password (required if api_key not provided)</td>
                </tr>
                <tr>
                    <td>sender_id</td>
                    <td>string</td>
                    <td>Yes</td>
                    <td>The sender ID/name for the SMS</td>
                </tr>
                <tr>
                    <td>phone_numbers</td>
                    <td>string</td>
                    <td>Yes</td>
                    <td>Comma-separated list of phone numbers</td>
                </tr>
                <tr>
                    <td>type</td>
                    <td>string</td>
                    <td>Yes</td>
                    <td>Message type: "text" or "unicode"</td>
                </tr>
                <tr>
                    <td>message</td>
                    <td>string</td>
                    <td>Yes</td>
                    <td>SMS content (use URL encoding for special characters)</td>
                </tr>
                <tr>
                    <td>scheduled_date_time</td>
                    <td>datetime</td>
                    <td>No</td>
                    <td>Schedule SMS for future delivery (YYYY-MM-DD HH:MM:SS)</td>
                </tr>
            </tbody>
        </table>
        <small>* Either api_key OR username+password is required</small>
        
        <div class="section-title">Response Format</div>
        <p><strong>Success Response:</strong></p>
        <div class="code-example">{
    "response_code": "0000",
    "message": "SMS sent successfully!"
}</div>
        
        <p><strong>Error Response:</strong></p>
        <div class="code-example">{
    "response_code": "1003",
    "message": "Invalid API key"
}</div>
        
        <div class="section-title">cURL Example</div>
        <div class="code-example">curl -X POST "{{ $baseUrl }}/api/sms-send" \
     -H "Content-Type: application/json" \
     -d '{
         "api_key": "YOUR_API_KEY",
         "sender_id": "YourSender",
         "phone_numbers": "1234567890,0987654321",
         "type": "text",
         "message": "Hello, this is a test message!"
     }'</div>
    </div>

    <div class="page-break"></div>

    <!-- Credit Balance API -->
    <div class="api-section">
        <div class="api-title">💰 Credit Balance API</div>
        
        <div class="section-title">Endpoint</div>
        <div class="endpoint">{{ $baseUrl }}/api/{apiKey}/getBalance</div>
        
        <div class="section-title">HTTP Methods</div>
        <span class="method-badge method-get">GET</span>
        <span class="method-badge method-post">POST</span>
        
        <div class="section-title">Authentication</div>
        <p>Same authentication methods as SMS Send API (API Key or Username/Password)</p>
        
        <div class="section-title">Request Parameters</div>
        <table>
            <thead>
                <tr>
                    <th>Parameter</th>
                    <th>Type</th>
                    <th>Required</th>
                    <th>Description</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>api_key</td>
                    <td>string</td>
                    <td>Optional*</td>
                    <td>Your API key for authentication</td>
                </tr>
                <tr>
                    <td>username</td>
                    <td>string</td>
                    <td>Optional*</td>
                    <td>Your username (required if api_key not provided)</td>
                </tr>
                <tr>
                    <td>password</td>
                    <td>string</td>
                    <td>Optional*</td>
                    <td>Your password (required if api_key not provided)</td>
                </tr>
            </tbody>
        </table>
        <small>* Either api_key OR username+password is required</small>
        
        <div class="section-title">Response Format</div>
        <div class="code-example">{
    "response_code": "0000",
    "message": "Balance retrieved successfully",
    "data": {
        "current_balance": 20000.50,
        "balance_expired": "2035-06-30 03:35:58",
        "company_name": "Your Company Name"
    }
}</div>
        
        <div class="section-title">cURL Example</div>
        <div class="code-example">curl -X POST "{{ $baseUrl }}/api/YOUR_API_KEY/getBalance" \
     -H "Content-Type: application/json" \
     -d '{"api_key": "YOUR_API_KEY"}'</div>
    </div>

    <div class="page-break"></div>

    <!-- Delivery Report API -->
    <div class="api-section">
        <div class="api-title">📊 Delivery Report API</div>
        
        <div class="section-title">Endpoint</div>
        <div class="endpoint">{{ $baseUrl }}/api/{apiKey}/getDLR/getAll</div>
        
        <div class="section-title">HTTP Methods</div>
        <span class="method-badge method-get">GET</span>
        <span class="method-badge method-post">POST</span>
        
        <div class="section-title">Request Parameters</div>
        <table>
            <thead>
                <tr>
                    <th>Parameter</th>
                    <th>Type</th>
                    <th>Required</th>
                    <th>Description</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>api_key</td>
                    <td>string</td>
                    <td>Optional*</td>
                    <td>Your API key for authentication</td>
                </tr>
                <tr>
                    <td>username</td>
                    <td>string</td>
                    <td>Optional*</td>
                    <td>Your username (required if api_key not provided)</td>
                </tr>
                <tr>
                    <td>password</td>
                    <td>string</td>
                    <td>Optional*</td>
                    <td>Your password (required if api_key not provided)</td>
                </tr>
                <tr>
                    <td>limit</td>
                    <td>integer</td>
                    <td>No</td>
                    <td>Number of records to return (default: 100, max: 1000)</td>
                </tr>
                <tr>
                    <td>offset</td>
                    <td>integer</td>
                    <td>No</td>
                    <td>Number of records to skip (default: 0)</td>
                </tr>
                <tr>
                    <td>date_from</td>
                    <td>date</td>
                    <td>No</td>
                    <td>Start date filter (YYYY-MM-DD format)</td>
                </tr>
                <tr>
                    <td>date_to</td>
                    <td>date</td>
                    <td>No</td>
                    <td>End date filter (YYYY-MM-DD format)</td>
                </tr>
            </tbody>
        </table>
        <small>* Either api_key OR username+password is required</small>

        <div class="section-title">Response Format</div>
        <div class="code-example">{
    "response_code": "0000",
    "message": "Delivery reports retrieved successfully",
    "data": {
        "total": 150,
        "limit": 10,
        "offset": 0,
        "messages": [
            {
                "message_id": "ABC1234567",
                "phone_number": "1234567890",
                "sender_name": "YourSender",
                "sms_content": "Your message content",
                "sms_type": "text",
                "sms_count": 1,
                "sms_cost": 0.05,
                "status": "delivered",
                "sent_at": "2024-01-15 10:30:00",
                "scheduled_at": null,
                "batch_number": "BATCH123",
                "api_response": {"status": "success"},
                "is_masking": true
            }
        ]
    }
}</div>

        <div class="section-title">cURL Example</div>
        <div class="code-example">curl -X POST "{{ $baseUrl }}/api/YOUR_API_KEY/getDLR/getAll" \
     -H "Content-Type: application/json" \
     -d '{"api_key": "YOUR_API_KEY", "limit": 10}'</div>
    </div>

    <div class="page-break"></div>

    <!-- API Key Retrieval -->
    <div class="api-section">
        <div class="api-title">🔑 API Key Retrieval</div>
        
        <div class="section-title">Endpoint</div>
        <div class="endpoint">{{ $baseUrl }}/api/getkey/{username}/{password}</div>
        
        <div class="section-title">HTTP Methods</div>
        <span class="method-badge method-get">GET</span>
        <span class="method-badge method-post">POST</span>
        
        <div class="section-title">URL Parameters</div>
        <table>
            <thead>
                <tr>
                    <th>Parameter</th>
                    <th>Type</th>
                    <th>Required</th>
                    <th>Description</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>username</td>
                    <td>string</td>
                    <td>Yes</td>
                    <td>Your username in the URL path</td>
                </tr>
                <tr>
                    <td>password</td>
                    <td>string</td>
                    <td>Yes</td>
                    <td>Your password in the URL path</td>
                </tr>
            </tbody>
        </table>
        
        <div class="section-title">Response Format</div>
        <div class="code-example">{
    "response_code": "0000",
    "message": "API key retrieved successfully",
    "data": {
        "api_key": "your-generated-api-key",
        "company_name": "Your Company Name",
        "user_name": "Your User Name"
    }
}</div>

        <div class="section-title">cURL Example</div>
        <div class="code-example">curl -X POST "{{ $baseUrl }}/api/getkey/your_username/your_password" \
     -H "Content-Type: application/json"</div>
    </div>

    <div class="page-break"></div>

    <!-- Error Codes -->
    <div class="api-section">
        <div class="api-title">📋 Complete Error Code Reference</div>
        
        <table class="error-table">
            <thead>
                <tr>
                    <th>Error Code</th>
                    <th>Meaning</th>
                    <th>Applicable APIs</th>
                </tr>
            </thead>
            <tbody>
                <tr class="success-row">
                    <td><strong>0000</strong></td>
                    <td>Success</td>
                    <td>All APIs</td>
                </tr>
                <tr>
                    <td>1000</td>
                    <td>No SMS data found</td>
                    <td>SMS Send</td>
                </tr>
                <tr>
                    <td>1001</td>
                    <td>Something error is happened</td>
                    <td>All APIs</td>
                </tr>
                <tr>
                    <td>1002</td>
                    <td>Sender Id/Masking Not Found</td>
                    <td>SMS Send</td>
                </tr>
                <tr class="warning-row">
                    <td><strong>1003</strong></td>
                    <td>Invalid API key</td>
                    <td>All APIs</td>
                </tr>
                <tr>
                    <td>1004</td>
                    <td>SPAM Detected</td>
                    <td>SMS Send</td>
                </tr>
                <tr>
                    <td>1007</td>
                    <td>Balance Insufficient</td>
                    <td>SMS Send</td>
                </tr>
                <tr>
                    <td>1008</td>
                    <td>Message is empty</td>
                    <td>SMS Send</td>
                </tr>
                <tr>
                    <td>1009</td>
                    <td>Message Type Not Set (text/unicode)</td>
                    <td>SMS Send</td>
                </tr>
                <tr class="warning-row">
                    <td><strong>1010</strong></td>
                    <td>Invalid User & Password</td>
                    <td>All APIs</td>
                </tr>
                <tr>
                    <td>1012</td>
                    <td>Invalid Number</td>
                    <td>SMS Send</td>
                </tr>
                <tr>
                    <td>1013</td>
                    <td>API limit error</td>
                    <td>All APIs</td>
                </tr>
                <tr class="danger-row">
                    <td><strong>422</strong></td>
                    <td>Validation errors</td>
                    <td>All APIs</td>
                </tr>
            </tbody>
        </table>
    </div>
</body>
</html>
