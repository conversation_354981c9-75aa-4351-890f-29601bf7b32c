@extends('layouts.app')

@section('title', 'Developers API')

@section('content')
    @php
        $baseUrl = config('app.url', request()->getSchemeAndHttpHost() ?? 'https://your-domain.com');
    @endphp
    <!--begin::Container-->
    <div class="container-xxl" id="kt_content_container">
        <div class="card mt-3">
            <div class="card-body">
                <h4 class="card-title">Your API Key :</h4>
                <div class="d-flex align-items-center">
                    @if(optional($company)->api_key)
                        <!-- Show API Key and Copy Button if it exists -->
                        <p id="api-key" class="card-text me-2">{{ optional($company)->api_key ?? 'No API Key available' }}</p>
                        <button class="btn btn-secondary btn-sm" id="copy-button" style="display: flex; align-items: center;">
                            <span class="me-1"> Copy</span>
                        </button>
                    @else
                        <!-- Show message to generate API Key if it's empty -->
                        <p class="text-warning">No API Key found. Please generate one.</p>
                    @endif
                </div>
                <div class="d-flex flex-wrap gap-3 mt-3">
                    <form id="regenerate-form" action="{{ route('generate-api-key') }}" method="POST" class="d-inline">
                        @csrf
                        <button type="button" id="regenerate-button" class="btn btn-primary">Regenerate API Key</button>
                    </form>
                    <a href="{{ route('developers.pdf') }}" target="_blank" class="btn btn-outline-success">
                        <i class="fas fa-download me-2"></i>Download API Documentation (PDF)
                    </a>
                </div>
            </div>
        </div>

        <!-- Quick Navigation -->
        <div class="card mb-4 bg-light mt-4">
            <div class="card-body">
                <h5 class="card-title">📖 Quick Navigation</h5>
                <div class="row">
                    <div class="col-md-4">
                        <ul class="list-unstyled">
                            <li><a href="#sms-send-api" class="text-decoration-none">📤 SMS Send API</a></li>
                            <li><a href="#balance-api" class="text-decoration-none">💰 Credit Balance API</a></li>
                            <li><a href="#dlr-api" class="text-decoration-none">📊 Delivery Report API</a></li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <ul class="list-unstyled">
                            <li><a href="#apikey-api" class="text-decoration-none">🔑 API Key Retrieval</a></li>
                            <li><a href="#error-responses" class="text-decoration-none">⚠️ Error Responses</a></li>
                            <li><a href="#error-codes" class="text-decoration-none">📋 Error Code Reference</a></li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <div class="alert alert-info mb-0">
                            <small><strong>💡 Tip:</strong> Click on any code example to copy it to your clipboard!</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- SMS Send API -->
        <div class="card mb-4" id="sms-send-api">
            <div class="card-header bg-light">
                <h4 class="card-title mb-0">📤 SMS Send API</h4>
            </div>
            <div class="card-body">
                <h5 class="text-primary">Endpoint</h5>
                <pre class="p-3 bg-light border rounded"><code>{{ $baseUrl }}/api/sms-send</code></pre>
                
                <h5 class="text-primary mt-4">HTTP Methods</h5>
                <span class="badge bg-success me-2">GET</span>
                <span class="badge bg-primary">POST</span>
                
                <h5 class="text-primary mt-4">Authentication</h5>
                <p>This endpoint supports two authentication methods:</p>
                <ul>
                    <li><strong>API Key:</strong> Use your API key in the request</li>
                    <li><strong>Username/Password:</strong> Use your login credentials in the request</li>
                </ul>
                
                <h5 class="text-primary mt-4">Request Parameters</h5>
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead class="thead-light">
                            <tr>
                                <th>Parameter</th>
                                <th>Type</th>
                                <th>Required</th>
                                <th>Description</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>api_key</td>
                                <td>string</td>
                                <td>Optional*</td>
                                <td>Your API key for authentication</td>
                            </tr>
                            <tr>
                                <td>username</td>
                                <td>string</td>
                                <td>Optional*</td>
                                <td>Your username (required if api_key not provided)</td>
                            </tr>
                            <tr>
                                <td>password</td>
                                <td>string</td>
                                <td>Optional*</td>
                                <td>Your password (required if api_key not provided)</td>
                            </tr>
                            <tr>
                                <td>sender_id</td>
                                <td>string</td>
                                <td>Yes</td>
                                <td>The sender ID/name for the SMS</td>
                            </tr>
                            <tr>
                                <td>phone_numbers</td>
                                <td>string</td>
                                <td>Yes</td>
                                <td>Comma-separated list of phone numbers</td>
                            </tr>
                            <tr>
                                <td>type</td>
                                <td>string</td>
                                <td>Yes</td>
                                <td>Message type: "text" or "unicode"</td>
                            </tr>
                            <tr>
                                <td>message</td>
                                <td>string</td>
                                <td>Yes</td>
                                <td>SMS content (use URL encoding for special characters)</td>
                            </tr>
                            <tr>
                                <td>scheduled_date_time</td>
                                <td>datetime</td>
                                <td>No</td>
                                <td>Schedule SMS for future delivery (YYYY-MM-DD HH:MM:SS)</td>
                            </tr>
                        </tbody>
                    </table>
                    <small class="text-muted">* Either api_key OR username+password is required</small>
                </div>
                
                <h5 class="text-primary mt-4">Response Format</h5>
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-success">Success Response</h6>
                        <pre class="p-3 bg-light border rounded"><code>{
    "response_code": "0000",
    "message": "SMS sent successfully!"
}</code></pre>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-success">Scheduled SMS Response</h6>
                        <pre class="p-3 bg-light border rounded"><code>{
    "response_code": "0000",
    "message": "Successfully scheduled message"
}</code></pre>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-md-6">
                        <h6 class="text-danger">Error Response</h6>
                        <pre class="p-3 bg-light border rounded"><code>{
    "response_code": "1003",
    "message": "Invalid API key"
}</code></pre>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-warning">Validation Error</h6>
                        <pre class="p-3 bg-light border rounded"><code>{
    "success": false,
    "message": "Validation errors",
    "errors": {
        "sender_id": ["The sender ID field is required."]
    }
}</code></pre>
                    </div>
                </div>
                
                <h5 class="text-primary mt-4">Sample cURL Commands</h5>
                <div class="accordion" id="smsExamples">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#smsGetApiKey">
                                GET Request with API Key
                            </button>
                        </h2>
                        <div id="smsGetApiKey" class="accordion-collapse collapse" data-bs-parent="#smsExamples">
                            <div class="accordion-body">
                                <pre class="p-3 bg-dark text-light rounded"><code>curl -X GET "{{ config('app.url') }}/api/sms-send?api_key=YOUR_API_KEY&sender_id=YourSender&phone_numbers=1234567890,0987654321&type=text&message=Hello%20World&scheduled_date_time=2024-01-14%2002:01:00"</code></pre>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#smsGetCredentials">
                                GET Request with Username/Password
                            </button>
                        </h2>
                        <div id="smsGetCredentials" class="accordion-collapse collapse" data-bs-parent="#smsExamples">
                            <div class="accordion-body">
                                <pre class="p-3 bg-dark text-light rounded"><code>curl -X GET "{{ config('app.url') }}/api/sms-send?username=your_username&password=your_password&sender_id=YourSender&phone_numbers=1234567890,0987654321&type=text&message=Hello%20World&scheduled_date_time=2024-01-14%2002:01:00"</code></pre>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#smsPostApiKey">
                                POST Request with API Key
                            </button>
                        </h2>
                        <div id="smsPostApiKey" class="accordion-collapse collapse" data-bs-parent="#smsExamples">
                            <div class="accordion-body">
                                <pre class="p-3 bg-dark text-light rounded"><code>curl -X POST "{{ config('app.url') }}/api/sms-send" \
     -H "Content-Type: application/json" \
     -d '{
         "api_key": "YOUR_API_KEY",
         "sender_id": "YourSender",
         "phone_numbers": "1234567890,0987654321",
         "type": "text",
         "message": "Hello, this is a test message!",
         "scheduled_date_time": "2024-01-14 02:01:00"
     }'</code></pre>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#smsPostCredentials">
                                POST Request with Username/Password
                            </button>
                        </h2>
                        <div id="smsPostCredentials" class="accordion-collapse collapse" data-bs-parent="#smsExamples">
                            <div class="accordion-body">
                                <pre class="p-3 bg-dark text-light rounded"><code>curl -X POST "{{ config('app.url') }}/api/sms-send" \
     -H "Content-Type: application/json" \
     -d '{
         "username": "your_username",
         "password": "your_password",
         "sender_id": "YourSender",
         "phone_numbers": "1234567890,0987654321",
         "type": "text",
         "message": "Hello, this is a test message!"
     }'</code></pre>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#smsUnicode">
                                Unicode Message Example
                            </button>
                        </h2>
                        <div id="smsUnicode" class="accordion-collapse collapse" data-bs-parent="#smsExamples">
                            <div class="accordion-body">
                                <pre class="p-3 bg-dark text-light rounded"><code>curl -X POST "{{ config('app.url') }}/api/sms-send" \
     -H "Content-Type: application/json" \
     -d '{
         "api_key": "YOUR_API_KEY",
         "sender_id": "YourSender",
         "phone_numbers": "1234567890",
         "type": "unicode",
         "message": "Hello 🌍 Unicode message with emojis! 📱✨"
     }'</code></pre>
                            </div>
                        </div>
                    </div>
                </div>



                <div class="alert alert-info mt-4">
                    <strong>💡 Pro Tips:</strong>
                    <ul class="mb-0 mt-2">
                        <li>Use URL encoding for special characters in GET requests</li>
                        <li>For multiple phone numbers, separate them with commas</li>
                        <li>Unicode type supports emojis and special characters</li>
                        <li>Scheduled messages must be set for future dates</li>
                        <li>Long messages are automatically split into multiple SMS segments</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Credit Balance API -->
        <div class="card mb-4" id="balance-api">
            <div class="card-header bg-light">
                <h4 class="card-title mb-0">💰 Credit Balance API</h4>
            </div>
            <div class="card-body">
                <h5 class="text-primary">Endpoint</h5>
                <pre class="p-3 bg-light border rounded"><code>{{ config('app.url') }}/api/{apiKey}/getBalance</code></pre>

                <h5 class="text-primary mt-4">HTTP Methods</h5>
                <span class="badge bg-success me-2">GET</span>
                <span class="badge bg-primary">POST</span>

                <h5 class="text-primary mt-4">Authentication</h5>
                <p>This endpoint supports two authentication methods:</p>
                <ul>
                    <li><strong>API Key:</strong> Use your API key in the request body</li>
                    <li><strong>Username/Password:</strong> Use your login credentials in the request body</li>
                </ul>

                <h5 class="text-primary mt-4">Request Parameters</h5>
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead class="thead-light">
                            <tr>
                                <th>Parameter</th>
                                <th>Type</th>
                                <th>Required</th>
                                <th>Description</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>api_key</td>
                                <td>string</td>
                                <td>Optional*</td>
                                <td>Your API key for authentication</td>
                            </tr>
                            <tr>
                                <td>username</td>
                                <td>string</td>
                                <td>Optional*</td>
                                <td>Your username (required if api_key not provided)</td>
                            </tr>
                            <tr>
                                <td>password</td>
                                <td>string</td>
                                <td>Optional*</td>
                                <td>Your password (required if api_key not provided)</td>
                            </tr>
                        </tbody>
                    </table>
                    <small class="text-muted">* Either api_key OR username+password is required</small>
                </div>

                <h5 class="text-primary mt-4">Response Format</h5>
                <pre class="p-3 bg-light border rounded"><code>{
    "response_code": "0000",
    "message": "Balance retrieved successfully",
    "data": {
        "current_balance": 20000.50,
        "balance_expired": "2035-06-30 03:35:58",
        "company_name": "Your Company Name"
    }
}</code></pre>

                <h5 class="text-primary mt-4">Sample cURL Commands</h5>
                <div class="accordion" id="balanceExamples">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#balanceApiKey">
                                Using API Key
                            </button>
                        </h2>
                        <div id="balanceApiKey" class="accordion-collapse collapse" data-bs-parent="#balanceExamples">
                            <div class="accordion-body">
                                <pre class="p-3 bg-dark text-light rounded"><code>curl -X POST "{{ config('app.url') }}/api/YOUR_API_KEY/getBalance" \
     -H "Content-Type: application/json" \
     -d '{"api_key": "YOUR_API_KEY"}'</code></pre>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#balanceCredentials">
                                Using Username/Password
                            </button>
                        </h2>
                        <div id="balanceCredentials" class="accordion-collapse collapse" data-bs-parent="#balanceExamples">
                            <div class="accordion-body">
                                <pre class="p-3 bg-dark text-light rounded"><code>curl -X POST "{{ config('app.url') }}/api/YOUR_API_KEY/getBalance" \
     -H "Content-Type: application/json" \
     -d '{"username": "your_username", "password": "your_password"}'</code></pre>
                            </div>
                        </div>
                    </div>
                </div>


            </div>
        </div>

        <!-- Delivery Report API -->
        <div class="card mb-4" id="dlr-api">
            <div class="card-header bg-light">
                <h4 class="card-title mb-0">📊 Delivery Report API</h4>
            </div>
            <div class="card-body">
                <h5 class="text-primary">Endpoint</h5>
                <pre class="p-3 bg-light border rounded"><code>{{ config('app.url') }}/api/{apiKey}/getDLR/getAll</code></pre>

                <h5 class="text-primary mt-4">HTTP Methods</h5>
                <span class="badge bg-success me-2">GET</span>
                <span class="badge bg-primary">POST</span>

                <h5 class="text-primary mt-4">Authentication</h5>
                <p>Same authentication methods as other APIs (API Key or Username/Password)</p>

                <h5 class="text-primary mt-4">Request Parameters</h5>
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead class="thead-light">
                            <tr>
                                <th>Parameter</th>
                                <th>Type</th>
                                <th>Required</th>
                                <th>Description</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>api_key</td>
                                <td>string</td>
                                <td>Optional*</td>
                                <td>Your API key for authentication</td>
                            </tr>
                            <tr>
                                <td>username</td>
                                <td>string</td>
                                <td>Optional*</td>
                                <td>Your username (required if api_key not provided)</td>
                            </tr>
                            <tr>
                                <td>password</td>
                                <td>string</td>
                                <td>Optional*</td>
                                <td>Your password (required if api_key not provided)</td>
                            </tr>
                            <tr>
                                <td>limit</td>
                                <td>integer</td>
                                <td>No</td>
                                <td>Number of records to return (default: 100, max: 1000)</td>
                            </tr>
                            <tr>
                                <td>offset</td>
                                <td>integer</td>
                                <td>No</td>
                                <td>Number of records to skip (default: 0)</td>
                            </tr>
                            <tr>
                                <td>date_from</td>
                                <td>date</td>
                                <td>No</td>
                                <td>Start date filter (YYYY-MM-DD format)</td>
                            </tr>
                            <tr>
                                <td>date_to</td>
                                <td>date</td>
                                <td>No</td>
                                <td>End date filter (YYYY-MM-DD format)</td>
                            </tr>
                        </tbody>
                    </table>
                    <small class="text-muted">* Either api_key OR username+password is required</small>
                </div>

                <h5 class="text-primary mt-4">Response Format</h5>
                <pre class="p-3 bg-light border rounded"><code>{
    "response_code": "0000",
    "message": "Delivery reports retrieved successfully",
    "data": {
        "total": 150,
        "limit": 10,
        "offset": 0,
        "messages": [
            {
                "message_id": "ABC1234567",
                "phone_number": "1234567890",
                "sender_name": "YourSender",
                "sms_content": "Your message content",
                "sms_type": "text",
                "sms_count": 1,
                "sms_cost": 0.05,
                "status": "delivered",
                "sent_at": "2024-01-15 10:30:00",
                "scheduled_at": null,
                "batch_number": "BATCH123",
                "api_response": {"status": "success"},
                "is_masking": true
            }
        ]
    }
}</code></pre>

                <h5 class="text-primary mt-4">Sample cURL Commands</h5>
                <div class="accordion" id="dlrExamples">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#dlrBasic">
                                Basic Request
                            </button>
                        </h2>
                        <div id="dlrBasic" class="accordion-collapse collapse" data-bs-parent="#dlrExamples">
                            <div class="accordion-body">
                                <pre class="p-3 bg-dark text-light rounded"><code>curl -X POST "{{ config('app.url') }}/api/YOUR_API_KEY/getDLR/getAll" \
     -H "Content-Type: application/json" \
     -d '{"api_key": "YOUR_API_KEY", "limit": 10}'</code></pre>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#dlrFiltered">
                                With Date Filters
                            </button>
                        </h2>
                        <div id="dlrFiltered" class="accordion-collapse collapse" data-bs-parent="#dlrExamples">
                            <div class="accordion-body">
                                <pre class="p-3 bg-dark text-light rounded"><code>curl -X POST "{{ config('app.url') }}/api/YOUR_API_KEY/getDLR/getAll" \
     -H "Content-Type: application/json" \
     -d '{
         "api_key": "YOUR_API_KEY",
         "limit": 50,
         "offset": 0,
         "date_from": "2024-01-01",
         "date_to": "2024-01-31"
     }'</code></pre>
                            </div>
                        </div>
                    </div>
                </div>


            </div>
        </div>

        <!-- API Key Retrieval -->
        <div class="card mb-4" id="apikey-api">
            <div class="card-header bg-light">
                <h4 class="card-title mb-0">🔑 API Key Retrieval</h4>
            </div>
            <div class="card-body">
                <h5 class="text-primary">Endpoint</h5>
                <pre class="p-3 bg-light border rounded"><code>{{ config('app.url') }}/api/getkey/{username}/{password}</code></pre>

                <h5 class="text-primary mt-4">HTTP Methods</h5>
                <span class="badge bg-success me-2">GET</span>
                <span class="badge bg-primary">POST</span>

                <h5 class="text-primary mt-4">Authentication</h5>
                <p>This endpoint uses URL parameters for authentication. No additional authentication required in the request body.</p>

                <h5 class="text-primary mt-4">URL Parameters</h5>
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead class="thead-light">
                            <tr>
                                <th>Parameter</th>
                                <th>Type</th>
                                <th>Required</th>
                                <th>Description</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>username</td>
                                <td>string</td>
                                <td>Yes</td>
                                <td>Your username in the URL path</td>
                            </tr>
                            <tr>
                                <td>password</td>
                                <td>string</td>
                                <td>Yes</td>
                                <td>Your password in the URL path</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h5 class="text-primary mt-4">Response Format</h5>
                <pre class="p-3 bg-light border rounded"><code>{
    "response_code": "0000",
    "message": "API key retrieved successfully",
    "data": {
        "api_key": "your-generated-api-key",
        "company_name": "Your Company Name",
        "user_name": "Your User Name"
    }
}</code></pre>

                <h5 class="text-primary mt-4">Sample cURL Command</h5>
                <pre class="p-3 bg-dark text-light rounded"><code>curl -X POST "{{ config('app.url') }}/api/getkey/your_username/your_password" \
     -H "Content-Type: application/json"</code></pre>



                <div class="alert alert-warning mt-3">
                    <strong>⚠️ Security Note:</strong> Be careful when using this endpoint as credentials are passed in the URL.
                    Consider using HTTPS and avoid logging URLs that contain sensitive information.
                </div>
            </div>
        </div>

        <!-- Error Responses -->
        <div class="card mb-4" id="error-responses">
            <div class="card-header bg-light">
                <h4 class="card-title mb-0">⚠️ Common Error Responses</h4>
            </div>
            <div class="card-body">
                <h5 class="text-primary">Error Response Format</h5>
                <pre class="p-3 bg-light border rounded"><code>{
    "response_code": "1003",
    "message": "Invalid API key"
}</code></pre>

                <h5 class="text-primary mt-4">Validation Error Format</h5>
                <pre class="p-3 bg-light border rounded"><code>{
    "success": false,
    "message": "Validation errors",
    "errors": {
        "username": ["The username field is required."],
        "password": ["The password field is required."]
    }
}</code></pre>
            </div>
        </div>

        <!-- Complete Error Code Reference -->
        <div class="card mb-4" id="error-codes">
            <div class="card-header bg-light">
                <h4 class="card-title mb-0">📋 Complete Error Code Reference</h4>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead class="thead-light">
                                <tr class="text-center">
                                    <th>Error Code</th>
                                    <th>Meaning</th>
                                    <th>Applicable APIs</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr class="table-success">
                                    <td><strong>0000</strong></td>
                                    <td>Success</td>
                                    <td>All APIs</td>
                                </tr>
                                <tr>
                                    <td>1000</td>
                                    <td>No SMS data found</td>
                                    <td>SMS Send</td>
                                </tr>
                                <tr>
                                    <td>1001</td>
                                    <td>Something error is happened</td>
                                    <td>All APIs</td>
                                </tr>
                                <tr>
                                    <td>1002</td>
                                    <td>Sender Id/Masking Not Found</td>
                                    <td>SMS Send</td>
                                </tr>
                                <tr class="table-warning">
                                    <td><strong>1003</strong></td>
                                    <td>Invalid API key</td>
                                    <td>All APIs</td>
                                </tr>
                                <tr>
                                    <td>1004</td>
                                    <td>SPAM Detected</td>
                                    <td>SMS Send</td>
                                </tr>
                                <tr>
                                    <td>1005</td>
                                    <td>Internal Error</td>
                                    <td>All APIs</td>
                                </tr>
                                <tr>
                                    <td>1006</td>
                                    <td>Internal Error</td>
                                    <td>All APIs</td>
                                </tr>
                                <tr>
                                    <td>1007</td>
                                    <td>Balance Insufficient</td>
                                    <td>SMS Send</td>
                                </tr>
                                <tr>
                                    <td>1008</td>
                                    <td>Message is empty</td>
                                    <td>SMS Send</td>
                                </tr>
                                <tr>
                                    <td>1009</td>
                                    <td>Message Type Not Set (text/unicode)</td>
                                    <td>SMS Send</td>
                                </tr>
                                <tr class="table-warning">
                                    <td><strong>1010</strong></td>
                                    <td>Invalid User & Password</td>
                                    <td>All APIs</td>
                                </tr>
                                <tr>
                                    <td>1011</td>
                                    <td>Invalid User Id</td>
                                    <td>All APIs</td>
                                </tr>
                                <tr>
                                    <td>1012</td>
                                    <td>Invalid Number</td>
                                    <td>SMS Send</td>
                                </tr>
                                <tr>
                                    <td>1013</td>
                                    <td>API limit error</td>
                                    <td>All APIs</td>
                                </tr>
                                <tr>
                                    <td>1014</td>
                                    <td>No matching template</td>
                                    <td>SMS Send</td>
                                </tr>
                                <tr>
                                    <td>1015</td>
                                    <td>SMS Content Validation Fails</td>
                                    <td>SMS Send</td>
                                </tr>
                                <tr>
                                    <td>1016</td>
                                    <td>Schedule date time format is invalid</td>
                                    <td>SMS Send</td>
                                </tr>
                                <tr>
                                    <td>1019</td>
                                    <td>Sms Purpose Missing</td>
                                    <td>SMS Send</td>
                                </tr>
                                <tr class="table-danger">
                                    <td><strong>422</strong></td>
                                    <td>Validation errors</td>
                                    <td>All APIs</td>
                                </tr>
                                <tr>
                                    <td>69</td>
                                    <td>Route mobile error</td>
                                    <td>SMS Send</td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="mt-3">
                            <small class="text-muted">
                                <strong>Note:</strong> Error codes highlighted in <span class="badge bg-success">green</span> indicate success,
                                <span class="badge bg-warning">yellow</span> indicate authentication issues, and
                                <span class="badge bg-danger">red</span> indicate validation errors.
                            </small>
                </div>
            </div>
        </div>
    </div>

    <script>
        function copyToClipboard() {
            const apiKeyElement = document.getElementById('api-key');
            const apiKey = apiKeyElement.textContent;

            if (navigator.clipboard && navigator.clipboard.writeText) {
                navigator.clipboard.writeText(apiKey).then(function() {
                    Swal.fire({
                        title: 'Copied!',
                        text: 'API key has been copied to clipboard.',
                        icon: 'success',
                        timer: 2000,
                        showConfirmButton: false
                    });
                }).catch(function() {
                    fallbackCopy(apiKey);
                });
            } else {
                fallbackCopy(apiKey);
            }
        }

        // Function to copy code examples
        function copyCodeExample(element) {
            const codeText = element.closest('.accordion-body').querySelector('code').innerText;

            if (navigator.clipboard && navigator.clipboard.writeText) {
                navigator.clipboard.writeText(codeText).then(function() {
                    Swal.fire({
                        title: 'Copied!',
                        text: 'Code example has been copied to clipboard.',
                        icon: 'success',
                        timer: 2000,
                        showConfirmButton: false
                    });
                }).catch(function() {
                    fallbackCopy(codeText);
                });
            } else {
                fallbackCopy(codeText);
            }
        }

        function fallbackCopy(text) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            try {
                document.execCommand('copy');
                Swal.fire({
                    title: 'Copied!',
                    text: 'Content has been copied to clipboard.',
                    icon: 'success',
                    timer: 2000,
                    showConfirmButton: false
                });
            } catch (err) {
                Swal.fire({
                    title: 'Error',
                    text: 'Failed to copy. Please select and copy manually.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
            document.body.removeChild(textArea);
        }

        $(document).ready(function() {
            // Copy button event
            $('#copy-button').on('click', copyToClipboard);

            // Add copy buttons to code examples
            $('.accordion-body pre').each(function() {
                const $pre = $(this);
                if ($pre.find('code').length > 0) {
                    const copyBtn = $('<button class="btn btn-sm btn-outline-secondary position-absolute" style="top: 10px; right: 10px; z-index: 10;" title="Copy to clipboard"><i class="fas fa-copy"></i></button>');
                    $pre.css('position', 'relative');
                    $pre.append(copyBtn);

                    copyBtn.on('click', function(e) {
                        e.preventDefault();
                        copyCodeExample(this);
                    });
                }
            });

            // Regenerate API key confirmation
            $('#regenerate-button').on('click', function(e) {
                e.preventDefault();

                Swal.fire({
                    title: 'Are you sure?',
                    text: "Your existing API key will be deleted, and you cannot retrieve it!",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Yes, regenerate it!',
                    cancelButtonText: 'Cancel'
                }).then((result) => {
                    if (result.isConfirmed) {
                        $('#regenerate-form').submit();
                    }
                });
            });

            // Smooth scrolling for anchor links
            $('a[href^="#"]').on('click', function(event) {
                var target = $(this.getAttribute('href'));
                if( target.length ) {
                    event.preventDefault();
                    $('html, body').stop().animate({
                        scrollTop: target.offset().top - 100
                    }, 1000);
                }
            });

            // Auto-replace YOUR_API_KEY with actual API key in examples
            @if(optional($company)->api_key)
            const actualApiKey = '{{ optional($company)->api_key }}';
            $('.accordion-body code').each(function() {
                const $code = $(this);
                let codeText = $code.text();
                codeText = codeText.replace(/YOUR_API_KEY/g, actualApiKey);
                codeText = codeText.replace(/your_username/g, 'your_actual_username');
                codeText = codeText.replace(/your_password/g, 'your_actual_password');
                $code.text(codeText);
            });
            @endif
        });
    </script>
@endsection
