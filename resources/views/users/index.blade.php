@extends('layouts.app')

@section('title', 'Users')

@section('content')
    <!--begin::Container-->
    <div class="container-xxl" id="kt_content_container">
        <!--begin::Card-->
        <div class="card">
            <!--begin::Card header-->
            <div class="card-header border-0 pt-6 flex-wrap">
                <div class="card-title">
                    <div class="row form-group my-1">
                        <div class="col-lg-3 col-md-4 col-sm-6 mb-2">
                            <input id="searchInput" type="text" class="form-control form-control-solid"
                                   placeholder="Search users..."/>
                        </div>
                        <div class="col-lg-3 col-md-4 col-sm-6 mb-2">
                            <select class="form-select" id="filterByUserType">
                                <option value="">Select Users Type</option>
                                @foreach($roles as $role => $value)
                                    <option value="{{$role}}">{{$value}}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-lg-3 col-md-4 col-sm-12 mb-2">
                            <select class="form-select" id="filterByUser">
                                <option value="">View Downline</option>
                                @foreach($users_by_role as $role)
                                    <optgroup label="{{$role->name}}">
                                        @foreach($role->users as $user)
                                            <option value="{{$user->id}}">{{$user->name}}</option>
                                        @endforeach
                                    </optgroup>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>
                <!--begin::Card toolbar-->
                <div class="card-toolbar">
                    <!--begin::Toolbar-->
                    <div class="d-flex justify-content-end" data-kt-user-table-toolbar="base">
                        <!--begin::Add user-->
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal"
                                data-bs-target="#kt_modal_add_user">
                            <!--begin::Svg Icon | path: icons/duotune/arrows/arr075.svg-->
                            <span class="svg-icon svg-icon-2">
												<svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                     xmlns="http://www.w3.org/2000/svg">
													<rect opacity="0.5" x="11.364" y="20.364" width="16" height="2"
                                                          rx="1" transform="rotate(-90 11.364 20.364)"
                                                          fill="currentColor"/>
													<rect x="4.36396" y="11.364" width="16" height="2" rx="1"
                                                          fill="currentColor"/>
												</svg>
											</span>
                            <!--end::Svg Icon-->Add User
                        </button>
                        <!--end::Add user-->
                    </div>
                </div>
                <!--end::Modal - New Card-->
                @include('users.add-user')
                @include('users.add-balance')
            </div>
            <!--end::Card header-->
            <!--begin::Card body-->
            <div class="card-body py-4">
                <!--begin::Table container-->
                <div class="table-responsive">
                    <!--begin::Table-->
                    <table class="table align-middle table-row-dashed fs-6 gy-5" id="kt_table_users">
                    </table>
                    <!--end::Table-->
                </div>
                <!--end::Table container-->
            </div>
            <!--end::Card body-->
        </div>
        <!--end::Card-->
    </div>
    <!--end::Container-->
@stop


@section('js')
    <script>
        // Pass user role to JavaScript for column visibility control
        window.isSuperAdmin = @json(auth()->user()->hasRole('super-admin'));
        window.isMasterReseller = @json(auth()->user()->hasRole('master-reseller'));
    </script>
    <script src="{{ asset('js/scripts/user.js') }}"></script>
    <script src="{{ asset('js/script.balance.js') }}"></script>
@stop
