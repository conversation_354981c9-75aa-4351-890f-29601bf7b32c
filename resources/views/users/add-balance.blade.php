<!--begin::Modal - Add balance-->
<div class="modal fade" id="kt_modal_add_balance" tabindex="-1" aria-hidden="true">
    <!--begin::Modal dialog-->
    <div class="modal-dialog modal-dialog-centered mw-650px">
        <!--begin::Modal content-->
        <div class="modal-content">
            <!--begin::Modal header-->
            <div class="modal-header" id="kt_modal_add_balance_header">
                <!--begin::Modal title-->
                <h2 class="fw-bold">Credit/Debit</h2>
                <!--end::Modal title-->
                <!--begin::Close-->
                <div class="btn btn-icon btn-sm btn-active-icon-primary" data-kt-modal-action="close">
                    <!--begin::Svg Icon | path: icons/duotune/arrows/arr061.svg-->
                    <span class="svg-icon svg-icon-1">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1"
                                  transform="rotate(-45 6 17.3137)" fill="currentColor"/>
                            <rect x="7.41422" y="6" width="16" height="2" rx="1" transform="rotate(45 7.41422 6)"
                                  fill="currentColor"/>
                        </svg>
                    </span>
                    <!--end::Svg Icon-->
                </div>
                <!--end::Close-->
            </div>
            <!--end::Modal header-->
            <!--begin::Modal body-->
            <div class="modal-body scroll-y mx-5 mx-xl-15 my-7">
                <!--begin::Form-->
                <form id="kt_modal_add_balance_form" method="POST" class="form" action="{{ route('webapi.user.balance') }}">
                    @csrf
                    <input type="hidden" id="payment_method" name="payment_method" value="manual" >
                    <input type="hidden" id="user_id" name="user_id" value="2" >
                    <input type="hidden" id="gateway_fee" name="gateway_fee" value="0" >
                    <!--begin::Scroll-->
                    <div class="d-flex flex-column scroll-y me-n7 pe-7" id="kt_modal_add_balance_scroll"
                         data-kt-scroll="true" data-kt-scroll-activate="{default: false, lg: true}"
                         data-kt-scroll-max-height="auto" data-kt-scroll-dependencies="#kt_modal_add_balance_header"
                         data-kt-scroll-wrappers="#kt_modal_add_balance_scroll" data-kt-scroll-offset="300px">

                        <!--begin::Input group-->
                        <div class="fv-row mb-7">
                            <!--begin::Label-->
                            <label class="required fw-semibold fs-6 mb-2">Transaction Amount</label>
                            <!--end::Label-->
                            <!--begin::Input-->
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">BDT</span>
                                </div>
                                <input type="number" id="amount" name="amount" class="form-control form-control-solid mb-3 mb-lg-0" placeholder="Enter amount (negative for deductions)" value="{{ old('amount') }}" required/>
                            </div>
                            <!--end::Input-->
                        </div>
                        <!--end::Input group-->

                        <!--begin::Input group-->
                        <div class="fv-row mb-7">
                            <!--begin::Label-->
                            <label class="required fw-semibold fs-6 mb-2">Transaction Remarks</label>
                            <!--end::Label-->
                            <!--begin::Input-->
                            <textarea name="remarks" id="remarks" class="form-control form-control-solid" rows="3" placeholder="Enter remarks in detail, e.g., transaction amount, purpose, etc." required>{{ old('remarks') }}</textarea>
                            <!--end::Input-->
                        </div>
                        <!--end::Input group-->
                    </div>
                    <!--end::Scroll-->
                    <!--begin::Actions-->
                    <div class="text-center pt-15">
                        <button type="reset" class="btn btn-light me-3" data-kt-modal-action="cancel">Discard</button>
                        <button type="submit" id="add-balance" type="submit" data-kt-contacts-type="submit" class="btn btn-primary">
                            <span class="indicator-label">Submit</span>
                            <span class="indicator-progress">Please wait...
                                <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                            </span>
                        </button>
                    </div>
                    <!--end::Actions-->
                </form>
                <!--end::Form-->
            </div>
            <!--end::Modal body-->
        </div>
        <!--end::Modal content-->
    </div>
    <!--end::Modal dialog-->
</div>
<!--end::Modal - Add balance-->
