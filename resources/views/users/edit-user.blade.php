<!--begin::Modal - Edit User-->
<div class="modal fade" id="kt_modal_edit_user" tabindex="-1" aria-hidden="true">
    <!--begin::Modal dialog-->
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <!--begin::Modal content-->
        <div class="modal-content">
            <!--begin::Modal header-->
            <div class="modal-header" id="kt_modal_edit_user_header">
                <!--begin::Modal title-->
                <h2 class="fw-bold">Edit User</h2>
                <!--end::Modal title-->
                <!--begin::Close-->
                <div class="btn btn-icon btn-sm btn-active-icon-primary btn-close cancel-button"
                     data-model="kt_modal_edit_user">
                    <!--begin::Svg Icon | path: icons/duotune/arrows/arr061.svg-->
                    <span class="svg-icon svg-icon-1">
                        <svg width="24" height="24" viewBox="0 0 24 24"
                             fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect opacity="0.5" x="6" y="17.3137" width="16"
                                  height="2" rx="1"
                                  transform="rotate(-45 6 17.3137)"
                                  fill="currentColor"/>
                            <rect x="7.41422" y="6" width="16" height="2" rx="1"
                                  transform="rotate(45 7.41422 6)"
                                  fill="currentColor"/>
                        </svg>
                    </span>
                    <!--end::Svg Icon-->
                </div>
                <!--end::Close-->
            </div>
            <!--end::Modal header-->
            <!--begin::Modal body-->
            <div class="modal-body scroll-y mx-3 mx-xl-5 my-7">
                <!--begin::Form-->
                <form id="kt_modal_edit_user_form" class="form" method="post">
                    @csrf
                    @method('PUT')
                    <input type="hidden" id="user_id" name="user_id" value="">
                    <!--begin::Scroll-->
                    <div class="d-flex flex-column scroll-y me-n7 pe-7" id="kt_modal_edit_user_scroll"
                         data-kt-scroll="true" data-kt-scroll-activate="{default: false, lg: true}"
                         data-kt-scroll-max-height="auto" data-kt-scroll-dependencies="#kt_modal_edit_user_header"
                         data-kt-scroll-wrappers="#kt_modal_edit_user_scroll" data-kt-scroll-offset="300px">

                        <!--begin::Personal Information Section-->
                        <div class="mb-8">
                            <h5 class="text-dark fw-bold mb-6">Personal Information</h5>
                            <div class="row g-6">
                                <!--begin::Full Name-->
                                <div class="col-lg-4 col-md-6 col-12">
                                    <div class="fv-row">
                                        <label class="required fw-semibold fs-6 mb-2" for="edit_user_name">Full Name</label>
                                        <input type="text" id="edit_user_name" name="name"
                                               class="form-control form-control-solid"
                                               placeholder="Enter full name"
                                               value=""
                                               tabindex="1"/>
                                        <div class="fv-plugins-message-container invalid-feedback"></div>
                                    </div>
                                </div>
                                <!--end::Full Name-->

                                <!--begin::Email-->
                                <div class="col-lg-4 col-md-6 col-12">
                                    <div class="fv-row">
                                        <label class="required fw-semibold fs-6 mb-2" for="edit_user_email">Email Address</label>
                                        <input type="email" id="edit_user_email" name="email"
                                               class="form-control form-control-solid"
                                               placeholder="<EMAIL>"
                                               value=""
                                               tabindex="2"/>
                                        <div class="fv-plugins-message-container invalid-feedback"></div>
                                    </div>
                                </div>
                                <!--end::Email-->

                                <!--begin::Mobile Number-->
                                <div class="col-lg-4 col-md-6 col-12">
                                    <div class="fv-row">
                                        <label class="required fw-semibold fs-6 mb-2" for="edit_user_phone">Mobile Number</label>
                                        <input type="text" id="edit_user_phone" name="phone"
                                               class="form-control form-control-solid"
                                               placeholder="8801617703137"
                                               value=""
                                               tabindex="3"/>
                                        <div class="fv-plugins-message-container invalid-feedback"></div>
                                    </div>
                                </div>
                                <!--end::Mobile Number-->
                            </div>
                        </div>
                        <!--end::Personal Information Section-->

                        <!--begin::Account Details Section-->
                        <div class="mb-8">
                            <h5 class="text-dark fw-bold mb-6">Account Details</h5>
                            <div class="row g-6">
                                <!--begin::Client Type-->
                                <div class="col-lg-4 col-md-6 col-12" id="role_selection_row" style="display: none;">
                                    <div class="fv-row">
                                        <label class="fs-6 fw-semibold mb-2" for="edit_user_role">Client Type</label>
                                        <select id="edit_user_role" name="role_id" class="form-select form-select-solid" tabindex="4">
                                            <option value="">Select client type</option>
                                            @foreach($roles as $role => $value)
                                                <option value="{{$role}}">{{$value}}</option>
                                            @endforeach
                                        </select>
                                        <div class="fv-plugins-message-container invalid-feedback"></div>
                                    </div>
                                </div>
                                <!--end::Client Type-->

                                <!--begin::SMS Balance-->
                                <div class="col-lg-4 col-md-6 col-12">
                                    <div class="fv-row">
                                        <label class="fw-semibold fs-6 mb-2" for="edit_user_balance">SMS Balance</label>
                                        <input type="number" id="edit_user_balance" name="balance"
                                               class="form-control form-control-solid"
                                               placeholder="0"
                                               value=""
                                               min="0"
                                               readonly
                                               tabindex="5"/>
                                        <div class="text-muted fs-7">Current balance (read-only)</div>
                                        <div class="fv-plugins-message-container invalid-feedback"></div>
                                    </div>
                                </div>
                                <!--end::SMS Balance-->

                                <!--begin::Account Status-->
                                <div class="col-lg-4 col-md-6 col-12" id="status_selection_row" style="display: none;">
                                    <div class="fv-row">
                                        <label class="fw-semibold fs-6 mb-2" for="edit_user_status">Account Status</label>
                                        <select id="edit_user_status" name="status" class="form-select form-select-solid" tabindex="6">
                                            <option value="1">Active</option>
                                            <option value="0">Inactive</option>
                                        </select>
                                        <div class="fv-plugins-message-container invalid-feedback"></div>
                                    </div>
                                </div>
                                <!--end::Account Status-->
                            </div>
                        </div>
                        <!--end::Account Details Section-->

                        <!--begin::Configuration Section-->
                        <div class="mb-8">
                            <h5 class="text-dark fw-bold mb-6">Configuration & Settings</h5>
                            <div class="row g-6">
                                <!--begin::Minimum Recharge-->
                                <div class="col-lg-4 col-md-6 col-12">
                                    <div class="fv-row">
                                        <label class="fw-semibold fs-6 mb-2" for="edit_user_min_recharge">Minimum Recharge (৳)</label>
                                        <input type="number" id="edit_user_min_recharge" name="minimum_recharge_amount"
                                               class="form-control form-control-solid"
                                               placeholder="0"
                                               value=""
                                               min="0"
                                               tabindex="7"/>
                                        <div class="fv-plugins-message-container invalid-feedback"></div>
                                    </div>
                                </div>
                                <!--end::Minimum Recharge-->

                                <!--begin::Remarks-->
                                <div class="col-lg-8 col-md-6 col-12" id="remarks_row" style="display: none;">
                                    <div class="fv-row">
                                        <label class="fw-semibold fs-6 mb-2" for="edit_user_remarks">Remarks</label>
                                        <textarea id="edit_user_remarks" name="remarks"
                                                  class="form-control form-control-solid"
                                                  placeholder="Optional notes or comments"
                                                  rows="3"
                                                  tabindex="8"></textarea>
                                        <div class="fv-plugins-message-container invalid-feedback"></div>
                                    </div>
                                </div>
                                <!--end::Remarks-->
                            </div>
                        </div>
                        <!--end::Configuration Section-->

                    </div>
                    <!--end::Scroll-->
                    <!--begin::Actions-->
                    <div class="text-center pt-10">
                        <button type="reset" class="btn btn-light me-3 cancel-button" data-model="kt_modal_edit_user" tabindex="10">
                            <i class="ki-duotone ki-cross fs-2">
                                <span class="path1"></span>
                                <span class="path2"></span>
                            </i>
                            Cancel
                        </button>
                        <button type="submit" class="btn btn-primary" id="update-user" data-kt-users-modal-action="submit" tabindex="9">
                            <span class="indicator-label">
                                <i class="ki-duotone ki-check fs-2"></i>
                                Update User
                            </span>
                            <span class="indicator-progress">
                                Please wait...
                                <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                            </span>
                        </button>
                    </div>
                    <!--end::Actions-->
                </form>
                <!--end::Form-->
            </div>
            <!--end::Modal body-->
        </div>
        <!--end::Modal content-->
    </div>
    <!--end::Modal dialog-->
</div>
<!--end::Modal - Edit User-->
