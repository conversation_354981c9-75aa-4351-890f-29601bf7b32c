<!--begin::Modal - Add User-->
<div class="modal fade" id="kt_modal_add_user" tabindex="-1" aria-hidden="true">
    <!--begin::Modal dialog-->
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <!--begin::Modal content-->
        <div class="modal-content">
            <!--begin::Modal header-->
            <div class="modal-header" id="kt_modal_add_user_header">
                <!--begin::Modal title-->
                <h2 class="fw-bold">Add New User</h2>
                <!--end::Modal title-->
                <!--begin::Close-->
                <div class="btn btn-icon btn-sm btn-active-icon-primary btn-close cancel-button"
                     data-model="kt_modal_add_user">
                    <!--begin::Svg Icon | path: icons/duotune/arrows/arr061.svg-->
                    <span class="svg-icon svg-icon-1">
                        <svg width="24" height="24" viewBox="0 0 24 24"
                             fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect opacity="0.5" x="6" y="17.3137" width="16"
                                  height="2" rx="1"
                                  transform="rotate(-45 6 17.3137)"
                                  fill="currentColor"/>
                            <rect x="7.41422" y="6" width="16" height="2" rx="1"
                                  transform="rotate(45 7.41422 6)"
                                  fill="currentColor"/>
                        </svg>
                    </span>
                    <!--end::Svg Icon-->
                </div>
                <!--end::Close-->
            </div>
            <!--end::Modal header-->
            <!--begin::Modal body-->
            <div class="modal-body scroll-y mx-3 mx-xl-5 my-7">
                <!--begin::Form-->
                <form id="kt_modal_add_user_form" class="form" method="post" action="{{route('users.store')}}">
                    @csrf
                    <!--begin::Scroll-->
                    <div class="d-flex flex-column scroll-y me-n7 pe-7" id="kt_modal_add_user_scroll"
                         data-kt-scroll="true" data-kt-scroll-activate="{default: false, lg: true}"
                         data-kt-scroll-max-height="auto" data-kt-scroll-dependencies="#kt_modal_add_user_header"
                         data-kt-scroll-wrappers="#kt_modal_add_user_scroll" data-kt-scroll-offset="300px">

                        <!--begin::Personal Information Section-->
                        <div class="mb-8">
                            <h5 class="text-dark fw-bold mb-6">Personal Information</h5>
                            <div class="row g-6">
                                <!--begin::Full Name-->
                                <div class="col-lg-4 col-md-6 col-12">
                                    <div class="fv-row">
                                        <label class="required fw-semibold fs-6 mb-2" for="user_name">Full Name</label>
                                        <input type="text" id="user_name" name="name"
                                               class="form-control form-control-solid"
                                               placeholder="Enter full name"
                                               value="{{old('name')}}"
                                               tabindex="1"/>
                                        <div class="fv-plugins-message-container invalid-feedback"></div>
                                    </div>
                                </div>
                                <!--end::Full Name-->

                                <!--begin::Email-->
                                <div class="col-lg-4 col-md-6 col-12">
                                    <div class="fv-row">
                                        <label class="required fw-semibold fs-6 mb-2" for="user_email">Email Address</label>
                                        <input type="email" id="user_email" name="email"
                                               class="form-control form-control-solid"
                                               placeholder="<EMAIL>"
                                               value="{{old('email')}}"
                                               tabindex="2"/>
                                        <div class="fv-plugins-message-container invalid-feedback"></div>
                                    </div>
                                </div>
                                <!--end::Email-->

                                <!--begin::Mobile Number-->
                                <div class="col-lg-4 col-md-6 col-12">
                                    <div class="fv-row">
                                        <label class="required fw-semibold fs-6 mb-2" for="user_phone">Mobile Number</label>
                                        <input type="text" id="user_phone" name="phone"
                                               class="form-control form-control-solid"
                                               placeholder="8801617703137"
                                               value="{{old('phone')}}"
                                               tabindex="3"/>
                                        <div class="fv-plugins-message-container invalid-feedback"></div>
                                    </div>
                                </div>
                                <!--end::Mobile Number-->
                            </div>
                        </div>
                        <!--end::Personal Information Section-->

                        <!--begin::Account Details Section-->
                        <div class="mb-8">
                            <h5 class="text-dark fw-bold mb-6">Account Details</h5>
                            <div class="row g-6">
                                <!--begin::Client Type-->
                                <div class="col-lg-4 col-md-6 col-12">
                                    <div class="fv-row">
                                        <label class="required fs-6 fw-semibold mb-2" for="user_role">Client Type</label>
                                        <select id="user_role" name="role_id" class="form-select form-select-solid" tabindex="4">
                                            <option value="">Select client type</option>
                                            @foreach($roles as $role => $value)
                                                <option value="{{$role}}" {{ old('role_id') == $role ? 'selected' : '' }}>{{$value}}</option>
                                            @endforeach
                                        </select>
                                        <div class="fv-plugins-message-container invalid-feedback"></div>
                                    </div>
                                </div>
                                <!--end::Client Type-->

                                <!--begin::SMS Balance-->
                                <div class="col-lg-4 col-md-6 col-12">
                                    <div class="fv-row">
                                        <label class="required fw-semibold fs-6 mb-2" for="user_balance">SMS Balance</label>
                                        <input type="number" id="user_balance" name="balance"
                                               class="form-control form-control-solid"
                                               placeholder="0"
                                               value="{{old('balance')}}"
                                               min="0"
                                               tabindex="5"/>
                                        <div class="fv-plugins-message-container invalid-feedback"></div>
                                    </div>
                                </div>
                                <!--end::SMS Balance-->

                                <!--begin::Validity-->
                                <div class="col-lg-4 col-md-6 col-12">
                                    <div class="fv-row">
                                        <label class="required fw-semibold fs-6 mb-2" for="user_validity">Account Validity</label>
                                        <select id="user_validity" name="validity" class="form-select form-select-solid" tabindex="6">
                                            <option value="">Select duration</option>
                                            <option value="1" {{ old('validity') == '1' ? 'selected' : '' }}>1 Month</option>
                                            <option value="2" {{ old('validity') == '2' ? 'selected' : '' }}>2 Months</option>
                                            <option value="3" {{ old('validity') == '3' ? 'selected' : '' }}>3 Months</option>
                                            <option value="4" {{ old('validity') == '4' ? 'selected' : '' }}>4 Months</option>
                                            <option value="5" {{ old('validity') == '5' ? 'selected' : '' }}>5 Months</option>
                                            <option value="6" {{ old('validity') == '6' ? 'selected' : '' }}>6 Months</option>
                                            <option value="12" {{ old('validity') == '12' ? 'selected' : '' }}>1 Year</option>
                                            <option value="24" {{ old('validity') == '24' ? 'selected' : '' }}>2 Years</option>
                                            <option value="60" {{ old('validity') == '60' ? 'selected' : '' }}>Unlimited</option>
                                        </select>
                                        <div class="fv-plugins-message-container invalid-feedback"></div>
                                    </div>
                                </div>
                                <!--end::Validity-->
                            </div>
                        </div>
                        <!--end::Account Details Section-->

                        <!--begin::Pricing & Configuration Section-->
                        <div class="mb-8">
                            <h5 class="text-dark fw-bold mb-6">Pricing & Configuration</h5>
                            <div class="row g-6">
                                <!--begin::Profit Margin-->
                                <div class="col-lg-4 col-md-6 col-12">
                                    <div class="fv-row">
                                        <label class="required fw-semibold fs-6 mb-2" for="user_profit_margin">Profit Margin</label>
                                        <input type="number" id="user_profit_margin" name="profit_margin"
                                               class="form-control form-control-solid"
                                               placeholder="0"
                                               value="{{old('profit_margin')}}"
                                               min="0"
                                               step="0.01"
                                               tabindex="7"/>
                                        <div class="fv-plugins-message-container invalid-feedback"></div>
                                    </div>
                                </div>
                                <!--end::Profit Margin-->

                                <!--begin::Margin Type-->
                                <div class="col-lg-4 col-md-6 col-12">
                                    <div class="fv-row">
                                        <label class="required fw-semibold fs-6 mb-2" for="user_margin_type">Margin Type</label>
                                        <select id="user_margin_type" name="margin_type" class="form-select form-select-solid" tabindex="8">
                                            <option value="1" {{ old('margin_type') == '1' ? 'selected' : '' }}>Percentage (%)</option>
                                            <option value="2" {{ old('margin_type') == '2' ? 'selected' : '' }}>Fixed Amount (৳)</option>
                                        </select>
                                        <div class="fv-plugins-message-container invalid-feedback"></div>
                                    </div>
                                </div>
                                <!--end::Margin Type-->

                                <!--begin::Minimum Recharge-->
                                <div class="col-lg-4 col-md-6 col-12">
                                    <div class="fv-row">
                                        <label class="required fw-semibold fs-6 mb-2" for="user_min_recharge">Minimum Recharge (৳)</label>
                                        <input type="number" id="user_min_recharge" name="minimum_recharge_amount"
                                               class="form-control form-control-solid"
                                               placeholder="0"
                                               value="{{old('minimum_recharge_amount')}}"
                                               min="0"
                                               tabindex="9"/>
                                        <div class="fv-plugins-message-container invalid-feedback"></div>
                                    </div>
                                </div>
                                <!--end::Minimum Recharge-->

                                <!--begin::Gateway Fee-->
                                <div class="col-lg-4 col-md-6 col-12">
                                    <div class="fv-row">
                                        <label class="required fw-semibold fs-6 mb-2" for="user_gateway_fee">Gateway Fee (৳)</label>
                                        <input type="number" id="user_gateway_fee" name="gateway_fee"
                                               class="form-control form-control-solid"
                                               placeholder="0"
                                               value="{{old('gateway_fee')}}"
                                               min="0"
                                               step="0.01"
                                               tabindex="10"/>
                                        <div class="fv-plugins-message-container invalid-feedback"></div>
                                    </div>
                                </div>
                                <!--end::Gateway Fee-->

                                <!--begin::Remarks-->
                                <div class="col-lg-8 col-md-6 col-12">
                                    <div class="fv-row">
                                        <label class="fw-semibold fs-6 mb-2" for="user_remarks">Remarks</label>
                                        <input type="text" id="user_remarks" name="remarks"
                                               class="form-control form-control-solid"
                                               placeholder="Optional notes or comments"
                                               value="{{old('remarks')}}"
                                               tabindex="11"/>
                                        <div class="fv-plugins-message-container invalid-feedback"></div>
                                    </div>
                                </div>
                                <!--end::Remarks-->
                            </div>
                        </div>
                        <!--end::Pricing & Configuration Section-->
                    </div>
                    <!--end::Scroll-->
                    <!--begin::Actions-->
                    <div class="text-center pt-10">
                        <button type="reset" class="btn btn-light me-3 cancel-button" data-model="kt_modal_add_user" tabindex="13">
                            <i class="ki-duotone ki-cross fs-2">
                                <span class="path1"></span>
                                <span class="path2"></span>
                            </i>
                            Cancel
                        </button>
                        <button type="submit" class="btn btn-primary" id="add-user" data-kt-users-modal-action="submit" tabindex="12">
                            <span class="indicator-label">
                                <i class="ki-duotone ki-plus fs-2"></i>
                                Create User
                            </span>
                            <span class="indicator-progress">
                                Please wait...
                                <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                            </span>
                        </button>
                    </div>
                    <!--end::Actions-->
                </form>
                <!--end::Form-->
            </div>
            <!--end::Modal body-->
        </div>
        <!--end::Modal content-->
    </div>
    <!--end::Modal dialog-->
</div>
<!--end::Modal - Add User-->
