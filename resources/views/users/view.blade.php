@extends('layouts.app')

@section('title', 'Users')

@section('content')
<!--begin::Container-->
<div class="container-xxl" id="kt_content_container">
    <!--begin::Layout-->
    <div class="d-flex flex-column flex-lg-row">
        <!--begin::Sidebar-->
        <div class="flex-column flex-lg-row-auto w-lg-250px w-xl-350px mb-10">
            <!--begin::Card-->
            <div class="card mb-5 mb-xl-8">
                <!--begin::Card body-->
                <div class="card-body">
                    <!--begin::Summary-->
                    <!--begin::User Info-->
                    <div class="d-flex flex-center flex-column py-5">
                        <!--begin::Avatar-->
                        <div class="symbol symbol-100px symbol-circle mb-7">
                            @if($user->photo)
                                <img src="{{ asset($user->photo) }}" alt="{{ $user->name }}" />
                            @else
                                <div class="symbol-label fs-3 bg-light-primary text-primary">
                                    {{ strtoupper(substr($user->name, 0, 1)) }}
                                </div>
                            @endif
                        </div>
                        <!--end::Avatar-->
                        <!--begin::Name-->
                        <a href="#" class="fs-3 text-gray-800 text-hover-primary fw-bold mb-3">{{$user->name}}</a>
                        <!--end::Name-->
                        <!--begin::Position-->
                        <div class="mb-9">
                            <!--begin::Badge-->
                            @foreach($user->roles as $role)
                                <div class="badge badge-lg badge-light-primary d-inline me-1">
                                    {{ ucfirst(str_replace('-', ' ', $role->name)) }}
                                </div>
                            @endforeach
                            <!--end::Badge-->
                        </div>
                        <!--end::Position-->

                        <!--begin::Status-->
                        <div class="mb-7">
                            <div class="d-flex align-items-center">
                                <span class="me-2">Status:</span>
                                @if($canManageStatus)
                                    <div class="form-check form-switch form-check-custom form-check-solid">
                                        <input class="form-check-input" type="checkbox" id="user_status_toggle"
                                               {{ $user->status === 1 ? 'checked' : '' }}
                                               data-user-id="{{ $user->id }}">
                                        <label class="form-check-label fw-semibold text-gray-400 ms-3" for="user_status_toggle">
                                            <span class="status-text">{{ $user->status === 1 ? 'Active' : 'Inactive' }}</span>
                                        </label>
                                    </div>
                                @else
                                    <span class="badge badge-{{ $user->status === 1 ? 'success' : 'danger' }}">
                                        {{ $user->status === 1 ? 'Active' : 'Inactive' }}
                                    </span>
                                @endif
                            </div>
                        </div>
                        <!--end::Status-->
                        <!--begin::Info-->
                        @if($user->company)
                        <div class="row g-2 mb-7">
                            <!--begin::Stats-->
                            <div class="col-12 col-sm-4">
                                <div class="border border-gray-300 border-dashed rounded py-3 px-2 text-center h-100">
                                    <div class="fs-6 fw-bold text-gray-700 d-flex align-items-center justify-content-center">
                                        <span class="me-1">${{ number_format($user->company->current_balance ?? 0, 2) }}</span>
                                        <!--begin::Svg Icon-->
                                        <span class="svg-icon svg-icon-4 svg-icon-success">
                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <rect opacity="0.5" x="13" y="6" width="13" height="2" rx="1" transform="rotate(90 13 6)" fill="currentColor" />
                                                <path d="M12.5657 8.56569L16.75 12.75C17.1642 13.1642 17.8358 13.1642 18.25 12.75C18.6642 12.3358 18.6642 11.6642 18.25 11.25L12.7071 5.70711C12.3166 5.31658 11.6834 5.31658 11.2929 5.70711L5.75 11.25C5.33579 11.6642 5.33579 12.3358 5.75 12.75C6.16421 13.1642 6.83579 13.1642 7.25 12.75L11.4343 8.56569C11.7467 8.25327 12.2533 8.25327 12.5657 8.56569Z" fill="currentColor" />
                                            </svg>
                                        </span>
                                        <!--end::Svg Icon-->
                                    </div>
                                    <div class="fw-semibold text-muted fs-7">Balance</div>
                                </div>
                            </div>
                            <!--end::Stats-->
                            <!--begin::Stats-->
                            <div class="col-12 col-sm-4">
                                <div class="border border-gray-300 border-dashed rounded py-3 px-2 text-center h-100">
                                    <div class="fs-6 fw-bold text-gray-700 d-flex align-items-center justify-content-center">
                                        <span class="me-1">{{ $user->children->count() }}</span>
                                        <!--begin::Svg Icon-->
                                        <span class="svg-icon svg-icon-4 svg-icon-primary">
                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M16.0173 9H15.3945C14.2833 9 13.263 9.61425 12.7431 10.5963L12.154 11.7091C12.0645 11.8781 12.1072 12.0868 12.2559 12.2071L12.6402 12.5183C13.2631 13.0225 13.7556 13.6691 14.0764 14.4035L14.2321 14.7601C14.2957 14.9058 14.4396 15 14.5987 15H18.6747C19.7297 15 20.4057 13.8774 19.912 12.945L18.6686 10.5963C18.1487 9.61425 17.1285 9 16.0173 9Z" fill="currentColor"/>
                                                <rect opacity="0.3" x="14" y="4" width="4" height="4" rx="2" fill="currentColor"/>
                                                <path d="M4.65486 14.8559C5.40389 13.1224 7.11161 12 9 12C10.8884 12 12.5961 13.1224 13.3451 14.8559L14.793 18.2067C15.3636 19.5271 14.3955 21 12.9571 21H5.04292C3.60453 21 2.63644 19.5271 3.20698 18.2067L4.65486 14.8559Z" fill="currentColor"/>
                                                <rect opacity="0.3" x="6" y="5" width="6" height="6" rx="3" fill="currentColor"/>
                                            </svg>
                                        </span>
                                        <!--end::Svg Icon-->
                                    </div>
                                    <div class="fw-semibold text-muted fs-7">Sub Users</div>
                                </div>
                            </div>
                            <!--end::Stats-->
                            <!--begin::Stats-->
                            <div class="col-12 col-sm-4">
                                <div class="border border-gray-300 border-dashed rounded py-3 px-2 text-center h-100">
                                    <div class="fs-6 fw-bold text-gray-700 d-flex align-items-center justify-content-center">
                                        <span class="me-1">{{ $coverages->count() }}</span>
                                        <!--begin::Svg Icon-->
                                        <span class="svg-icon svg-icon-4 svg-icon-warning">
                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path opacity="0.3" d="M20.5543 4.37824L12.1798 2.02473C12.0626 1.99176 11.9376 1.99176 11.8203 2.02473L3.44572 4.37824C3.18118 4.45258 3 4.6807 3 4.93945V13.569C3 14.6914 3.48509 15.8404 4.4417 16.984C5.17231 17.8575 6.18314 18.7345 7.446 19.5909C9.56752 21.0295 11.6566 21.912 11.7445 21.9488C11.8258 21.9829 11.9129 22 12.0001 22C12.0872 22 12.1744 21.983 12.2557 21.9488C12.3435 21.912 14.4326 21.0295 16.5541 19.5909C17.8169 18.7345 18.8277 17.8575 19.5584 16.984C20.515 15.8404 21 14.6914 21 13.569V4.93945C21 4.6807 20.8189 4.45258 20.5543 4.37824Z" fill="currentColor"/>
                                                <path d="M10.5606 11.3042L9.57283 10.3018C9.28174 10.0065 8.80522 10.0065 8.51412 10.3018C8.22897 10.5912 8.22897 11.0559 8.51412 11.3452L10.4182 13.2773C10.8099 13.6747 11.451 13.6747 11.8427 13.2773L15.4859 9.58051C15.771 9.29117 15.771 8.82648 15.4859 8.53714C15.1948 8.24176 14.7183 8.24176 14.4272 8.53714L11.7002 11.3042C11.3869 11.6221 10.874 11.6221 10.5606 11.3042Z" fill="currentColor"/>
                                            </svg>
                                        </span>
                                        <!--end::Svg Icon-->
                                    </div>
                                    <div class="fw-semibold text-muted fs-7">Coverage</div>
                                </div>
                            </div>
                            <!--end::Stats-->
                        </div>
                        @endif
                        <!--end::Info-->
                    </div>
                    <!--end::User Info-->
                    <!--end::Summary-->
                    <!--begin::Details toggle-->
                    <div class="d-flex flex-stack fs-4 py-3">
                        <div class="fw-bold rotate collapsible" data-bs-toggle="collapse" href="#kt_user_view_details" role="button" aria-expanded="false" aria-controls="kt_user_view_details">Details
                            <span class="ms-2 rotate-180">
													<!--begin::Svg Icon | path: icons/duotune/arrows/arr072.svg-->
													<span class="svg-icon svg-icon-3">
														<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
															<path d="M11.4343 12.7344L7.25 8.55005C6.83579 8.13583 6.16421 8.13584 5.75 8.55005C5.33579 8.96426 5.33579 9.63583 5.75 10.05L11.2929 15.5929C11.6834 15.9835 12.3166 15.9835 12.7071 15.5929L18.25 10.05C18.6642 9.63584 18.6642 8.96426 18.25 8.55005C17.8358 8.13584 17.1642 8.13584 16.75 8.55005L12.5657 12.7344C12.2533 13.0468 11.7467 13.0468 11.4343 12.7344Z" fill="currentColor" />
														</svg>
													</span>
                                <!--end::Svg Icon-->
												</span></div>
                        @if($canEditUser)
                        <span data-bs-toggle="tooltip" data-bs-trigger="hover" title="Edit user details">
													<a href="#" class="btn btn-sm btn-light-primary edit-user"
                                                       data-endpoint="{{route('webapi.user',$user->id)}}"
                                                       data-bs-toggle="modal" data-bs-target="#kt_modal_edit_user">Edit</a>
												</span>
                        @endif
                    </div>
                    <!--end::Details toggle-->
                    <div class="separator"></div>
                    <!--begin::Details content-->
                    <div id="kt_user_view_details" class="collapse show">
                        <div class="pb-5 fs-6">
                            <!--begin::Details item-->
                            <div class="fw-bold mt-5">Account ID</div>
                            <div class="text-gray-600">{{$user->username}}</div>
                            <!--end::Details item-->

                            <!--begin::Details item-->
                            <div class="fw-bold mt-5">Email</div>
                            <div class="text-gray-600">
                                <a href="mailto:{{$user->email}}" class="text-gray-600 text-hover-primary">{{$user->email}}</a>
                            </div>
                            <!--end::Details item-->

                            <!--begin::Details item-->
                            <div class="fw-bold mt-5">Phone</div>
                            <div class="text-gray-600">
                                <a href="tel:{{$user->phone}}" class="text-gray-600 text-hover-primary">{{$user->phone ?? 'N/A'}}</a>
                            </div>
                            <!--end::Details item-->

                            @if($user->company && (auth()->user()->hasRole('super-admin') || auth()->user()->hasRole('master-reseller')))
                            <!--begin::Details item-->
                            <div class="fw-bold mt-5">Company</div>
                            <div class="text-gray-600">{{$user->company->name}}</div>
                            <!--end::Details item-->
                            @endif

                            @if($user->parent && (auth()->user()->hasRole('super-admin') || auth()->user()->hasRole('master-reseller')))
                            <!--begin::Details item-->
                            <div class="fw-bold mt-5">Parent User</div>
                            <div class="text-gray-600">
                                <a href="{{ route('users.show', $user->parent->id) }}" class="text-gray-600 text-hover-primary">
                                    {{$user->parent->name}}
                                </a>
                            </div>
                            <!--end::Details item-->
                            @endif

                            <!--begin::Details item-->
                            <div class="fw-bold mt-5">Balance Expired</div>
                            <div class="text-gray-600">{{$user->balance_expired ?? 'N/A'}}</div>
                            <!--end::Details item-->

                            <!--begin::Details item-->
                            <div class="fw-bold mt-5">Last Login</div>
                            <div class="text-gray-600">
                                @if($logs->first())
                                    {{ $logs->first()->login_at->format('M d, Y H:i') }}
                                @else
                                    Never
                                @endif
                            </div>
                            <!--end::Details item-->

                            <!--begin::Details item-->
                            <div class="fw-bold mt-5">Member Since</div>
                            <div class="text-gray-600">{{$user->created_at->format('M d, Y')}}</div>
                            <!--end::Details item-->

                            @if($user->updated_at != $user->created_at)
                            <!--begin::Details item-->
                            <div class="fw-bold mt-5">Last Updated</div>
                            <div class="text-gray-600">{{$user->updated_at->format('M d, Y H:i')}}</div>
                            <!--end::Details item-->
                            @endif
                        </div>
                    </div>
                    <!--end::Details content-->
                </div>
                <!--end::Card body-->
            </div>
        </div>
        <!--end::Sidebar-->
        <!--begin::Content-->
        <div class="flex-lg-row-fluid ms-lg-15">
            <!--begin:::Tabs-->
            <ul class="nav nav-custom nav-tabs nav-line-tabs nav-line-tabs-2x border-0 fs-4 fw-semibold mb-8">
                <!--begin:::Tab item-->
                <li class="nav-item">
                    <a class="nav-link text-active-primary pb-4 active" data-kt-countup-tabs="true" data-bs-toggle="tab" href="#kt_user_view_overview">Overview</a>
                </li>
                <!--end:::Tab item-->

                <!--begin:::Tab item-->
                <li class="nav-item">
                    <a class="nav-link text-active-primary pb-4" data-bs-toggle="tab" href="#kt_user_view_price_coverage">Price & Coverage</a>
                </li>
                <!--end:::Tab item-->
                <!--begin:::Tab item-->
                <li class="nav-item">
                    <a class="nav-link text-active-primary pb-4" data-bs-toggle="tab" href="#kt_user_view_recharge_history_tab">Recharge History</a>
                </li>
                <!--end:::Tab item-->
                <!--begin:::Tab item-->
                <li class="nav-item">
                    <a class="nav-link text-active-primary pb-4" data-bs-toggle="tab" href="#kt_user_view_overview_events_and_logs_tab">Events & Logs</a>
                </li>
                <!--end:::Tab item-->

                <!--begin:::Tab item-->
                <li class="nav-item ms-auto">
                    <!--begin::Action menu-->
                    <a href="#" class="btn btn-primary ps-7" data-kt-menu-trigger="click" data-kt-menu-attach="parent" data-kt-menu-placement="bottom-end">Actions
                        <!--begin::Svg Icon | path: icons/duotune/arrows/arr072.svg-->
                        <span class="svg-icon svg-icon-2 me-0">
												<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
													<path d="M11.4343 12.7344L7.25 8.55005C6.83579 8.13583 6.16421 8.13584 5.75 8.55005C5.33579 8.96426 5.33579 9.63583 5.75 10.05L11.2929 15.5929C11.6834 15.9835 12.3166 15.9835 12.7071 15.5929L18.25 10.05C18.6642 9.63584 18.6642 8.96426 18.25 8.55005C17.8358 8.13584 17.1642 8.13584 16.75 8.55005L12.5657 12.7344C12.2533 13.0468 11.7467 13.0468 11.4343 12.7344Z" fill="currentColor" />
												</svg>
											</span>
                        <!--end::Svg Icon--></a>
                    <!--begin::Menu-->
                    <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold py-4 w-250px fs-6" data-kt-menu="true">
                        <!--begin::Menu item-->
                        <div class="menu-item px-5">
                            <div class="menu-content text-muted pb-2 px-5 fs-7 text-uppercase">Payments</div>
                        </div>
                        <!--end::Menu item-->
                        <!--begin::Menu item-->
                        <div class="menu-item px-5">
                            <a href="#" class="menu-link flex-stack px-5">Create payments
                                <i class="fas fa-exclamation-circle ms-2 fs-7" data-bs-toggle="tooltip" title="Specify a target name for future usage and reference"></i></a>
                        </div>
                        <!--end::Menu item-->
                        <!--begin::Menu separator-->
                        <div class="separator my-3"></div>
                        <!--end::Menu separator-->
                        <!--begin::Menu item-->
                        <div class="menu-item px-5">
                            <div class="menu-content text-muted pb-2 px-5 fs-7 text-uppercase">Account</div>
                        </div>
                        <!--end::Menu item-->
                        <!--begin::Menu item-->
                        <div class="menu-item px-5">
                            <a href="{{ route('users.destroy', $user->id) }}"
                               data-redirect-url="{{ route('users.index') }}"
                               class="btn btn-danger btn-sm delete-button-view">
                                Delete customer
                            </a>
                        </div>
                        <!--end::Menu item-->
                    </div>
                    <!--end::Menu-->
                    <!--end::Menu-->
                </li>
                <!--end:::Tab item-->
            </ul>
            <!--end:::Tabs-->
            <!--begin:::Tab content-->
            <div class="tab-content" id="myTabContent">
                <!--begin:::Tab pane-->
                <div class="tab-pane fade show active" id="kt_user_view_overview" role="tabpanel">
                    <!--begin::Card-->
                    <div class="card pt-4 mb-6 mb-xl-9">
                        <!--begin::Card header-->
                        <div class="card-header border-0">
                            <!--begin::Card title-->
                            <div class="card-title flex-column">
                                <h2>Overview Dashboard</h2>
                                <div class="fs-6 fw-semibold text-muted">Choose what messages you’d like to receive for each of your accounts.</div>
                            </div>
                            <!--end::Card title-->
                        </div>
                        <!--end::Card header-->
                        <!--begin::Card body-->
                        <div class="card-body">
                            <!--begin::Overview Placeholder-->
                            <div class="d-flex flex-center flex-column py-20">
                                <div class="mb-10">
                                    <i class="ki-duotone ki-chart-simple fs-5x text-gray-300">
                                        <span class="path1"></span>
                                        <span class="path2"></span>
                                        <span class="path3"></span>
                                        <span class="path4"></span>
                                    </i>
                                </div>
                                <h2 class="text-gray-600 fw-bold mb-5">Overview Content Coming Soon</h2>
                                <p class="text-gray-400 fs-6 text-center mb-0">
                                    The overview dashboard will be implemented in a future update.<br>
                                    For now, you can manage user details through the other available tabs.
                                </p>
                            </div>
                            <!--end::Overview Placeholder-->
                        </div>
                        <!--end::Card body-->
                        <!--begin::Card footer-->
                        <!--end::Card footer-->
                    </div>
                    <!--end::Card-->
                </div>
                <!--end:::Tab pane-->


                <!--begin:::Tab pane-->
                <div class="tab-pane fade" id="kt_user_view_price_coverage" role="tabpanel">
                    <!--begin::Card-->
                    <div class="card card-flush mb-6 mb-xl-9">
                        <!--begin::Card header-->
                        <div class="card-header mt-6">
                            <!--begin::Card title-->
                            <div class="card-title">
                                <h2>Price & Coverage</h2>
                            </div>
                            <!--end::Card title-->
                            <!--begin::Card toolbar-->
                            <div class="card-toolbar">
                                @if($canEditCoverage && auth()->user()->hasPermissionTo('coverage-create'))
                                <button type="button" class="btn btn-light-primary btn-sm" data-bs-toggle="modal" data-bs-target="#kt_modal_add_coverage">
                                    <!--begin::Svg Icon-->
                                    <span class="svg-icon svg-icon-2">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <rect opacity="0.5" x="11.364" y="20.364" width="16" height="2" rx="1" transform="rotate(-90 11.364 20.364)" fill="currentColor"/>
                                            <rect x="4.36396" y="11.364" width="16" height="2" rx="1" fill="currentColor"/>
                                        </svg>
                                    </span>
                                    <!--end::Svg Icon-->
                                    Add New Coverage
                                </button>
                                @else
                                <div class="text-muted fs-7">
                                    @if(!$canEditCoverage)
                                        <i class="ki-duotone ki-information-5 fs-6 me-1">
                                            <span class="path1"></span>
                                            <span class="path2"></span>
                                            <span class="path3"></span>
                                        </i>
                                        Read-only access - You can view but not modify coverage settings
                                    @endif
                                </div>
                                @endif
                            </div>
                            <!--end::Card toolbar-->
                        </div>
                        <!--end::Card header-->
                        <!--begin::Card body-->
                        <div class="card-body p-9 pt-4">
                            <!--begin::Tab Content-->
                            <div class="tab-content">
                                <!--begin::Table Container-->
                                <div class="table-responsive">
                                    <!--begin::Table-->
                                    <table class="table align-middle table-row-dashed fs-6 gy-5" id="kt_table_user_coverages" data-user-id="{{ $user->id }}">
                                        <thead>
                                        <tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
                                            <th class="d-none d-md-table-cell">Company</th>
                                            <th>Operator</th>
                                            <th class="w-80px text-center">Prefix</th>
                                            <th class="w-100px text-end d-none d-lg-table-cell">Masking Price</th>
                                            <th class="w-110px text-end d-none d-lg-table-cell">Non Masking Price</th>
                                            <th class="w-80px text-center d-none d-md-table-cell">Status</th>
                                            <th class="w-80px text-end">Actions</th>
                                        </tr>
                                        </thead>
                                        <tbody class="fw-semibold text-gray-600">
                                        </tbody>
                                    </table>
                                    <!--end::Table-->
                                </div>
                                <!--end::Table Container-->
                            </div>
                            <!--end::Tab Content-->
                        </div>
                        <!--end::Card body-->
                    </div>
                    <!--end::Card-->
                </div>
                <!--end:::Tab pane-->
                <!--begin:::Tab pane-->
                <div class="tab-pane fade" id="kt_user_view_recharge_history_tab" role="tabpanel">
                    <!--begin::Card-->
                    <div class="card pt-4 mb-6 mb-xl-9">
                        <!--begin::Card header-->
                        <div class="card-header border-0">
                            <!--begin::Card title-->
                            <div class="card-title flex-column">
                                <h2>Recharge History</h2>
                                <div class="fs-6 fw-semibold text-muted">View all recharge transactions for this user</div>
                            </div>
                            <!--end::Card title-->
                            <!--begin::Card toolbar-->
                            <div class="card-toolbar">
                                <button type="button" class="btn btn-light-primary btn-sm" id="kt_export_recharge_history">
                                    <!--begin::Svg Icon-->
                                    <span class="svg-icon svg-icon-2">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path opacity="0.3" d="M19 22H5C4.4 22 4 21.6 4 21V3C4 2.4 4.4 2 5 2H14L20 8V21C20 21.6 19.6 22 19 22Z" fill="currentColor"/>
                                            <path d="M15 8H20L14 2V7C14 7.6 14.4 8 15 8Z" fill="currentColor"/>
                                        </svg>
                                    </span>
                                    <!--end::Svg Icon-->
                                    Export CSV
                                </button>
                            </div>
                            <!--end::Card toolbar-->
                        </div>
                        <!--end::Card header-->
                        <!--begin::Card body-->
                        <div class="card-body p-9 pt-4">
                            <!--begin::Table Container-->
                            <div class="table-responsive">
                                <!--begin::Table-->
                                <table class="table align-middle table-row-dashed fs-6 gy-5" id="kt_table_user_recharge_history" data-user-id="{{ $user->id }}">
                                    <thead>
                                    <tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
                                        <th class="w-100px">Date</th>
                                        <th class="w-80px">Gateway</th>
                                        <th class="w-120px d-none d-md-table-cell">Transaction ID</th>
                                        <th class="w-100px text-end">Amount</th>
                                        <th class="w-80px text-center d-none d-lg-table-cell">Status</th>
                                        <th class="w-150px d-none d-lg-table-cell">Remarks</th>
                                        <th class="w-100px text-end">Actions</th>
                                    </tr>
                                    </thead>
                                    <tbody class="fw-semibold text-gray-600">
                                    </tbody>
                                </table>
                                <!--end::Table-->
                            </div>
                            <!--end::Table Container-->
                        </div>
                        <!--end::Card body-->
                    </div>
                    <!--end::Card-->
                </div>
                <!--end:::Tab pane-->
                <!--begin:::Tab pane-->
                <div class="tab-pane fade" id="kt_user_view_overview_events_and_logs_tab" role="tabpanel">
                    <!--begin::Card-->
                    <!--begin::Col-->
                    <div class="col-xxl-12">
                        <!--begin::Security summary-->
                        <div class="card mb-5 mb-lg-10">
                            <!--begin::Card header-->
                            <div class="card-header">
                                <!--begin::Heading-->
                                <div class="card-title">
                                    <h3>Security Summary</h3>
                                </div>
                                <!--end::Heading-->
                                <!--end::Title-->
                                <!--begin::Toolbar-->
                                <div class="card-toolbar">
                                </div>
                                <!--end::Toolbar-->
                            </div>
                            <!--end::Header-->
                            <!--begin::Body-->
                            <div class="card-body pt-7 pb-0 px-0">
                                <div class="row p-0 mb-5 px-9">
                                    <!--begin::Col-->
                                    <div class="col">
                                        <div
                                            class="border border-dashed border-gray-300 text-center min-w-125px rounded pt-4 pb-2 my-3">
                                            <span class="fs-4 fw-semibold text-success d-block">User Sign-in</span>
                                            <span class="fs-2hx fw-bold text-gray-900" data-kt-countup="true"
                                                  data-kt-countup-value="25">0</span>
                                        </div>
                                    </div>
                                    <!--end::Col-->
                                    <!--begin::Col-->
                                    <div class="col">
                                        <div
                                            class="border border-dashed border-gray-300 text-center min-w-125px rounded pt-4 pb-2 my-3">
                                            <span class="fs-4 fw-semibold text-primary d-block">Admin Sign-in</span>
                                            <span class="fs-2hx fw-bold text-gray-900" data-kt-countup="true"
                                                  data-kt-countup-value="1">0</span>
                                        </div>
                                    </div>
                                    <!--end::Col-->
                                    <!--begin::Col-->
                                    <div class="col">
                                        <div
                                            class="border border-dashed border-gray-300 text-center min-w-125px rounded pt-4 pb-2 my-3">
                                            <span class="fs-4 fw-semibold text-danger d-block">Failed Attempts</span>
                                            <span class="fs-2hx fw-bold text-gray-900" data-kt-countup="true"
                                                  data-kt-countup-value="1">0</span>
                                        </div>
                                    </div>
                                    <!--end::Col-->
                                </div>
                            </div>
                            <!--end::Body-->
                        </div>
                        <!--end::Security summary-->
                    </div>
                    <!--end::Col-->
                    <!--begin::Login sessions-->
                    <div class="card pt-4 mb-6 mb-xl-9">
                        <!--begin::Card header-->
                        <div class="card-header">
                            <!--begin::Heading-->
                            <div class="card-title">
                                <h3>Login Sessions</h3>
                            </div>
                            <!--end::Heading-->
                            <div class="card-toolbar">
                                <!--begin::Filter-->
                                <button type="button" class="btn btn-sm btn-flex btn-light-primary" id="kt_modal_sign_out_sesions">
                                    <!--begin::Svg Icon | path: icons/duotune/arrows/arr077.svg-->
                                    <span class="svg-icon svg-icon-3">
															<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
																<rect opacity="0.3" x="4" y="11" width="12" height="2" rx="1" fill="currentColor" />
																<path d="M5.86875 11.6927L7.62435 10.2297C8.09457 9.83785 8.12683 9.12683 7.69401 8.69401C7.3043 8.3043 6.67836 8.28591 6.26643 8.65206L3.34084 11.2526C2.89332 11.6504 2.89332 12.3496 3.34084 12.7474L6.26643 15.3479C6.67836 15.7141 7.3043 15.6957 7.69401 15.306C8.12683 14.8732 8.09458 14.1621 7.62435 13.7703L5.86875 12.3073C5.67684 12.1474 5.67684 11.8526 5.86875 11.6927Z" fill="currentColor" />
																<path d="M8 5V6C8 6.55228 8.44772 7 9 7C9.55228 7 10 6.55228 10 6C10 5.44772 10.4477 5 11 5H18C18.5523 5 19 5.44772 19 6V18C19 18.5523 18.5523 19 18 19H11C10.4477 19 10 18.5523 10 18C10 17.4477 9.55228 17 9 17C8.44772 17 8 17.4477 8 18V19C8 20.1046 8.89543 21 10 21H19C20.1046 21 21 20.1046 21 19V5C21 3.89543 20.1046 3 19 3H10C8.89543 3 8 3.89543 8 5Z" fill="currentColor" />
															</svg>
														</span>
                                    <!--end::Svg Icon-->Sign out all sessions</button>
                                <!--end::Filter-->
                            </div>
                            <!--end::Card toolbar-->
                        </div>
                        <!--end::Card header-->
                        <!--begin::Card body-->
                        <div class="card-body pt-0 pb-5">
                            <!--begin::Table wrapper-->
                            <div class="table-responsive">
                                <!--begin::Table-->
                                <table class="table align-middle table-row-bordered table-row-solid gy-4 gs-9">
                                    <!--begin::Thead-->
                                    <thead class="border-gray-200 fs-5 fw-semibold bg-lighten">
                                    <tr>
                                        <th class="min-w-150px">IP Address</th>
                                        <th class="min-w-150px">Browser</th>
                                        <th class="min-w-150px">Location</th>
                                        <th class="min-w-150px">Login At</th>
                                        <th class="min-w-150px">Login Successful</th>
                                        <th class="min-w-150px">Logout At</th>
                                        <th class="min-w-150px">Cleared By User</th>
                                    </tr>
                                    </thead>
                                    <!--end::Thead-->
                                    <!--begin::Tbody-->
                                    <tbody class="fw-6 fw-semibold text-gray-600">
                                    @foreach($logs as $log)
                                        @php
                                            $localtion = (isset($log->location['city']) && $log->location['country']) ? $log->location['city'].','.$log->location['country']:'';
                                        @endphp
                                        <tr>
                                            <td>{{$log->ip_address}}</td>
                                            <td>{{$log->user_agent}}</td>
                                            <td>{{$localtion}}</td>
                                            <td>{{$log->login_at}}</td>
                                            <td>{{$log->login_successful?'Yes':'No'}}</td>
                                            <td>{{$log->logout_at}}</td>
                                            <td>{{$log->cleared_by_user?'Yes':'No'}}</td>
                                        </tr>
                                    @endforeach
                                    </tbody>
                                    <!--end::Tbody-->
                                </table>
                                <!--end::Table-->
                            </div>
                            <!--end::Table wrapper-->
                        </div>
                        <!--end::Card body-->
                    </div>
                    <!--end::Login sessions-->
                    <!--end::Card-->
                </div>
                <!--end:::Tab pane-->

            </div>
            <!--end:::Tab content-->
        </div>
        <!--end::Content-->
    </div>
    <!--end::Layout-->
    <!--begin::Modals-->
    <!--begin::Modal - Update user details-->
    <!--end::Modal - New Card-->
    @include('users.edit-user')
    <!--end::Modal - Update user details-->

    <!--begin::Modal - Add Coverage-->
    @include('users.modals.add-coverage')
    <!--end::Modal - Add Coverage-->

    <!--begin::Modal - Edit Coverage-->
    @include('users.modals.edit-coverage')
    <!--end::Modal - Edit Coverage-->
    <!--end::Modals-->
</div>
<!--end::Container-->
@stop

@section('js')
<script>
// Global variables for DataTable functionality
window.userId = {{ $user->id }};
window.isSuperAdmin = {{ auth()->user()->hasRole('super-admin') ? 'true' : 'false' }};
window.isMasterReseller = {{ auth()->user()->hasRole('master-reseller') ? 'true' : 'false' }};
window.routes = {
    coverageData: "{{ route('users.coverages.data', $user->id) }}",
    coverageStore: "{{ route('users.coverages.store', $user->id) }}",
    coverageShow: "{{ url('users/:userId/coverages/:coverageId') }}",
    coverageUpdate: "{{ url('users/:userId/coverages/:coverageId') }}",
    coverageDestroy: "{{ url('users/:userId/coverages/:coverageId') }}",
    userStatusUpdate: "{{ route('users.status.update', $user->id) }}",
    rechargeHistoryData: "{{ route('users.recharge-history.data', $user->id) }}"
};
</script>

<!-- Include centralized DataTable JavaScript -->
<script src="{{ asset('js/datatable.js') }}"></script>
@stop
