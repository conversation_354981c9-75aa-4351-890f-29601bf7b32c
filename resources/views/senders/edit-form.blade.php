<div class="col-lg-12 col-xl-12">
    <div class="card card-flush h-lg-100" id="kt_contacts_main">
        <div class="card-header pt-7" id="kt_chat_contacts_header">
            <div class="card-title">
                <span class="svg-icon svg-icon-1 me-2">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M20 14H18V10H20C20.6 10 21 10.4 21 11V13C21 13.6 20.6 14 20 14ZM21 19V17C21 16.4 20.6 16 20 16H18V20H20C20.6 20 21 19.6 21 19ZM21 7V5C21 4.4 20.6 4 20 4H18V8H20C20.6 8 21 7.6 21 7Z" fill="currentColor"/>
                        <path opacity="0.3" d="M17 22H3C2.4 22 2 21.6 2 21V3C2 2.4 2 2 3 2H17C17.6 2 18 2.4 18 3V21C18 21.6 17.6 22 17 22ZM10 7C8.9 7 8 7.9 8 9C8 10.1 8.9 11 10 11C11.1 11 12 10.1 12 9C12 7.9 11.1 7 10 7ZM13.3 16C14 16 14.5 15.3 14.3 14.7C13.7 13.2 12 12 10.1 12C8.10001 12 6.49999 13.1 5.89999 14.7C5.59999 15.3 6.19999 16 7.39999 16H13.3Z" fill="currentColor"/>
                    </svg>
                </span>
                <h2>Update Sender</h2>
            </div>
        </div>

        <div class="card-body pt-5">
            <form method="POST" class="form" action="{{ route('senders.update', $sender->id) }}" enctype="multipart/form-data">
                @csrf
                @method('PATCH')

                <!-- Sender ID (Editable only by super-admin) -->
                <div class="fv-row mb-7">
                    <label class="fs-6 fw-semibold form-label mt-3">
                        <span class="required">Sender ID</span>
                        <i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip" title="Enter the Sender ID."></i>
                    </label>
                    <input type="text" class="form-control form-control-solid" name="name"
                           value="{{ $sender->name }}"
                           {{ auth()->user()->hasRole('super-admin') ? '' : 'readonly' }} required/>
                </div>

                <!-- Server ID Dropdown (Super-admin only) -->
                @if(auth()->user()->hasRole('super-admin'))
                    <div class="fv-row mb-7">
                        <label class="fs-6 fw-semibold form-label mt-3">
                            <span>Server ID</span>
                            <i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip" title="Select the Server ID."></i>
                        </label>
                        <select name="server_id" class="form-select form-control-solid">
                            <option value="">Select Server</option>
                            @foreach($servers as $server)
                                <option value="{{ $server->id }}" {{ $sender->server_id == $server->id ? 'selected' : '' }}>
                                    {{ $server->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Is Server Default Toggle -->
                    <div class="fv-row mb-7">
                        <label class="fs-6 fw-semibold form-label mt-3">
                            <span>Set as Default for Server</span>
                            <i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip" title="Mark this as the default for the server."></i>
                        </label>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" name="is_server_default" value="1" {{ $sender->is_server_default ? 'checked' : '' }}>
                            <label class="form-check-label">Yes</label>
                        </div>
                    </div>
                @endif

                <!-- Required Documents (View mode only) -->
                <div class="fv-row mb-7">
                    <label class="fs-6 fw-semibold form-label mt-3">
                        <span>Existing Documents</span>
                    </label>
                    <div id="existing-file-preview" class="d-flex flex-wrap gap-3">
                        @foreach($requiredDocuments as $document)
                            @if(is_string($document))
                                <div class="file-preview-item text-center">
                                    <a href="{{ asset('storage/' . $document) }}" target="_blank">
                                        @if(pathinfo($document, PATHINFO_EXTENSION) === 'pdf')
                                            <i class="fas fa-file-pdf fa-2x text-danger"></i>
                                        @elseif(in_array(pathinfo($document, PATHINFO_EXTENSION), ['jpg', 'jpeg', 'png']))
                                            <img src="{{ asset('storage/' . $document) }}" class="img-thumbnail" style="max-width: 100px; max-height: 100px;">
                                        @else
                                            <i class="fas fa-file-alt fa-2x text-primary"></i>
                                        @endif
                                    </a>
                                    <div class="text-truncate" style="max-width: 100px;">{{ basename($document) }}</div>
                                </div>
                            @endif
                        @endforeach
                    </div>
                </div>

                <!-- Upload New Documents -->
                <div class="fv-row mb-7">
                    <label class="fs-6 fw-semibold form-label mt-3">
                        <span>Upload New Documents (optional)</span>
                        <i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip" title="Upload required documents."></i>
                    </label>
                    <input type="file" name="required_documents[]" class="form-control form-control-solid" multiple id="required_documents">
                    <small class="text-muted">Selecting new files will replace existing documents.</small>
                </div>

                @if(auth()->user()->hasRole('super-admin'))
                    <!-- Default Toggle (Super-admin only) -->
                    <div class="fv-row mb-7">
                        <label class="fs-6 fw-semibold form-label mt-3">
                            <span class="required">Default</span>
                            <i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip" title="Select if default or not."></i>
                        </label>
                        <select class="form-select form-select-solid" name="is_default" required>
                            <option value="1" {{ $sender->is_default == 1 ? 'selected' : '' }}>Yes</option>
                            <option value="0" {{ $sender->is_default == 0 ? 'selected' : '' }}>No</option>
                        </select>
                    </div>

                    <!-- Status (Super-admin only) -->
                    <div class="fv-row mb-7">
                        <label class="fs-6 fw-semibold form-label mt-3">
                            <span class="required">Status</span>
                            <i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip" title="Select the status."></i>
                        </label>
                        <select class="form-select form-select-solid" name="status" required>
                            <option value="">Select a Status</option>
                            @foreach($senderStatus as $status)
                                <option value="{{ $status }}" {{ $sender->status == $status ? 'selected' : '' }}>
                                    {{ $status }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                @endif

                <div class="separator mb-6"></div>

                <div class="d-flex justify-content-end">
                    <button type="reset" class="btn btn-light me-3">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <span class="indicator-label">Save</span>
                        <span class="indicator-progress">Please wait...
                            <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                        </span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>


<script>
    document.getElementById('required_documents').addEventListener('change', function(event) {
        const newFilePreview = document.getElementById('new-file-preview');
        newFilePreview.innerHTML = ''; // Clear any previous previews

        Array.from(event.target.files).forEach(file => {
            const fileElement = document.createElement('div');
            fileElement.className = 'file-preview-item text-center position-relative';

            // Check file type for different icons or previews
            if (file.type.startsWith('image/')) {
                const img = document.createElement('img');
                img.src = URL.createObjectURL(file);
                img.className = 'img-thumbnail';
                img.style.maxWidth = '100px';
                img.style.maxHeight = '100px';
                img.onload = () => URL.revokeObjectURL(img.src);
                fileElement.appendChild(img);
            } else if (file.type === 'application/pdf') {
                const pdfIcon = document.createElement('div');
                pdfIcon.innerHTML = `<i class="fas fa-file-pdf fa-2x text-danger"></i>`;
                fileElement.appendChild(pdfIcon);
            } else {
                const docIcon = document.createElement('div');
                docIcon.innerHTML = `<i class="fas fa-file-alt fa-2x text-primary"></i>`;
                fileElement.appendChild(docIcon);
            }

            // Display file name and size
            const fileInfo = document.createElement('div');
            fileInfo.className = 'text-truncate';
            fileInfo.style.maxWidth = '100px';
            fileInfo.innerText = `${file.name} (${(file.size / 1024).toFixed(1)} KB)`;
            fileElement.appendChild(fileInfo);

            newFilePreview.appendChild(fileElement);
        });
    });
</script>
