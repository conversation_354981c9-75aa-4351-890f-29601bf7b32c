<!--begin::<PERSON><PERSON> - Add Sender-->
<div class="modal fade" id="kt_modal_add_sender" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered mw-650px">
        <div class="modal-content">
            <div class="modal-header" id="kt_modal_add_user_header">
                <h2 class="fw-bold">Add New Sender</h2>
                <div class="btn btn-icon btn-sm btn-active-icon-primary" data-kt-modal-action="close">
                    <span class="svg-icon svg-icon-1">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1" transform="rotate(-45 6 17.3137)" fill="currentColor"/>
                            <rect x="7.41422" y="6" width="16" height="2" rx="1" transform="rotate(45 7.41422 6)" fill="currentColor"/>
                        </svg>
                    </span>
                </div>
            </div>
            <div class="modal-body scroll-y mx-5 mx-xl-15 my-7">
                <form id="kt_modal_add_sender_form" method="POST" enctype="multipart/form-data" class="form">
                    @csrf
                    <div class="d-flex flex-column scroll-y me-n7 pe-7" id="kt_modal_add_user_scroll"
                         data-kt-scroll="true" data-kt-scroll-activate="{default: false, lg: true}"
                         data-kt-scroll-max-height="auto" data-kt-scroll-dependencies="#kt_modal_add_user_header"
                         data-kt-scroll-wrappers="#kt_modal_add_user_scroll" data-kt-scroll-offset="300px">

                        <div class="fv-row mb-7">
                            <label class="required fw-semibold fs-6 mb-2">Sender ID</label>
                            <input type="text" id="sender-name" name="name" class="form-control form-control-solid mb-3 mb-lg-0" placeholder="Write sender name" value="{{ old('name') }}" />
                        </div>

                        <!--begin::Required Documents-->
                        <div class="fv-row mb-7">
                            <label class="fw-semibold fs-6 mb-2">Required Documents (optional)</label>
                            <input type="file" name="required_documents[]" class="form-control form-control-solid" multiple id="required_documents" />
                            <small class="text-muted">Upload multiple files if needed</small>
                            <div id="file-preview" class="mt-3 d-flex flex-wrap gap-3"></div>
                        </div>
                        <!--end::Required Documents-->

                        <!--begin::Server ID Dropdown - Super Admin Only-->
                        @if(auth()->user()->hasRole('super-admin'))
                            <div class="fv-row mb-7">
                                <label class="fw-semibold fs-6 mb-2">Server ID</label>
                                <select name="server_id" class="form-select form-control-solid">
                                    <option value="">Select Server</option>
                                    @foreach($servers as $server)
                                        <option value="{{ $server->id }}">{{ $server->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="fv-row mb-7">
                                <label class="fw-semibold fs-6 mb-2">Set as Default for Server</label>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="is_server_default" name="is_server_default" value="1">
                                    <label class="form-check-label" for="is_server_default">Yes</label>
                                </div>
                            </div>
                        @endif
                    </div>

                    <div class="text-center pt-15">
                        <button type="reset" class="btn btn-light me-3" data-kt-modal-action="cancel">Discard</button>
                        <button type="button" id="add-sender" class="btn btn-primary">
                            <span class="indicator-label">Submit</span>
                            <span class="indicator-progress">Please wait...
                                <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                            </span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<!--end::Modal - Add Sender-->

<script>
    document.getElementById('required_documents').addEventListener('change', function(event) {
        const filePreviewContainer = document.getElementById('file-preview');
        filePreviewContainer.innerHTML = '';

        Array.from(event.target.files).forEach(file => {
            const fileElement = document.createElement('div');
            fileElement.className = 'file-preview-item d-flex flex-column align-items-center text-center';

            if (file.type.startsWith('image/')) {
                const img = document.createElement('img');
                img.src = URL.createObjectURL(file);
                img.className = 'img-thumbnail';
                img.style.maxWidth = '100px';
                img.style.maxHeight = '100px';
                img.onload = () => URL.revokeObjectURL(img.src);
                fileElement.appendChild(img);
            } else if (file.type === 'application/pdf') {
                const pdfIcon = document.createElement('div');
                pdfIcon.className = 'pdf-icon';
                pdfIcon.innerHTML = `<i class="fas fa-file-pdf fa-2x text-danger"></i>`;
                const fileInfo = document.createElement('span');
                fileInfo.textContent = `${file.name} (${(file.size / 1024).toFixed(1)} KB)`;
                fileElement.appendChild(pdfIcon);
                fileElement.appendChild(fileInfo);
            } else {
                const docIcon = document.createElement('div');
                docIcon.className = 'doc-icon';
                docIcon.innerHTML = `<i class="fas fa-file-alt fa-2x text-primary"></i>`;
                const fileInfo = document.createElement('span');
                fileInfo.textContent = `${file.name} (${(file.size / 1024).toFixed(1)} KB)`;
                fileElement.appendChild(docIcon);
                fileElement.appendChild(fileInfo);
            }

            filePreviewContainer.appendChild(fileElement);
        });
    });

</script>
