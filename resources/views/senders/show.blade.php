@extends('layouts.app')

@section('title', 'Sender Details')

@section('content')
<!--begin::Container-->
<div class="container-xxl" id="kt_content_container">
    <!--begin::Card-->
    <div class="card">
        <!--begin::Card header-->
        <div class="card-header">
            <div class="card-title">
                <h3 class="fw-bold m-0">{{ $sender->name }} - Sender Details</h3>
            </div>
            <div class="card-toolbar">
                <a href="{{ route('senders.index') }}" class="btn btn-sm btn-light">
                    <i class="ki-duotone ki-arrow-left fs-2"><span class="path1"></span><span class="path2"></span></i>
                    Back
                </a>
            </div>
        </div>
        <!--end::Card header-->

        <!--begin::Card body-->
        <div class="card-body">
            <!--begin::Row-->
            <div class="row g-5">
                <!--begin::Col-->
                <div class="col-md-6">
                    <!--begin::Basic Information-->
                    <div class="d-flex flex-column gap-3">
                        <div class="d-flex align-items-center">
                            <span class="fw-semibold text-gray-600 w-100px">ID:</span>
                            <span class="text-gray-800">#{{ $sender->id }}</span>
                        </div>
                        <div class="d-flex align-items-center">
                            <span class="fw-semibold text-gray-600 w-100px">Name:</span>
                            <span class="text-gray-800">{{ $sender->name }}</span>
                        </div>
                        <div class="d-flex align-items-center">
                            <span class="fw-semibold text-gray-600 w-100px">Type:</span>
                            <span class="badge badge-{{ $sender->is_masking ? 'primary' : 'success' }}">
                                {{ $sender->is_masking ? 'Masking' : 'Non-Masking' }}
                            </span>
                        </div>
                        <div class="d-flex align-items-center">
                            <span class="fw-semibold text-gray-600 w-100px">Status:</span>
                            <span class="badge badge-{{
                                $sender->status === 'Approved' ? 'success' :
                                ($sender->status === 'Pending Approved' ? 'warning' : 'secondary')
                            }}">
                                {{ $sender->status }}
                            </span>
                        </div>
                        <div class="d-flex align-items-center">
                            <span class="fw-semibold text-gray-600 w-100px">Default:</span>
                            <span class="badge badge-{{ $sender->is_default ? 'success' : 'light' }}">
                                {{ $sender->is_default ? 'Yes' : 'No' }}
                            </span>
                        </div>
                        <div class="d-flex align-items-center">
                            <span class="fw-semibold text-gray-600 w-100px">Server Default:</span>
                            <span class="badge badge-{{ $sender->is_server_default ? 'success' : 'light' }}">
                                {{ $sender->is_server_default ? 'Yes' : 'No' }}
                            </span>
                        </div>
                    </div>
                    <!--end::Basic Information-->
                </div>
                <!--end::Col-->

                <!--begin::Col-->
                <div class="col-md-6">
                    <!--begin::Additional Information-->
                    <div class="d-flex flex-column gap-3">
                        @if($sender->company)
                        <div class="d-flex align-items-center">
                            <span class="fw-semibold text-gray-600 w-100px">Company:</span>
                            <span class="text-gray-800">{{ $sender->company->name }}</span>
                        </div>
                        <div class="d-flex align-items-center">
                            <span class="fw-semibold text-gray-600 w-100px">Balance:</span>
                            <span class="text-gray-800">${{ number_format($sender->company->current_balance ?? 0, 2) }}</span>
                        </div>
                        @endif
                        <div class="d-flex align-items-center">
                            <span class="fw-semibold text-gray-600 w-100px">Server:</span>
                            <span class="text-gray-800">{{ $sender->server->name ?? 'Not Assigned' }}</span>
                        </div>
                        <div class="d-flex align-items-center">
                            <span class="fw-semibold text-gray-600 w-100px">Created By:</span>
                            <span class="text-gray-800">{{ $sender->user->name ?? 'Unknown' }}</span>
                        </div>
                        <div class="d-flex align-items-center">
                            <span class="fw-semibold text-gray-600 w-100px">Created:</span>
                            <span class="text-gray-800">{{ $sender->created_at ? $sender->created_at->format('M d, Y H:i') : 'N/A' }}</span>
                        </div>
                        @if($sender->executed_at)
                        <div class="d-flex align-items-center">
                            <span class="fw-semibold text-gray-600 w-100px">Approved:</span>
                            <span class="text-gray-800">{{ $sender->executed_at->format('M d, Y H:i') }}</span>
                        </div>
                        @endif
                    </div>
                    <!--end::Additional Information-->
                </div>
                <!--end::Col-->
            </div>
            <!--end::Row-->

            <!--begin::Documents Section-->
            @if($sender->required_documents && count($sender->required_documents) > 0)
            <div class="separator my-6"></div>
            <div class="row">
                <div class="col-12">
                    <h5 class="fw-bold mb-4">Required Documents</h5>
                    <div class="row g-3">
                        @foreach($sender->required_documents as $document)
                        <div class="col-lg-3 col-md-4 col-sm-6">
                            <!--begin::Document Item-->
                            <div class="card card-flush border border-gray-300 h-100">
                                <div class="card-body text-center p-4">
                                    <!--begin::Icon-->
                                    <div class="mb-3">
                                        @php
                                            $extension = strtolower(pathinfo($document, PATHINFO_EXTENSION));
                                        @endphp
                                        @if($extension === 'pdf')
                                            <i class="ki-duotone ki-file-pdf fs-3x text-danger">
                                                <span class="path1"></span>
                                                <span class="path2"></span>
                                            </i>
                                        @elseif(in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp']))
                                            <i class="ki-duotone ki-picture fs-3x text-primary">
                                                <span class="path1"></span>
                                                <span class="path2"></span>
                                            </i>
                                        @elseif(in_array($extension, ['doc', 'docx']))
                                            <i class="ki-duotone ki-file-doc fs-3x text-info">
                                                <span class="path1"></span>
                                                <span class="path2"></span>
                                            </i>
                                        @elseif(in_array($extension, ['xls', 'xlsx']))
                                            <i class="ki-duotone ki-file-excel fs-3x text-success">
                                                <span class="path1"></span>
                                                <span class="path2"></span>
                                            </i>
                                        @else
                                            <i class="ki-duotone ki-document fs-3x text-gray-600">
                                                <span class="path1"></span>
                                                <span class="path2"></span>
                                            </i>
                                        @endif
                                    </div>
                                    <!--end::Icon-->

                                    <!--begin::File Info-->
                                    <div class="mb-3">
                                        <div class="fw-bold text-gray-800 fs-6 mb-1">{{ basename($document) }}</div>
                                        <div class="text-muted fs-7 text-uppercase">{{ $extension }} File</div>
                                    </div>
                                    <!--end::File Info-->

                                    <!--begin::Download Button-->
                                    <a href="{{ asset('storage/' . $document) }}" target="_blank" class="btn btn-sm btn-light-primary">
                                        <i class="ki-duotone ki-down fs-2">
                                            <span class="path1"></span>
                                            <span class="path2"></span>
                                        </i>
                                        Download
                                    </a>
                                    <!--end::Download Button-->
                                </div>
                            </div>
                            <!--end::Document Item-->
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
            @endif
            <!--end::Documents Section-->

            <!--begin::Approval Section-->
            @if(auth()->user()->hasRole('super-admin') && $sender->status === 'Pending Approved')
            <div class="separator my-6"></div>
            <div class="row">
                <div class="col-12">
                    <h5 class="fw-bold mb-4">Approval Actions</h5>

                    <!--begin::Approval Form-->
                    <div class="row g-4">
                        <!--begin::Server Selection Column-->
                        <div class="col-lg-6 col-md-6 col-12">
                            <div class="fv-row">
                                <label class="form-label fw-semibold mb-2">Assign Server</label>
                                <select id="server_id" name="server_id" class="form-select" data-control="select2" data-placeholder="Select a server...">
                                    <option value="">Select a server...</option>
                                    @foreach($servers as $server)
                                        <option value="{{ $server->id }}" {{ $sender->server_id == $server->id ? 'selected' : '' }}>
                                            {{ $server->name }}
                                        </option>
                                    @endforeach
                                </select>
                                <div class="form-text mt-1">A server must be assigned before approval.</div>
                            </div>
                        </div>
                        <!--end::Server Selection Column-->

                        <!--begin::Approve Button Column (Hidden on mobile)-->
                        <div class="col-lg-6 col-md-6 d-none d-md-block">
                            <div class="fv-row">
                                <label class="form-label fw-semibold mb-2 invisible">Action</label>
                                <div class="d-flex justify-content-md-start justify-content-center">
                                    <button type="button" class="btn btn-success px-6" id="approve-sender-btn" disabled>
                                        <i class="ki-duotone ki-check fs-2 me-2">
                                            <span class="path1"></span>
                                            <span class="path2"></span>
                                        </i>
                                        Approve Sender
                                    </button>
                                </div>
                                <div class="form-text mt-1 invisible">Placeholder</div>
                            </div>
                        </div>
                        <!--end::Approve Button Column-->
                    </div>
                    <!--end::Approval Form-->

                    <!--begin::Mobile Layout (Hidden on larger screens)-->
                    <div class="d-block d-md-none mt-4">
                        <div class="d-grid">
                            <button type="button" class="btn btn-success btn-lg" id="approve-sender-btn-mobile" disabled>
                                <i class="ki-duotone ki-check fs-2 me-2">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                </i>
                                Approve Sender
                            </button>
                        </div>
                    </div>
                    <!--end::Mobile Layout-->
                </div>
            </div>
            @endif
            <!--end::Approval Section-->
        </div>
        <!--end::Card body-->
    </div>
    <!--end::Card-->
</div>
<!--end::Container-->
@endsection

@section('js')
<script>
$(document).ready(function() {
    // Function to update button state based on server selection
    function updateApprovalButtonState() {
        const hasValue = !!$('#server_id').val();
        $('#approve-sender-btn, #approve-sender-btn-mobile').prop('disabled', !hasValue);
    }

    // Initialize button state on page load
    updateApprovalButtonState();

    // Enable/disable approve buttons based on server selection
    $('#server_id').on('change', function() {
        updateApprovalButtonState();
    });

    // Handle sender approval for both desktop and mobile buttons
    $('#approve-sender-btn, #approve-sender-btn-mobile').on('click', function() {
        const serverId = $('#server_id').val();

        if (!serverId) {
            Swal.fire('Error', 'Please select a server before approving.', 'warning');
            return;
        }

        const selectedServerName = $('#server_id option:selected').text();

        Swal.fire({
            title: 'Approve Sender?',
            html: `
                <div class="text-start">
                    <p><strong>Sender:</strong> {{ $sender->name }}</p>
                    <p><strong>Server:</strong> ${selectedServerName}</p>
                    <hr class="my-3">
                    <p class="text-muted">This action will approve the sender and assign it to the selected server.</p>
                </div>
            `,
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Yes, Approve',
            cancelButtonText: 'Cancel',
            confirmButtonColor: '#50cd89',
            cancelButtonColor: '#f1416c'
        }).then((result) => {
            if (result.isConfirmed) {
                const btn = $(this);
                const originalHtml = btn.html();

                // Show loading state
                btn.prop('disabled', true).html('<span class="spinner-border spinner-border-sm me-2"></span>Processing...');

                // Disable both buttons during processing
                $('#approve-sender-btn, #approve-sender-btn-mobile').prop('disabled', true);

                $.ajax({
                    url: '{{ route("senders.approve", $sender->id) }}',
                    type: 'POST',
                    data: {
                        server_id: serverId,
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.success) {
                            Swal.fire({
                                title: 'Success!',
                                text: response.message,
                                icon: 'success',
                                confirmButtonText: 'OK'
                            }).then(() => {
                                window.location.reload();
                            });
                        } else {
                            Swal.fire('Error!', response.message || 'Approval failed.', 'error');
                            resetButtons(originalHtml);
                        }
                    },
                    error: function(xhr) {
                        const message = xhr.responseJSON?.message || 'An error occurred.';
                        Swal.fire('Error!', message, 'error');
                        resetButtons(originalHtml);
                    }
                });
            }
        });
    });

    // Function to reset button states
    function resetButtons(originalHtml) {
        const iconHtml = '<i class="ki-duotone ki-check fs-2 me-2"><span class="path1"></span><span class="path2"></span></i>Approve Sender';
        $('#approve-sender-btn, #approve-sender-btn-mobile').prop('disabled', false).html(iconHtml);
    }

    // Initialize Select2 with enhanced styling
    $('#server_id').select2({
        placeholder: "Select a server...",
        allowClear: true,
        width: '100%'
    });
});
</script>
@endsection
