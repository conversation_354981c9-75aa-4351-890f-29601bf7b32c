<!DOCTYPE html>

<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<!--begin::Head-->
<head>
    <title>{{ config('app.name', 'Laravel') }}</title>
    <meta charset="utf-8" />
    <meta name="description" content="The most advanced Bootstrap 5 Admin Theme with 40 unique prebuilt layouts on Themeforest trusted by 100,000 beginners and professionals. Multi-demo, Dark Mode, RTL support and complete React, Angular, Vue, Asp.Net Core, Rails, Spring, Blazor, Django, Express.js, Node.js, Flask, Symfony & Laravel versions. Grab your copy now and get life-time updates for free." />
    <meta name="keywords" content="metronic, bootstrap, bootstrap 5, angular, VueJs, React, Asp.Net Core, Rails, Spring, Blazor, Django, Express.js, Node.js, Flask, Symfony & Laravel starter kits, admin themes, web design, figma, web development, free templates, free admin themes, bootstrap theme, bootstrap template, bootstrap dashboard, bootstrap dak mode, bootstrap button, bootstrap datepicker, bootstrap timepicker, fullcalendar, datatables, flaticon" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta property="og:locale" content="en_US" />
    <meta property="og:type" content="article" />
    <meta property="og:title" content="Metronic - Bootstrap Admin Template, HTML, VueJS, React, Angular. Laravel, Asp.Net Core, Ruby on Rails, Spring Boot, Blazor, Django, Express.js, Node.js, Flask Admin Dashboard Theme & Template" />
    <meta property="og:url" content="https://keenthemes.com/metronic" />
    <meta property="og:site_name" content="Keenthemes | Metronic" />
    <link rel="canonical" href="https://preview.keenthemes.com/metronic8" />
    <link rel="shortcut icon" href="{{ asset('media/logos/favicon.ico') }}" />
    <!--begin::Fonts(mandatory for all pages)-->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Inter:300,400,500,600,700" />
    <!--end::Fonts-->
    <!--begin::Global Stylesheets Bundle(mandatory for all pages)-->
    <link rel="stylesheet" href="{{ asset('css/style.bundle.css') }}" />
    <link rel="stylesheet" href="{{ asset('plugins/global/plugins.bundle.css') }}" />
    <!--end::Global Stylesheets Bundle-->
    <!--begin::Authentication Stylesheets-->
    <link rel="stylesheet" href="{{ asset('css/auth-futuristic.css') }}" />
    <link rel="stylesheet" href="{{ asset('css/auth-animations.css') }}" />
    <link rel="stylesheet" href="{{ asset('css/auth-responsive.css') }}" />
    <!--end::Authentication Stylesheets-->

</head>
<!--end::Head-->
<!--begin::Body-->
<body id="kt_body" class="auth-bg bgi-size-cover bgi-position-center">
<!--begin::Theme mode setup on page load-->
<script>var defaultThemeMode = "light"; var themeMode; if ( document.documentElement ) { if ( document.documentElement.hasAttribute("data-bs-theme-mode")) { themeMode = document.documentElement.getAttribute("data-bs-theme-mode"); } else { if ( localStorage.getItem("data-bs-theme") !== null ) { themeMode = localStorage.getItem("data-bs-theme"); } else { themeMode = defaultThemeMode; } } if (themeMode === "system") { themeMode = window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light"; } document.documentElement.setAttribute("data-bs-theme", themeMode); }</script>
<!--end::Theme mode setup on page load-->
<!--begin::Main-->
<!--begin::Root-->
<div class="d-flex flex-column flex-root">
    @include('layouts.message')
    <!--begin::Authentication Container-->
    <div class="futuristic-container">
        <div class="stars"></div>
        <div class="circuit-overlay"></div>

        <!-- Data Stream Particles -->
        <div class="data-particles">
            <div class="data-particle"></div>
            <div class="data-particle"></div>
            <div class="data-particle"></div>
            <div class="data-particle"></div>
            <div class="data-particle"></div>
            <div class="data-particle"></div>
        </div>

        <!-- Network Topology Lines -->
        <div class="network-lines">
            <div class="network-line"></div>
            <div class="network-line"></div>
            <div class="network-line"></div>
            <div class="network-line"></div>
        </div>

        <!-- SMS Network Tower -->
        <div class="sms-tower"></div>

        <!-- Signal Waves -->
        <div class="signal-waves">
            <div class="signal-wave"></div>
            <div class="signal-wave"></div>
            <div class="signal-wave"></div>
            <div class="signal-wave"></div>
        </div>

        <!-- Floating Message Bubbles -->
        <div class="message-bubbles">
            <div class="message-bubble">SMS</div>
            <div class="message-bubble">API</div>
            <div class="message-bubble">SEND</div>
            <div class="message-bubble">CONNECT</div>
        </div>

        <!-- Authentication Content -->
        <div class="login-panel">
            <div class="platform-title">{{ config('app.name', 'Application') }}</div>
            @yield('content')
        </div>
    </div>
    <!--end::Authentication Container-->
</div>
<!--end::Root-->
<!--end::Main-->
<!--begin::Javascript-->
<script>var hostUrl = "assets/";</script>
<!--begin::Global Javascript Bundle(mandatory for all pages)-->
<script src="{{ asset('js/scripts.bundle.js') }}"></script>
<script src="{{ asset('plugins/global/plugins.bundle.js') }}"></script>
<!--end::Global Javascript Bundle-->

@if ($errors->any())
    <script>
        $(document).ready(function() {
            Swal.fire({
                icon: 'error',
                title: 'Validation Error',
                html: '{!! implode('<br>', $errors->all()) !!}',
            });
        });
    </script>
@endif
@section('js')
@show
</body>
<!--end::Body-->
</html>
