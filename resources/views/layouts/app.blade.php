<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<!--begin::Head-->
<head>
    <title>{{ config('app.name', 'ShareSpeedy') }}</title>
    <meta charset="utf-8"/>
    <meta name="description"
          content="Connect with a wider audience by using sms.mirajit.net to share your social media posts on Facebook, Twitter, LinkedIn, and beyond."/>
    <meta name="keywords"
          content="Share Speedy, Social media sharing, Web portal, Facebook sharing, Twitter sharing, LinkedIn sharing, Multi-platform sharing, Cross-platform sharing, Social media management, Social media scheduling, Social media automation, Social media marketing, Online marketing, Digital marketing, Content distribution, Content sharing, Brand promotion, Social media strategy, Social media analytics, Social media engagement, Online presence, Online visibility, Social media optimization, Social media tools."/>
    <meta name="viewport" content="width=device-width, initial-scale=1"/>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta property="og:locale" content="en_US"/>
    <meta property="og:type" content="article"/>
    <meta property="og:title" content="Effortlessly Share Social Media Posts with ShareSpeedy"/>
    <meta property="og:url" content="{{url()->full()}}"/>
    <meta property="og:site_name" content="Keenthemes | Metronic"/>
    <link rel="canonical" href="{{url()->full()}}"/>
    <link rel="shortcut icon" href="{{ asset('media/logos/favicon.ico') }}"/>
    <!--begin::Fonts(mandatory for all pages)-->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Inter:300,400,500,600,700"/>
    <!--end::Fonts-->
    <!--begin::Vendor Stylesheets(used for this page only)-->
    <link href="{{ asset('plugins/custom/datatables/datatables.bundle.css') }}" rel="stylesheet" type="text/css"/>
    <!--end::Vendor Stylesheets-->

    <!--begin::Global Stylesheets Bundle(mandatory for all pages)-->
    <link rel="stylesheet" href="{{ asset('css/style.bundle.css') }}"/>
    <link rel="stylesheet" href="{{ asset('plugins/global/plugins.bundle.css') }}"/>
    <link rel="stylesheet" href="{{ asset('plugins/global/plugins.bundle.css') }}"/>
    <link rel="stylesheet" href="{{ asset('css/common.css') }}"/>
    <link rel="stylesheet" href="{{ asset('css/dark-mode-menu.css') }}"/>
    <!--end::Global Stylesheets Bundle-->
    @section('css')
    @show
</head>
<!--end::Head-->
<!--begin::Body-->
<body id="kt_body" class="header-fixed header-tablet-and-mobile-fixed aside-fixed aside-secondary-enabled">
<!--begin::Main-->
<!--begin::Root-->
<div class="d-flex flex-column flex-root">
    <!--begin::Page-->
    <div class="page d-flex flex-row flex-column-fluid">
        <!--begin::Aside-->
        @include('layouts.aside_consolidated')
        <!--end::Aside-->

        <!--begin::Wrapper-->
        <div class="wrapper d-flex flex-column flex-row-fluid" id="kt_wrapper">
            <!--begin::Header-->
            @include('layouts.header')
            <!--end::Header-->
            <!--begin::Content-->
            <div class="content d-flex flex-column flex-column-fluid" id="kt_content">
                <!--begin::Impersonation Banner-->
                @include('components.impersonation-banner')
                <!--end::Impersonation Banner-->
                @yield('content')
            </div>
            <!--end::Content-->
            <!--begin::Footer-->
            @include('layouts.footer')
            <!--end::Footer-->
        </div>
        <!--end::Wrapper-->
    </div>
    <!--end::Page-->
</div>
<!--end::Root-->
<!--end::Main-->
<!--begin::Javascript-->
<script>var hostUrl = "assets/";</script>
<!--begin::Global Javascript Bundle(mandatory for all pages)-->
<script src="{{ asset('js/scripts.bundle.js') }}"></script>
<script src="{{ asset('plugins/global/plugins.bundle.js') }}"></script>
<!--end::Global Javascript Bundle-->
<script src="{{ asset('js/apis.js') }}"></script>
<!--begin::Vendors Javascript(used for this page only)-->
<script src="{{ asset('/plugins/custom/datatables/datatables.bundle.js') }}"></script>
<script src="{{ asset('/js/datatables.js') }}"></script>

@if(isset($popupSettings) && $popupSettings)
<!--begin::Popup Notification Settings-->
<script>
    window.popupSettings = @json($popupSettings);
</script>
<script src="{{ asset('js/popup-notification.js') }}"></script>
<!--end::Popup Notification Settings-->
@endif

<script type="application/javascript">
    @include('layouts.message')
</script>
<!--end::Vendors Javascript-->
@section('js')
@show
</body>
<!--end::Body-->
</html>

