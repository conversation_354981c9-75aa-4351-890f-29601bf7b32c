<div id="kt_aside" class="aside" data-kt-drawer="true" data-kt-drawer-name="aside" data-kt-drawer-activate="{default: true, lg: false}" data-kt-drawer-overlay="true" data-kt-drawer-width="220px" data-kt-drawer-direction="start" data-kt-drawer-toggle="#kt_aside_mobile_toggle">
    <!--begin::Consolidated Sidebar-->
    <div class="aside-secondary d-flex flex-column h-100">
        <!--begin::Logo Section-->
        <div class="aside-logo d-flex flex-column align-items-center py-8 px-5 position-relative" id="kt_aside_logo">
            <!--begin::Mobile Close Button-->
            <button class="btn btn-icon btn-sm btn-active-color-primary position-absolute top-0 end-0 mt-3 me-3 d-lg-none"
                    data-kt-drawer-dismiss="true"
                    title="Close menu"
                    style="min-width: 44px; min-height: 44px;">
                <!--begin::Close Icon-->
                <span class="svg-icon svg-icon-2">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1" transform="rotate(-45 6 17.3137)" fill="currentColor"/>
                        <rect x="7.41422" y="6" width="16" height="2" rx="1" transform="rotate(45 7.41422 6)" fill="currentColor"/>
                    </svg>
                </span>
                <!--end::Close Icon-->
            </button>
            <!--end::Mobile Close Button-->
            <a href="/index" class="d-flex align-items-center">
                <img alt="Logo" src="{{ asset('media/logos/demo7.svg') }}" class="h-40px" />
            </a>
        </div>
        <!--end::Logo Section-->

        <!--begin::Menu Section-->
        <div class="aside-menu flex-column-fluid overflow-auto" style="max-height: calc(100vh - 200px);">
            <!--begin::Menu-->
            @include('layouts.aside_menu')
            <!--end::Menu-->
        </div>
        <!--end::Menu Section-->

        <!--begin::User Profile Footer-->
        <div class="aside-footer d-flex flex-column align-items-center py-4 ps-6 pe-2" id="kt_aside_footer">
            <!--begin::User Menu-->
            <div class="d-flex align-items-center w-100" id="kt_header_user_menu_toggle">
                <!--begin::Menu wrapper-->
                <div class="cursor-pointer d-flex align-items-center w-100 p-3 rounded bg-light-primary" data-kt-menu-trigger="click" data-kt-menu-overflow="true" data-kt-menu-placement="top-start" title="User profile">
                    <!--begin::Avatar-->
                    <div class="symbol symbol-35px me-3">
                        <img src="{{ asset('media/avatars/300-1.jpg') }}" alt="image" />
                    </div>
                    <!--end::Avatar-->
                    <!--begin::User Info-->
                    <div class="flex-grow-1">
                        <div class="fw-bold text-dark fs-7">{{ Auth::user()->name }}</div>
                        <div class="text-muted fs-8">{{ Auth::user()->email }}</div>
                    </div>
                    <!--end::User Info-->
                    <!--begin::Dropdown Icon-->
                    <div class="ms-2">
                        <i class="ki-duotone ki-down fs-6 text-muted">
                            <span class="path1"></span>
                            <span class="path2"></span>
                        </i>
                    </div>
                    <!--end::Dropdown Icon-->
                </div>
                <!--begin::User account menu-->
                @include('layouts.aside_menu_user')
                <!--end::User account menu-->
                <!--end::Menu wrapper-->
            </div>
            <!--end::User Menu-->
        </div>
        <!--end::User Profile Footer-->
    </div>
    <!--end::Consolidated Sidebar-->
</div>

<style>
/* Optimize sidebar menu spacing */
#kt_aside_menu .menu-sub {
    padding-left: 1rem !important;
    padding-right: 0.25rem !important;
}

#kt_aside_menu .menu-sub .menu-link {
    padding-left: 1.5rem !important;
    padding-right: 0.5rem !important;
}

/* Ensure proper alignment for menu icons and text */
#kt_aside_menu .menu-link {
    padding-right: 0.5rem !important;
}

/* Optimize user profile section alignment */
.aside-footer .w-100 {
    margin-right: 0 !important;
}

/* Compact vertical spacing for menu items */
#kt_aside_menu .menu-item {
    margin-bottom: 0.25rem !important;
}

#kt_aside_menu .menu-link {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
    min-height: auto !important;
    line-height: 1.2 !important;
}

#kt_aside_menu .menu-sub .menu-item {
    margin-bottom: 0.125rem !important;
}

#kt_aside_menu .menu-sub .menu-link {
    padding-top: 0.375rem !important;
    padding-bottom: 0.375rem !important;
    min-height: auto !important;
}

/* Optimize menu icon size to match restored text size */
#kt_aside_menu .menu-icon {
    width: 1.5rem !important;
    height: 1.5rem !important;
    margin-right: 0.75rem !important;
}

#kt_aside_menu .menu-icon img {
    width: 1.5rem !important;
    height: 1.5rem !important;
}

/* Restore original menu title font sizes for readability */
#kt_aside_menu .menu-title {
    font-weight: 600 !important;
}

#kt_aside_menu .menu-sub .menu-title {
    font-weight: 500 !important;
}

/* Mobile drawer close button styling */
@media (max-width: 991.98px) {
    .aside-logo .btn[data-kt-drawer-dismiss] {
        background-color: rgba(255, 255, 255, 0.1) !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        color: #6c757d !important;
        transition: all 0.3s ease !important;
    }

    .aside-logo .btn[data-kt-drawer-dismiss]:hover {
        background-color: rgba(255, 255, 255, 0.2) !important;
        color: #495057 !important;
        transform: scale(1.05) !important;
    }

    .aside-logo .btn[data-kt-drawer-dismiss]:active {
        transform: scale(0.95) !important;
    }

    /* Ensure adequate touch target size */
    .aside-logo .btn[data-kt-drawer-dismiss] {
        min-width: 44px !important;
        min-height: 44px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }
}
</style>
