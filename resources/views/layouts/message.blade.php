@if (Session::has('success') || Session::has('error') || Session::has('warning') || Session::has('info') || $errors->any())
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Check if SweetAlert2 is available, fallback to toastr if available
        if (typeof Swal !== 'undefined') {
            @if ($message = Session::get('success'))
                Swal.fire({
                    text: '{{ addslashes($message) }}',
                    icon: 'success',
                    buttonsStyling: false,
                    confirmButtonText: 'Ok, got it!',
                    customClass: {
                        confirmButton: 'btn btn-primary'
                    },
                    timer: 3000,
                    timerProgressBar: true
                });
            @endif
            @if ($message = Session::get('error'))
                Swal.fire({
                    text: '{{ addslashes($message) }}',
                    icon: 'error',
                    buttonsStyling: false,
                    confirmButtonText: 'Ok, got it!',
                    customClass: {
                        confirmButton: 'btn btn-primary'
                    }
                });
            @endif
            @if ($message = Session::get('warning'))
                Swal.fire({
                    text: '{{ addslashes($message) }}',
                    icon: 'warning',
                    buttonsStyling: false,
                    confirmButtonText: 'Ok, got it!',
                    customClass: {
                        confirmButton: 'btn btn-primary'
                    }
                });
            @endif
            @if ($message = Session::get('info'))
                Swal.fire({
                    text: '{{ addslashes($message) }}',
                    icon: 'info',
                    buttonsStyling: false,
                    confirmButtonText: 'Ok, got it!',
                    customClass: {
                        confirmButton: 'btn btn-primary'
                    }
                });
            @endif
            @if ($errors->any())
                Swal.fire({
                    text: '{{ addslashes($errors->first()) }}',
                    icon: 'error',
                    buttonsStyling: false,
                    confirmButtonText: 'Ok, got it!',
                    customClass: {
                        confirmButton: 'btn btn-primary'
                    }
                });
            @endif
        } else if (typeof toastr !== 'undefined') {
            // Fallback to toastr if SweetAlert2 is not available
            @if ($message = Session::get('success'))
                toastr.success('{{ addslashes($message) }}');
            @endif
            @if ($message = Session::get('error'))
                toastr.error('{{ addslashes($message) }}');
            @endif
            @if ($message = Session::get('warning'))
                toastr.warning('{{ addslashes($message) }}');
            @endif
            @if ($message = Session::get('info'))
                toastr.info('{{ addslashes($message) }}');
            @endif
            @if ($errors->any())
                toastr.error('{{ addslashes($errors->first()) }}');
            @endif
        } else {
            // Final fallback to console.log
            @if ($message = Session::get('success'))
                console.log('Success: {{ addslashes($message) }}');
            @endif
            @if ($message = Session::get('error'))
                console.log('Error: {{ addslashes($message) }}');
            @endif
            @if ($message = Session::get('warning'))
                console.log('Warning: {{ addslashes($message) }}');
            @endif
            @if ($message = Session::get('info'))
                console.log('Info: {{ addslashes($message) }}');
            @endif
            @if ($errors->any())
                console.log('Error: {{ addslashes($errors->first()) }}');
            @endif
        }
    });
</script>
@endif
