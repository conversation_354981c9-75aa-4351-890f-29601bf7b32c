<!--begin::Modal - Add task-->
<div class="modal fade" id="kt_modal_add_user" tabindex="-1" aria-hidden="true">
    <!--begin::Modal dialog-->
    <div class="modal-dialog modal-dialog-centered mw-650px">
        <!--begin::Modal content-->
        <div class="modal-content">
            <!--begin::Modal header-->
            <div class="modal-header" id="kt_modal_add_user_header">
                <!--begin::Modal title-->
                <h2 class="fw-bold">Add New User</h2>
                <!--end::Modal title-->
                <!--begin::Close-->
                <div class="btn btn-icon btn-sm btn-active-icon-primary" data-kt-users-modal-action="close">
                    <!--begin::Svg Icon | path: icons/duotune/arrows/arr061.svg-->
                    <span class="svg-icon svg-icon-1">
                        <svg width="24" height="24" viewBox="0 0 24 24"
                             fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect opacity="0.5" x="6" y="17.3137" width="16"
                                  height="2" rx="1"
                                  transform="rotate(-45 6 17.3137)"
                                  fill="currentColor"/>
                            <rect x="7.41422" y="6" width="16" height="2" rx="1"
                                  transform="rotate(45 7.41422 6)"
                                  fill="currentColor"/>
                        </svg>
                    </span>
                    <!--end::Svg Icon-->
                </div>
                <!--end::Close-->
            </div>
            <!--end::Modal header-->
            <!--begin::Modal body-->
            <div class="modal-body scroll-y mx-5 mx-xl-15 my-7">
                <!--begin::Form-->
                <form id="kt_modal_add_user_form" class="form" method="post" action="{{route('users.store')}}">
                    @csrf
                    <!--begin::Scroll-->
                    <div class="d-flex flex-column scroll-y me-n7 pe-7" id="kt_modal_add_user_scroll"
                         data-kt-scroll="true" data-kt-scroll-activate="{default: false, lg: true}"
                         data-kt-scroll-max-height="auto" data-kt-scroll-dependencies="#kt_modal_add_user_header"
                         data-kt-scroll-wrappers="#kt_modal_add_user_scroll" data-kt-scroll-offset="300px">

                        <div class="row">
                            <!--begin::Col-->
                            <div class="col-md-6 fv-row mb-7">
                                <!--begin::Label-->
                                <label class="required fs-6 fw-semibold form-label mb-2">Client Type</label>
                                <!--end::Label-->
                                <!--begin::Row-->
                                <div class="row fv-row fv-plugins-icon-container">
                                    <select name="role_id" class="form-select form-select-solid">
                                        <option value="">Select</option>
                                        @foreach($roles as $role => $value)
                                            <option value="{{$role}}">{{$value}}</option>
                                        @endforeach
                                    </select>
                                    <div class="fv-plugins-message-container invalid-feedback"></div>
                                </div>
                                <!--end::Row-->
                            </div>
                            <!--end::Col-->
                            <!--begin::Col-->
                            <div class="col-md-6 fv-row mb-7">
                                <!--begin::Label-->
                                <label class="required fw-semibold fs-6 mb-2">Full Name</label>
                                <!--end::Label-->
                                <!--begin::Input-->
                                <input type="text" name="name" class="form-control form-control-solid mb-3 mb-lg-0"
                                       placeholder="Full name" value="{{old('name')}}"/>
                                <!--end::Input-->
                            </div>
                        </div>
                        <!--end::Input group-->

                        <div class="row">
                            <!--begin::Col-->
                            <div class="col-md-6 fv-row mb-7">
                                <!--begin::Label-->
                                <label class="required fw-semibold fs-6 mb-2">Email</label>
                                <!--end::Label-->
                                <!--begin::Input-->
                                <input type="email" name="email" class="form-control form-control-solid mb-3 mb-lg-0"
                                       placeholder="<EMAIL>" value="{{old('email')}}"/>
                                <!--end::Input-->
                            </div>
                            <!--end::Col-->
                            <!--begin::Col-->
                            <div class="col-md-6 fv-row mb-7">
                                <!--begin::Label-->
                                <label class="required fw-semibold fs-6 mb-2">Mobile No</label>
                                <!--end::Label-->
                                <!--begin::Input-->
                                <input type="text" name="phone" class="form-control form-control-solid mb-3 mb-lg-0"
                                       placeholder="8801617703137" value="{{old('phone')}}"/>
                                <!--end::Input-->
                            </div>
                        </div>

                        <div class="row">
                            <!--begin::Col-->
                            <div class="col-md-6 fv-row mb-7">
                                <!--begin::Label-->
                                <label class="required fw-semibold fs-6 mb-2">Allot SMS Balance</label>
                                <!--end::Label-->
                                <!--begin::Input-->
                                <input type="number" name="balance" class="form-control form-control-solid mb-3 mb-lg-0"
                                       placeholder="0" value="{{old('balance')}}"/>
                                <!--end::Input-->
                            </div>
                            <!--end::Col-->
                            <!--begin::Col-->
                            <div class="col-md-6 fv-row mb-7">
                                <!--begin::Label-->
                                <label class="required fw-semibold fs-6 mb-2">Select Validity</label>
                                <!--end::Label-->
                                <!--begin::Row-->
                                <div class="row fv-row fv-plugins-icon-container">
                                    <select required name="validity" class="form-select form-select-solid">
                                        <option value="">Select duration</option>
                                        <option value="1">1 Month</option>
                                        <option value="2">2 Month</option>
                                        <option value="3">3 Month</option>
                                        <option value="4">4 Month</option>
                                        <option value="5">5 Month</option>
                                        <option value="6">6 Month</option>
                                        <option value="12">1 year</option>
                                        <option value="24">2 year</option>
                                        <option value="60">Unlimited</option>
                                    </select>
                                    <div class="fv-plugins-message-container invalid-feedback"></div>
                                </div>
                                <!--end::Row-->
                            </div>
                        </div>

                        <div class="row">
                            <!--begin::Col-->
                            <div class="col-md-6 fv-row mb-7">
                                <!--begin::Label-->
                                <label class="required fw-semibold fs-6 mb-2">Profit Margin</label>
                                <!--end::Label-->
                                <!--begin::Input-->
                                <input type="number" name="profit_margin"
                                       class="form-control form-control-solid mb-3 mb-lg-0" placeholder="0"
                                       value="{{old('profit_margin')}}"/>
                                <!--end::Input-->
                            </div>
                            <!--end::Col-->
                            <!--begin::Col-->
                            <div class="col-md-6 fv-row mb-7">
                                <!--begin::Label-->
                                <label class="required fw-semibold fs-6 mb-2">Margin Type</label>
                                <!--end::Label-->
                                <!--begin::Row-->
                                <div class="row fv-row fv-plugins-icon-container">
                                    <select name="margin_type" class="form-select form-select-solid">
                                        <option value="1">Percentage(%)</option>
                                        <option value="2">Fixed</option>
                                    </select>
                                    <div class="fv-plugins-message-container invalid-feedback"></div>
                                </div>
                                <!--end::Row-->
                            </div>
                        </div>

                        <div class="row">
                            <!--begin::Col-->
                            <div class="col-md-6 fv-row mb-7">
                                <!--begin::Label-->
                                <label class="required fw-semibold fs-6 mb-2">Minimum Recharge</label>
                                <!--end::Label-->
                                <!--begin::Input-->
                                <input type="number" name="minimum_recharge"
                                       class="form-control form-control-solid mb-3 mb-lg-0" placeholder="0"
                                       value="{{old('minimum_recharge')}}"/>
                                <!--end::Input-->
                            </div>
                            <!--end::Col-->
                            <!--begin::Col-->


                            <!--begin::Col-->
                            <div class="col-md-6 fv-row mb-7">
                                <!--begin::Label-->
                                <label class="required fw-semibold fs-6 mb-2">Gateway fee</label>
                                <!--end::Label-->
                                <!--begin::Row-->
                                <!--begin::Input-->
                                <input type="number" name="gateway_fee"
                                       class="form-control form-control-solid mb-3 mb-lg-0" placeholder="0"
                                       value="{{old('gateway_fee')}}" required/>
                                <!--end::Input-->
                                <!--end::Row-->
                            </div>
                        </div>

                        <div class="row">
                            <!--begin::Col-->
                            <div class="col-md-12 fv-row mb-7">
                                <!--begin::Label-->
                                <label class="required fw-semibold fs-6 mb-2">Remarks</label>
                                <!--end::Label-->
                                <!--begin::Row-->
                                <!--begin::Input-->
                                <input type="text" name="remarks" class="form-control form-control-solid mb-3 mb-lg-0"
                                       placeholder="" value="{{old('remarks')}}"/>
                                <!--end::Input-->
                                <!--end::Row-->
                            </div>
                        </div>
                        <!--end::Input group-->
                    </div>
                    <!--end::Scroll-->
                    <!--begin::Actions-->
                    <div class="text-center pt-15">
                        <button type="reset" class="btn btn-light me-3" data-kt-users-modal-action="cancel">Cancel
                        </button>
                        <button type="submit" class="btn btn-primary" data-kt-users-modal-action="submit">
                            <span class="indicator-label">Create User</span>
                            <span class="indicator-progress">Please wait...
							    <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                            </span>
                        </button>
                    </div>
                    <!--end::Actions-->
                </form>
                <!--end::Form-->
            </div>
            <!--end::Modal body-->
        </div>
        <!--end::Modal content-->
    </div>
    <!--end::Modal dialog-->
</div>
<!--end::Modal - Add task-->
