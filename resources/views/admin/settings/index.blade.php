@extends('layouts.app')

@section('title', '<PERSON><PERSON>')

@section('css')
<style>
.required::after {
    content: " *";
    color: #f1416c;
    font-weight: bold;
}

.form-control.is-invalid {
    border-color: #f1416c;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23f1416c'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 1.4 1.4M7.2 4.6l-1.4 1.4'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.character-counter {
    font-size: 0.875rem;
    color: #7e8299;
}

.character-counter.warning {
    color: #ffc700;
}

.character-counter.danger {
    color: #f1416c;
}

.file-preview {
    max-width: 200px;
    max-height: 200px;
    border: 1px solid #e1e3ea;
    border-radius: 0.475rem;
    padding: 0.5rem;
    margin-top: 0.5rem;
}

.file-preview img {
    max-width: 100%;
    height: auto;
    border-radius: 0.375rem;
}

.file-upload-area {
    border: 2px dashed #e1e3ea;
    border-radius: 0.475rem;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.file-upload-area:hover {
    border-color: #009ef7;
    background-color: #f8faff;
}

.file-upload-area.dragover {
    border-color: #009ef7;
    background-color: #f8faff;
}
</style>
@endsection

@section('content')
<!--begin::Content wrapper-->
<div class="d-flex flex-column flex-column-fluid">
    <!--begin::Toolbar-->
    <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
        <!--begin::Toolbar container-->
        <div id="kt_app_toolbar_container" class="app-container container-xxl d-flex flex-stack">
            <!--begin::Page title-->
            <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                <!--begin::Title-->
                <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">Settings</h1>
                <!--end::Title-->
                <!--begin::Breadcrumb-->
                <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                    <!--begin::Item-->
                    <li class="breadcrumb-item text-muted">
                        <a href="{{route('admin.dashboard')}}" class="text-muted text-hover-primary">Admin</a>
                    </li>
                    <!--end::Item-->
                    <!--begin::Item-->
                    <li class="breadcrumb-item">
                        <span class="bullet bg-gray-400 w-5px h-2px"></span>
                    </li>
                    <!--end::Item-->
                    <!--begin::Item-->
                    <li class="breadcrumb-item text-muted">Settings</li>
                    <!--end::Item-->
                </ul>
                <!--end::Breadcrumb-->
            </div>
            <!--end::Page title-->
        </div>
        <!--end::Toolbar container-->
    </div>
    <!--end::Toolbar-->

    <!--begin::Content-->
    <div id="kt_app_content" class="app-content flex-column-fluid">
        <!--begin::Content container-->
        <div id="kt_app_content_container" class="app-container container-xxl">
            <!--begin::Card-->
            <div class="card card-custom">
                <!--begin::Card header-->
                <div class="card-header">
                    <!--begin::Card title-->
                    <div class="card-title">
                        <h3 class="fw-bold m-0">Application Settings</h3>
                    </div>
                    <!--end::Card title-->
                </div>
                <!--end::Card header-->

                <!--begin::Card body-->
                <div class="card-body">
                    <!--begin::Nav tabs-->
                    <ul class="nav nav-tabs nav-line-tabs nav-line-tabs-2x mb-5 fs-6">
                        <li class="nav-item">
                            <a class="nav-link active" data-bs-toggle="tab" href="#kt_tab_pane_website">
                                <span class="nav-icon">
                                    <i class="ki-duotone ki-home fs-2">
                                        <span class="path1"></span>
                                        <span class="path2"></span>
                                    </i>
                                </span>
                                <span class="nav-text">Website Settings</span>
                            </a>
                        </li>
                        @if(Auth::user()->hasRole('super-admin'))
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#kt_tab_pane_bulk_pricing">
                                <span class="nav-icon">
                                    <i class="ki-duotone ki-price-tag fs-2">
                                        <span class="path1"></span>
                                        <span class="path2"></span>
                                        <span class="path3"></span>
                                    </i>
                                </span>
                                <span class="nav-text">Bulk Pricing Management</span>
                            </a>
                        </li>
                        @endif
                        @if(!Auth::user()->hasRole('client'))
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#kt_tab_pane_popup_settings">
                                <span class="nav-icon">
                                    <i class="ki-duotone ki-notification-bing fs-2">
                                        <span class="path1"></span>
                                        <span class="path2"></span>
                                        <span class="path3"></span>
                                    </i>
                                </span>
                                <span class="nav-text">Pop-up Settings</span>
                            </a>
                        </li>
                        @endif
                        <!-- Future tabs can be added here -->
                    </ul>
                    <!--end::Nav tabs-->

                    <!--begin::Tab content-->
                    <div class="tab-content" id="myTabContent">
                        <!--begin::Tab pane - Website Settings-->
                        <div class="tab-pane fade show active" id="kt_tab_pane_website" role="tabpanel">
                            <form id="kt_settings_form" method="POST" action="{{ route('admin.settings.update') }}" enctype="multipart/form-data">
                                @csrf
                                
                                <!--begin::Website Identity Section-->
                                <div class="card card-custom mb-8">
                                    <div class="card-header">
                                        <div class="card-title">
                                            <h3 class="fw-bold m-0">Website Identity</h3>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <!--begin::Row-->
                                        <div class="row">
                                            <!--begin::Col-->
                                            <div class="col-lg-6">
                                                <!--begin::Website Title-->
                                                <div class="fv-row mb-7">
                                                    <label class="fw-semibold fs-6 mb-2">Website Title</label>
                                                    <input type="text" name="website_title" class="form-control form-control-solid" 
                                                           placeholder="Enter website title" 
                                                           value="{{ old('website_title', $settings['website_title'] ?? '') }}"
                                                           maxlength="255" />
                                                    <div class="form-text">The title that appears in browser tabs and search results</div>
                                                    @error('website_title')
                                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                                <!--end::Website Title-->
                                            </div>
                                            <!--end::Col-->
                                        </div>
                                        <!--end::Row-->

                                        <!--begin::Row-->
                                        <div class="row">
                                            <!--begin::Col-->
                                            <div class="col-lg-6">
                                                <!--begin::Website Logo-->
                                                <div class="fv-row mb-7">
                                                    <label class="fw-semibold fs-6 mb-2">Website Logo</label>
                                                    <div class="file-upload-area" id="logo-upload-area">
                                                        <input type="file" name="website_logo" id="website_logo" class="d-none" accept=".jpg,.jpeg,.png,.svg" />
                                                        <div class="upload-content">
                                                            <i class="ki-duotone ki-file-up fs-3x text-primary mb-3">
                                                                <span class="path1"></span>
                                                                <span class="path2"></span>
                                                            </i>
                                                            <div class="fw-bold fs-6 text-gray-900 mb-2">Click to upload or drag and drop</div>
                                                            <div class="fs-7 text-gray-500">JPG, JPEG, PNG, SVG (Max: 2MB)</div>
                                                        </div>
                                                    </div>
                                                    @if(isset($settings['website_logo']) && $settings['website_logo'])
                                                        <div class="file-preview mt-3 show current-logo-preview" id="current-logo-preview">
                                                            <div class="d-flex align-items-center justify-content-between">
                                                                <img src="{{ asset('storage/' . $settings['website_logo']) }}" alt="Current Logo" class="me-3">
                                                                <button type="button" class="btn btn-sm btn-light-danger" onclick="deleteFile('website_logo')">
                                                                    <i class="ki-duotone ki-trash fs-6"></i>
                                                                    Delete
                                                                </button>
                                                            </div>
                                                        </div>
                                                    @endif
                                                    <div id="logo-preview" class="file-preview"></div>
                                                    @error('website_logo')
                                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                                <!--end::Website Logo-->
                                            </div>
                                            <!--end::Col-->

                                            <!--begin::Col-->
                                            <div class="col-lg-6">
                                                <!--begin::Website Favicon-->
                                                <div class="fv-row mb-7">
                                                    <label class="fw-semibold fs-6 mb-2">Website Favicon</label>
                                                    <div class="file-upload-area" id="favicon-upload-area">
                                                        <input type="file" name="website_favicon" id="website_favicon" class="d-none" accept=".ico,.png" />
                                                        <div class="upload-content">
                                                            <i class="ki-duotone ki-file-up fs-3x text-primary mb-3">
                                                                <span class="path1"></span>
                                                                <span class="path2"></span>
                                                            </i>
                                                            <div class="fw-bold fs-6 text-gray-900 mb-2">Click to upload or drag and drop</div>
                                                            <div class="fs-7 text-gray-500">ICO, PNG (Max: 512KB, 16x16 to 128x128px)</div>
                                                        </div>
                                                    </div>
                                                    @if(isset($settings['website_favicon']) && $settings['website_favicon'])
                                                        <div class="file-preview mt-3 show current-favicon-preview" id="current-favicon-preview">
                                                            <div class="d-flex align-items-center justify-content-between">
                                                                <img src="{{ asset('storage/' . $settings['website_favicon']) }}" alt="Current Favicon" class="me-3">
                                                                <button type="button" class="btn btn-sm btn-light-danger" onclick="deleteFile('website_favicon')">
                                                                    <i class="ki-duotone ki-trash fs-6"></i>
                                                                    Delete
                                                                </button>
                                                            </div>
                                                        </div>
                                                    @endif
                                                    <div id="favicon-preview" class="file-preview"></div>
                                                    @error('website_favicon')
                                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                                <!--end::Website Favicon-->
                                            </div>
                                            <!--end::Col-->
                                        </div>
                                        <!--end::Row-->
                                    </div>
                                </div>
                                <!--end::Website Identity Section-->

                                <!--begin::SEO Settings Section-->
                                <div class="card card-custom mb-8">
                                    <div class="card-header">
                                        <div class="card-title">
                                            <h3 class="fw-bold m-0">SEO Settings</h3>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <!--begin::Row-->
                                        <div class="row">
                                            <!--begin::Col-->
                                            <div class="col-lg-12">
                                                <!--begin::Meta Description-->
                                                <div class="fv-row mb-7">
                                                    <label class="fw-semibold fs-6 mb-2">Meta Description</label>
                                                    <textarea name="meta_description" class="form-control form-control-solid" 
                                                              rows="3" placeholder="Enter meta description for search engines"
                                                              maxlength="160" id="meta_description">{{ old('meta_description', $settings['meta_description'] ?? '') }}</textarea>
                                                    <div class="d-flex justify-content-between">
                                                        <div class="form-text">Brief description for search engine results</div>
                                                        <div class="character-counter" id="meta_description_counter">0/160</div>
                                                    </div>
                                                    @error('meta_description')
                                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                                <!--end::Meta Description-->
                                            </div>
                                            <!--end::Col-->
                                        </div>
                                        <!--end::Row-->

                                        <!--begin::Row-->
                                        <div class="row">
                                            <!--begin::Col-->
                                            <div class="col-lg-12">
                                                <!--begin::Meta Keywords-->
                                                <div class="fv-row mb-7">
                                                    <label class="fw-semibold fs-6 mb-2">Meta Keywords</label>
                                                    <input type="text" name="meta_keywords" class="form-control form-control-solid" 
                                                           placeholder="Enter keywords separated by commas" 
                                                           value="{{ old('meta_keywords', $settings['meta_keywords'] ?? '') }}"
                                                           maxlength="500" />
                                                    <div class="form-text">Comma-separated keywords relevant to your website</div>
                                                    @error('meta_keywords')
                                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                                <!--end::Meta Keywords-->
                                            </div>
                                            <!--end::Col-->
                                        </div>
                                        <!--end::Row-->
                                    </div>
                                </div>
                                <!--end::SEO Settings Section-->

                                <!--begin::Social Media & Analytics Section-->
                                <div class="card card-custom mb-8">
                                    <div class="card-header">
                                        <div class="card-title">
                                            <h3 class="fw-bold m-0">Social Media & Analytics</h3>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <!--begin::Row-->
                                        <div class="row">
                                            <!--begin::Col-->
                                            <div class="col-lg-6">
                                                <!--begin::Open Graph Title-->
                                                <div class="fv-row mb-7">
                                                    <label class="fw-semibold fs-6 mb-2">Open Graph Title</label>
                                                    <input type="text" name="og_title" class="form-control form-control-solid"
                                                           placeholder="Enter title for social media sharing"
                                                           value="{{ old('og_title', $settings['og_title'] ?? '') }}"
                                                           maxlength="60" id="og_title" />
                                                    <div class="d-flex justify-content-between">
                                                        <div class="form-text">Title when shared on social media</div>
                                                        <div class="character-counter" id="og_title_counter">0/60</div>
                                                    </div>
                                                    @error('og_title')
                                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                                <!--end::Open Graph Title-->
                                            </div>
                                            <!--end::Col-->

                                            <!--begin::Col-->
                                            <div class="col-lg-6">
                                                <!--begin::Google Analytics ID-->
                                                <div class="fv-row mb-7">
                                                    <label class="fw-semibold fs-6 mb-2">Google Analytics ID</label>
                                                    <input type="text" name="google_analytics_id" class="form-control form-control-solid"
                                                           placeholder="G-XXXXXXXXXX or UA-XXXXXXXX-X"
                                                           value="{{ old('google_analytics_id', $settings['google_analytics_id'] ?? '') }}"
                                                           maxlength="50" />
                                                    <div class="form-text">Your Google Analytics tracking ID</div>
                                                    @error('google_analytics_id')
                                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                                <!--end::Google Analytics ID-->
                                            </div>
                                            <!--end::Col-->
                                        </div>
                                        <!--end::Row-->

                                        <!--begin::Row-->
                                        <div class="row">
                                            <!--begin::Col-->
                                            <div class="col-lg-12">
                                                <!--begin::Open Graph Description-->
                                                <div class="fv-row mb-7">
                                                    <label class="fw-semibold fs-6 mb-2">Open Graph Description</label>
                                                    <textarea name="og_description" class="form-control form-control-solid"
                                                              rows="3" placeholder="Enter description for social media sharing"
                                                              maxlength="160" id="og_description">{{ old('og_description', $settings['og_description'] ?? '') }}</textarea>
                                                    <div class="d-flex justify-content-between">
                                                        <div class="form-text">Description when shared on social media</div>
                                                        <div class="character-counter" id="og_description_counter">0/160</div>
                                                    </div>
                                                    @error('og_description')
                                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                                <!--end::Open Graph Description-->
                                            </div>
                                            <!--end::Col-->
                                        </div>
                                        <!--end::Row-->
                                    </div>
                                </div>
                                <!--end::Social Media & Analytics Section-->

                                <!--begin::Advanced Settings Section-->
                                <div class="card card-custom mb-8">
                                    <div class="card-header">
                                        <div class="card-title">
                                            <h3 class="fw-bold m-0">Advanced Settings</h3>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <!--begin::Row-->
                                        <div class="row">
                                            <!--begin::Col-->
                                            <div class="col-lg-12">
                                                <!--begin::Robots.txt-->
                                                <div class="fv-row mb-7">
                                                    <label class="fw-semibold fs-6 mb-2">Robots.txt Content</label>
                                                    <textarea name="robots_txt" class="form-control form-control-solid"
                                                              rows="8" placeholder="Enter robots.txt directives"
                                                              maxlength="5000" id="robots_txt">{{ old('robots_txt', $settings['robots_txt'] ?? '') }}</textarea>
                                                    <div class="d-flex justify-content-between">
                                                        <div class="form-text">Instructions for search engine crawlers</div>
                                                        <div class="character-counter" id="robots_txt_counter">0/5000</div>
                                                    </div>
                                                    @error('robots_txt')
                                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                                <!--end::Robots.txt-->
                                            </div>
                                            <!--end::Col-->
                                        </div>
                                        <!--end::Row-->
                                    </div>
                                </div>
                                <!--end::Advanced Settings Section-->

                                <!--begin::Form Actions-->
                                <div class="d-flex justify-content-end">
                                    <button type="button" class="btn btn-light me-3" id="reset-form">
                                        <i class="ki-duotone ki-arrow-left fs-2">
                                            <span class="path1"></span>
                                            <span class="path2"></span>
                                        </i>
                                        Reset
                                    </button>
                                    <button type="submit" class="btn btn-primary" id="submit-settings">
                                        <span class="indicator-label">
                                            <i class="ki-duotone ki-check fs-2">
                                                <span class="path1"></span>
                                                <span class="path2"></span>
                                            </i>
                                            Save Settings
                                        </span>
                                        <span class="indicator-progress">
                                            Please wait... <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                                        </span>
                                    </button>
                                </div>
                                <!--end::Form Actions-->
                            </form>
                        </div>
                        <!--end::Tab pane - Website Settings-->

                        @if(Auth::user()->hasRole('super-admin'))
                        <!--begin::Tab pane - Bulk Pricing Management-->
                        <div class="tab-pane fade" id="kt_tab_pane_bulk_pricing" role="tabpanel">
                            <!--begin::Bulk Pricing Form-->
                            <form id="kt_bulk_pricing_form">
                                @csrf

                                <!--begin::Form Controls Section-->
                                <div id="formControlsSection">
                                    <!--begin::Filter Section-->
                                <div class="card card-custom mb-8">
                                    <div class="card-header">
                                        <div class="card-title">
                                            <h3 class="fw-bold m-0">Target Selection</h3>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <!--begin::Row-->
                                        <div class="row">
                                            <!--begin::Col-->
                                            <div class="col-lg-6">
                                                <!--begin::Role Filter-->
                                                <div class="fv-row mb-7">
                                                    <label class="fw-semibold fs-6 mb-2 required">Target Role</label>
                                                    <select name="target_role" id="targetRoleFilter" class="form-select form-select-solid" required>
                                                        <option value="">Select Target Role</option>
                                                        <option value="all">All Roles</option>
                                                        <option value="master-reseller">Master Reseller</option>
                                                        <option value="reseller">Reseller</option>
                                                        <option value="client">Client</option>
                                                    </select>
                                                    <div class="form-text">Select which user roles to apply pricing changes to</div>
                                                    <div class="invalid-feedback"></div>
                                                </div>
                                                <!--end::Role Filter-->
                                            </div>
                                            <!--end::Col-->

                                            <!--begin::Col-->
                                            <div class="col-lg-6">
                                                <!--begin::Account Filter-->
                                                <div class="fv-row mb-7">
                                                    <label class="fw-semibold fs-6 mb-2 required">Target Account</label>
                                                    <select name="target_account" id="targetAccountFilter" class="form-select form-select-solid" required>
                                                        <option value="">Select Target Account</option>
                                                        <option value="all">All Accounts</option>
                                                    </select>
                                                    <div class="form-text">Select which accounts to apply pricing changes to</div>
                                                    <div class="invalid-feedback"></div>
                                                </div>
                                                <!--end::Account Filter-->
                                            </div>
                                            <!--end::Col-->
                                        </div>
                                        <!--end::Row-->
                                    </div>
                                </div>
                                <!--end::Filter Section-->

                                <!--begin::Pricing Update Section-->
                                <div class="card card-custom mb-8">
                                    <div class="card-header">
                                        <div class="card-title">
                                            <h3 class="fw-bold m-0">Pricing Adjustment</h3>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <!--begin::Row-->
                                        <div class="row">
                                            <!--begin::Col-->
                                            <div class="col-lg-4">
                                                <!--begin::Adjustment Type-->
                                                <div class="fv-row mb-7">
                                                    <label class="fw-semibold fs-6 mb-2 required">Adjustment Type</label>
                                                    <select name="adjustment_type" id="adjustmentType" class="form-select form-select-solid" required>
                                                        <option value="">Select Type</option>
                                                        <option value="percentage">Percentage (%)</option>
                                                        <option value="fixed">Fixed Amount</option>
                                                    </select>
                                                    <div class="form-text">Choose between percentage or fixed amount adjustment</div>
                                                    <div class="invalid-feedback"></div>
                                                </div>
                                                <!--end::Adjustment Type-->
                                            </div>
                                            <!--end::Col-->

                                            <!--begin::Col-->
                                            <div class="col-lg-4">
                                                <!--begin::Adjustment Value-->
                                                <div class="fv-row mb-7">
                                                    <label class="fw-semibold fs-6 mb-2 required">Adjustment Value</label>
                                                    <div class="input-group">
                                                        <input type="number" name="adjustment_value" id="adjustmentValue"
                                                               class="form-control form-control-solid"
                                                               placeholder="Enter value"
                                                               step="0.01"
                                                               min="-100"
                                                               max="1000"
                                                               required />
                                                        <span class="input-group-text" id="adjustment-unit">%</span>
                                                    </div>
                                                    <div class="form-text">Enter positive value to increase, negative to decrease</div>
                                                    <div class="invalid-feedback"></div>
                                                </div>
                                                <!--end::Adjustment Value-->
                                            </div>
                                            <!--end::Col-->

                                            <!--begin::Col-->
                                            <div class="col-lg-4">
                                                <!--begin::SMS Type-->
                                                <div class="fv-row mb-7">
                                                    <label class="fw-semibold fs-6 mb-2 required">SMS Type</label>
                                                    <select name="sms_type" id="smsType" class="form-select form-select-solid" required>
                                                        <option value="">Select SMS Type</option>
                                                        <option value="both">Both (Masking & Non-Masking)</option>
                                                        <option value="masking">Masking SMS Only</option>
                                                        <option value="non_masking">Non-Masking SMS Only</option>
                                                    </select>
                                                    <div class="form-text">Select which SMS types to update</div>
                                                    <div class="invalid-feedback"></div>
                                                </div>
                                                <!--end::SMS Type-->
                                            </div>
                                            <!--end::Col-->
                                        </div>
                                        <!--end::Row-->
                                    </div>
                                </div>
                                <!--end::Pricing Update Section-->

                                <!--begin::Preview Section-->
                                <div class="card card-custom mb-8 bulk-pricing-preview-section" id="previewSection">
                                    <div class="card-header">
                                        <div class="card-title">
                                            <h3 class="fw-bold m-0">Preview Changes</h3>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div class="alert alert-info">
                                            <div class="alert-text">
                                                <strong>Preview:</strong> The following changes will be applied. Please review carefully before confirming.
                                            </div>
                                        </div>

                                        <!--begin::Preview Stats-->
                                        <div class="row mb-5">
                                            <div class="col-lg-3">
                                                <div class="card card-flush bg-light-primary">
                                                    <div class="card-body text-center">
                                                        <span class="fs-2hx fw-bold text-primary d-block" id="previewCompaniesCount">0</span>
                                                        <span class="fs-6 fw-semibold text-gray-400">Companies Affected</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-lg-3">
                                                <div class="card card-flush bg-light-success">
                                                    <div class="card-body text-center">
                                                        <span class="fs-2hx fw-bold text-success d-block" id="previewCoveragesCount">0</span>
                                                        <span class="fs-6 fw-semibold text-gray-400">Coverage Records</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-lg-3">
                                                <div class="card card-flush bg-light-warning">
                                                    <div class="card-body text-center">
                                                        <span class="fs-2hx fw-bold text-warning d-block" id="previewAdjustmentType">-</span>
                                                        <span class="fs-6 fw-semibold text-gray-400">Adjustment Type</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-lg-3">
                                                <div class="card card-flush bg-light-info">
                                                    <div class="card-body text-center">
                                                        <span class="fs-2hx fw-bold text-info d-block" id="previewAdjustmentValue">0</span>
                                                        <span class="fs-6 fw-semibold text-gray-400">Adjustment Value</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <!--end::Preview Stats-->

                                        <!--begin::Preview Table-->
                                        <div class="table-responsive">
                                            <table class="table table-row-dashed table-row-gray-300 gy-7" id="previewTable">
                                                <thead>
                                                    <tr class="fw-bold fs-6 text-gray-800">
                                                        <th>Company</th>
                                                        <th>Prefix</th>
                                                        <th>Operator</th>
                                                        <th>Current Masking Price (৳)</th>
                                                        <th>New Masking Price (৳)</th>
                                                        <th>Current Non-Masking Price (৳)</th>
                                                        <th>New Non-Masking Price (৳)</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="previewTableBody">
                                                    <!-- Preview data will be loaded here -->
                                                </tbody>
                                            </table>
                                        </div>

                                        <!--begin::Preview Pagination-->
                                        <div id="previewPagination" class="d-flex justify-content-between align-items-center mt-6 bulk-pricing-pagination">
                                            <div class="d-flex align-items-center">
                                                <span class="text-muted me-3">Show</span>
                                                <select class="form-select form-select-sm w-auto me-3" id="previewPerPage">
                                                    <option value="25">25</option>
                                                    <option value="50">50</option>
                                                    <option value="100">100</option>
                                                </select>
                                                <span class="text-muted">entries</span>
                                            </div>
                                            <div class="d-flex align-items-center">
                                                <span class="text-muted me-3" id="previewPaginationInfo">Showing 0 to 0 of 0 entries</span>
                                                <div class="btn-group" role="group">
                                                    <button type="button" class="btn btn-sm btn-light" id="previewPrevPage" disabled>
                                                        <i class="ki-duotone ki-arrow-left fs-5"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-light" id="previewNextPage" disabled>
                                                        <i class="ki-duotone ki-arrow-right fs-5"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <!--end::Preview Pagination-->
                                        <!--end::Preview Table-->
                                    </div>
                                </div>
                                <!--end::Preview Section-->
                                </div>
                                <!--end::Form Controls Section-->

                                <!--begin::Post Application Section-->
                                <div id="postApplicationSection" class="bulk-pricing-post-application">
                                    <!--begin::Success Message-->
                                    <div class="card mb-8">
                                        <div class="card-header">
                                            <div class="card-title">
                                                <h3 class="fw-bold text-success">
                                                    <i class="ki-duotone ki-check-circle fs-1 text-success me-2">
                                                        <span class="path1"></span>
                                                        <span class="path2"></span>
                                                    </i>
                                                    Pricing Changes Applied Successfully
                                                </h3>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="row g-6 mb-6">
                                                <div class="col-md-6">
                                                    <div class="d-flex align-items-center">
                                                        <div class="symbol symbol-50px me-3">
                                                            <span class="symbol-label bg-light-primary">
                                                                <i class="ki-duotone ki-office-bag fs-2x text-primary">
                                                                    <span class="path1"></span>
                                                                    <span class="path2"></span>
                                                                    <span class="path3"></span>
                                                                    <span class="path4"></span>
                                                                </i>
                                                            </span>
                                                        </div>
                                                        <div>
                                                            <div class="fs-4 fw-bold text-gray-900" id="postAppCompaniesCount">0</div>
                                                            <div class="fs-7 text-muted">Companies Affected</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="d-flex align-items-center">
                                                        <div class="symbol symbol-50px me-3">
                                                            <span class="symbol-label bg-light-success">
                                                                <i class="ki-duotone ki-price-tag fs-2x text-success">
                                                                    <span class="path1"></span>
                                                                    <span class="path2"></span>
                                                                    <span class="path3"></span>
                                                                </i>
                                                            </span>
                                                        </div>
                                                        <div>
                                                            <div class="fs-4 fw-bold text-gray-900" id="postAppCoveragesCount">0</div>
                                                            <div class="fs-7 text-muted">Coverage Records Updated</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="notice d-flex bg-light-success rounded border-success border border-dashed p-6">
                                                <i class="ki-duotone ki-information-5 fs-2tx text-success me-4">
                                                    <span class="path1"></span>
                                                    <span class="path2"></span>
                                                    <span class="path3"></span>
                                                </i>
                                                <div class="d-flex flex-stack flex-grow-1">
                                                    <div class="fw-semibold">
                                                        <h4 class="text-gray-900 fw-bold">Pricing Update Complete</h4>
                                                        <div class="fs-6 text-gray-700">All pricing changes have been successfully applied to the selected coverage records. The new prices are now active and will be used for future SMS transactions.</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!--end::Success Message-->

                                    <!--begin::Updated Pricing Table-->
                                    <div class="card mb-8 bulk-pricing-updated-table" id="updatedPricingTable">
                                        <div class="card-header">
                                            <div class="card-title">
                                                <h3 class="fw-bold text-gray-800">Current Pricing (After Update)</h3>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="table-responsive">
                                                <table class="table table-row-bordered table-row-gray-100 align-middle gs-0 gy-3">
                                                    <thead>
                                                        <tr class="fw-bold text-muted">
                                                            <th class="min-w-150px">Company</th>
                                                            <th class="min-w-100px">Prefix</th>
                                                            <th class="min-w-120px">Operator</th>
                                                            <th class="min-w-120px">Masking Price (৳)</th>
                                                            <th class="min-w-120px">Non-Masking Price (৳)</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody id="updatedPricingTableBody">
                                                        <!-- Updated pricing data will be populated here -->
                                                    </tbody>
                                                </table>
                                            </div>

                                            <!--begin::Updated Pricing Pagination-->
                                            <div id="updatedPricingPagination" class="d-flex justify-content-between align-items-center mt-6 bulk-pricing-pagination">
                                                <div class="d-flex align-items-center">
                                                    <span class="text-muted me-3">Show</span>
                                                    <select class="form-select form-select-sm w-auto me-3" id="updatedPricingPerPage">
                                                        <option value="25">25</option>
                                                        <option value="50">50</option>
                                                        <option value="100">100</option>
                                                    </select>
                                                    <span class="text-muted">entries</span>
                                                </div>
                                                <div class="d-flex align-items-center">
                                                    <span class="text-muted me-3" id="updatedPricingPaginationInfo">Showing 0 to 0 of 0 entries</span>
                                                    <div class="btn-group" role="group">
                                                        <button type="button" class="btn btn-sm btn-light" id="updatedPricingPrevPage" disabled>
                                                            <i class="ki-duotone ki-arrow-left fs-5"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-light" id="updatedPricingNextPage" disabled>
                                                            <i class="ki-duotone ki-arrow-right fs-5"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                            <!--end::Updated Pricing Pagination-->
                                        </div>
                                    </div>
                                    <!--end::Updated Pricing Table-->

                                    <!--begin::Back Action-->
                                    <div class="d-flex justify-content-center">
                                        <button type="button" class="btn btn-primary btn-lg" id="back-to-bulk-pricing">
                                            <i class="ki-duotone ki-arrow-left fs-2">
                                                <span class="path1"></span>
                                                <span class="path2"></span>
                                            </i>
                                            Back to Bulk Pricing
                                        </button>
                                    </div>
                                    <!--end::Back Action-->
                                </div>
                                <!--end::Post Application Section-->

                                <!--begin::Form Actions Section-->
                                <div id="formActionsSection">
                                    <!--begin::Form Actions-->
                                    <div class="d-flex justify-content-end">
                                        <button type="button" class="btn btn-light me-3" id="reset-bulk-pricing-form">
                                            <i class="ki-duotone ki-arrow-left fs-2">
                                                <span class="path1"></span>
                                                <span class="path2"></span>
                                            </i>
                                            Reset
                                        </button>
                                        <button type="button" class="btn btn-info me-3" id="preview-pricing-changes">
                                            <span class="indicator-label">
                                                <i class="ki-duotone ki-eye fs-2">
                                                    <span class="path1"></span>
                                                    <span class="path2"></span>
                                                    <span class="path3"></span>
                                                </i>
                                                Preview Changes
                                            </span>
                                            <span class="indicator-progress">
                                                Please wait... <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                                            </span>
                                        </button>
                                        <button type="button" class="btn btn-primary bulk-pricing-apply-button" id="apply-pricing-changes">
                                            <span class="indicator-label">
                                                <i class="ki-duotone ki-check fs-2">
                                                    <span class="path1"></span>
                                                    <span class="path2"></span>
                                                </i>
                                                Apply Changes
                                            </span>
                                            <span class="indicator-progress">
                                                Please wait... <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                                            </span>
                                        </button>
                                    </div>
                                    <!--end::Form Actions-->
                                </div>
                                <!--end::Form Actions Section-->
                            </form>
                            <!--end::Bulk Pricing Form-->
                        </div>
                        <!--end::Tab pane - Bulk Pricing Management-->
                        @endif

                        @if(!Auth::user()->hasRole('client'))
                        <!--begin::Tab pane - Pop-up Settings-->
                        <div class="tab-pane fade" id="kt_tab_pane_popup_settings" role="tabpanel">
                            <form id="kt_popup_settings_form" method="POST" action="{{ route('admin.settings.update') }}" enctype="multipart/form-data">
                                @csrf
                                <input type="hidden" name="popup_settings" value="1">

                                <!--begin::Pop-up Configuration Section-->
                                <div class="card card-custom mb-8">
                                    <div class="card-header">
                                        <div class="card-title">
                                            <h3 class="fw-bold m-0">Pop-up Configuration</h3>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <!--begin::Row-->
                                        <div class="row">
                                            <!--begin::Col-->
                                            <div class="col-lg-6">
                                                <!--begin::Pop-up Title-->
                                                <div class="fv-row mb-7">
                                                    <label class="fw-semibold fs-6 mb-2 required">Pop-up Title</label>
                                                    <input type="text" name="popup_title" class="form-control form-control-solid"
                                                           placeholder="Enter pop-up title"
                                                           value="{{ old('popup_title', $settings['popup_title'] ?? '') }}"
                                                           maxlength="255" />
                                                    <div class="form-text">The title displayed at the top of the pop-up (max 255 characters)</div>
                                                    @error('popup_title')
                                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                                <!--end::Pop-up Title-->
                                            </div>
                                            <!--end::Col-->

                                            <!--begin::Col-->
                                            <div class="col-lg-6">
                                                <!--begin::Pop-up Status-->
                                                <div class="fv-row mb-7">
                                                    <label class="fw-semibold fs-6 mb-2">Pop-up Status</label>
                                                    <div class="form-check form-switch form-check-custom form-check-solid">
                                                        <input class="form-check-input" type="checkbox" name="popup_status" id="popup_status"
                                                               value="1" {{ old('popup_status', $settings['popup_status'] ?? false) ? 'checked' : '' }} />
                                                        <label class="form-check-label fw-semibold text-gray-400 ms-3" for="popup_status">
                                                            Enable pop-up display
                                                        </label>
                                                    </div>
                                                    <div class="form-text">Toggle to enable or disable the pop-up for users</div>
                                                    @error('popup_status')
                                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                                <!--end::Pop-up Status-->
                                            </div>
                                            <!--end::Col-->
                                        </div>
                                        <!--end::Row-->

                                        <!--begin::Row-->
                                        <div class="row">
                                            <!--begin::Col-->
                                            <div class="col-lg-12">
                                                <!--begin::Pop-up Description-->
                                                <div class="fv-row mb-7">
                                                    <label class="fw-semibold fs-6 mb-2 required">Pop-up Description</label>
                                                    <textarea name="popup_description" class="form-control form-control-solid"
                                                              rows="8" placeholder="Enter pop-up content description">{{ old('popup_description', $settings['popup_description'] ?? '') }}</textarea>
                                                    <div class="form-text">The main content body of the pop-up. Supports rich text formatting.</div>
                                                    @error('popup_description')
                                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                                <!--end::Pop-up Description-->
                                            </div>
                                            <!--end::Col-->
                                        </div>
                                        <!--end::Row-->

                                        <!--begin::Row-->
                                        <div class="row">
                                            <!--begin::Col-->
                                            <div class="col-lg-12">
                                                <!--begin::Pop-up File Upload-->
                                                <div class="fv-row mb-7">
                                                    <label class="fw-semibold fs-6 mb-2">Attachment File</label>
                                                    <input type="file" name="popup_file" class="form-control form-control-solid" accept=".pdf,.jpg,.jpeg" />
                                                    @if(isset($settings['popup_file']) && $settings['popup_file'])
                                                        <div class="form-text">
                                                            Current file: <strong>{{ basename($settings['popup_file']) }}</strong>
                                                            <a href="{{ asset('storage/' . $settings['popup_file']) }}" target="_blank" class="text-primary ms-2">
                                                                <i class="ki-duotone ki-eye fs-5">
                                                                    <span class="path1"></span>
                                                                    <span class="path2"></span>
                                                                    <span class="path3"></span>
                                                                </i>
                                                                View
                                                            </a>
                                                        </div>
                                                    @endif
                                                    <div class="form-text">Upload a PDF document or image to attach to the pop-up (PDF, JPG, JPEG files only, max 5MB)</div>
                                                    @error('popup_file')
                                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                                <!--end::Pop-up File Upload-->
                                            </div>
                                            <!--end::Col-->
                                        </div>
                                        <!--end::Row-->
                                    </div>
                                </div>
                                <!--end::Pop-up Configuration Section-->

                                <!--begin::Form Actions-->
                                <div class="d-flex justify-content-end">
                                    <button type="button" class="btn btn-light me-3" id="reset-popup-form">
                                        <i class="ki-duotone ki-arrow-left fs-2">
                                            <span class="path1"></span>
                                            <span class="path2"></span>
                                        </i>
                                        Reset
                                    </button>
                                    <button type="submit" class="btn btn-primary" id="submit-popup-settings">
                                        <span class="indicator-label">
                                            <i class="ki-duotone ki-check fs-2">
                                                <span class="path1"></span>
                                                <span class="path2"></span>
                                            </i>
                                            Save Settings
                                        </span>
                                        <span class="indicator-progress">
                                            Please wait... <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                                        </span>
                                    </button>
                                </div>
                                <!--end::Form Actions-->
                            </form>
                        </div>
                        <!--end::Tab pane - Pop-up Settings-->
                        @endif
                    </div>
                    <!--end::Tab content-->
                </div>
                <!--end::Card body-->
            </div>
            <!--end::Card-->
        </div>
        <!--end::Content container-->
    </div>
    <!--end::Content-->
</div>
<!--end::Content wrapper-->
@endsection

@section('css')
<style>
.file-upload-area {
    transition: all 0.3s ease;
}

.file-upload-area:hover {
    border-color: var(--bs-primary) !important;
    background-color: rgba(var(--bs-primary-rgb), 0.05);
}

.file-upload-area.dragover {
    border-color: var(--bs-primary) !important;
    background-color: rgba(var(--bs-primary-rgb), 0.1);
}

.file-upload-placeholder {
    user-select: none;
}
</style>
@endsection

@section('js')
<script src="{{ asset('js/admin-settings.js') }}"></script>
@endsection
