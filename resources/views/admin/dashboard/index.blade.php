@extends('layouts.app')

@section('title', 'Admin Dashboard')

@section('css')
<link href="{{ asset('css/admin-dashboard.css') }}" rel="stylesheet" type="text/css" />
@endsection

@section('content')
    <!--begin::Container-->
    <div class="container-xxl" id="kt_content_container">

        <!-- Summary Cards Component -->
        <x-dashboard.summary-cards :cards="$summaryCards" />

        <!-- Main Content Row -->
        <div class="row g-5 g-xl-10">
            <!-- Action Queue Component -->
            <x-dashboard.action-queue :action-queue="$actionQueue" />

            <!-- Balance Summary Component -->
            <x-dashboard.balance-summary
                :balance="$aggregatedMetrics['total_balance']"
                :balance-msgs="$aggregatedMetrics['total_balance_msgs']"
                :sms-last-week="$aggregatedMetrics['sms_last_week']"
                :cost-last-week="$aggregatedMetrics['cost_last_week']"
                :sms-in-last-month="$aggregatedMetrics['sms_in_last_month']"
                :cost-in-last-month="$aggregatedMetrics['cost_in_last_month']"
                :last-month-name="$aggregatedMetrics['last_month_name']" />
        </div>
    </div>
    <!--end::Container-->
@endsection

@section('js')
<script src="{{ asset('js/admin-notification-system.js') }}"></script>
<script src="{{ asset('js/admin-dashboard-realtime.js') }}?v={{ time() }}"></script>
<script>
let adminDashboard;

document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing admin dashboard...');

    // Check if required classes are available
    if (typeof AdminDashboardRealtime === 'undefined') {
        console.error('AdminDashboardRealtime class not found!');
        return;
    }

    if (typeof AdminNotificationSystem === 'undefined') {
        console.error('AdminNotificationSystem class not found!');
        return;
    }

    // Initialize real-time system
    adminDashboard = new AdminDashboardRealtime();
    adminDashboard.start();
    console.log('Admin dashboard initialized successfully');

    // Enhanced quick actions with loading states
    const quickActionButtons = document.querySelectorAll('[data-quick-action]');
    quickActionButtons.forEach(button => {
        button.addEventListener('click', function() {
            const originalText = this.innerHTML;
            this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Processing...';
            this.disabled = true;

            // Re-enable button after 5 seconds as fallback
            setTimeout(() => {
                this.innerHTML = originalText;
                this.disabled = false;
            }, 5000);
        });
    });

    // Manual refresh button for action queue
    const refreshButton = document.querySelector('[onclick="refreshActionQueue()"]');
    if (refreshButton) {
        console.log('Refresh button found, setting up click handler');
        refreshButton.onclick = function() {
            console.log('Refresh button clicked');
            adminDashboard.fetchActionQueue();

            // Show loading state
            const icon = this.querySelector('i');
            if (icon) {
                icon.classList.add('fa-spin');
                setTimeout(() => {
                    icon.classList.remove('fa-spin');
                }, 1000);
            }
        };
    } else {
        console.warn('Refresh button not found!');
    }

    // Add keyboard shortcuts for admin actions
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + R for refresh all
        if ((e.ctrlKey || e.metaKey) && e.key === 'r' && e.shiftKey) {
            e.preventDefault();
            adminDashboard.refreshAll();
            adminDashboard.notificationSystem.showInfo('Dashboard refreshed manually');
        }
    });
});

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (adminDashboard) {
        adminDashboard.stop();
    }
});

// Legacy function for backward compatibility
function refreshActionQueue() {
    if (adminDashboard) {
        adminDashboard.fetchActionQueue();
    }
}


</script>
@endsection
