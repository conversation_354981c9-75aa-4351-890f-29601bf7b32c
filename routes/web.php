<?php

use App\Http\Controllers\NoticeController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\ProjectController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::prefix('/payment')->middleware(['auth', 'tenant'])->as('payment.')->group(function () {
    Route::post('/pay', [PaymentController::class, 'index'])->name('pay');
    Route::match(['get', 'post'], '/success', [PaymentController::class, 'success'])
        ->middleware('payment.rate.limit')
        ->name('success');
    Route::match(['get', 'post'], '/fail', [PaymentController::class, 'fail'])->name('fail');
    Route::match(['get', 'post'], '/cancel', [PaymentController::class, 'cancel'])->name('cancel');
    Route::match(['get', 'post'], '/ipn', [PaymentController::class, 'ipn'])->name('ipn');
});

// Public notice routes
Route::middleware(['auth'])->group(function () {
    Route::get('/notices', [NoticeController::class, 'publicIndex'])->name('notices.index');
});

require __DIR__.'/auth.php';
require __DIR__.'/admin.php';
