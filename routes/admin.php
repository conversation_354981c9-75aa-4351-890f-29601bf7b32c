<?php

use App\Http\Controllers\AdminDashboardController;
use App\Http\Controllers\CustomerController;
use App\Http\Controllers\GatewayController;
use App\Http\Controllers\NoticeController;
use App\Http\Controllers\PermissionController;
use App\Http\Controllers\RechargeController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\ServerController;
use App\Http\Controllers\SettingsController;
use App\Http\Controllers\SupportTicketController;

Route::prefix('admin')->name('admin.')->middleware(['auth', 'verified'])->group(function () {

    // Admin Dashboard Routes (accessible at /admin/dashboard for direct access)
    Route::get('/dashboard', [AdminDashboardController::class, 'index'])->name('dashboard');
    Route::get('/dashboard/alerts', [AdminDashboardController::class, 'getAlerts'])->name('dashboard.alerts');
    Route::get('/dashboard/metrics', [AdminDashboardController::class, 'getMetrics'])->name('dashboard.metrics');
    Route::get('/dashboard/action-queue', [AdminDashboardController::class, 'getActionQueueData'])->name('dashboard.action-queue');
    Route::get('/dashboard/summary-cards', [AdminDashboardController::class, 'getSummaryCardsData'])->name('dashboard.summary-cards');
    Route::get('/dashboard/aggregated-metrics', [AdminDashboardController::class, 'getAggregatedMetricsData'])->name('dashboard.aggregated-metrics');
    Route::get('/dashboard/data', [AdminDashboardController::class, 'getDashboardDataApi'])->name('dashboard.data');
    Route::post('/dashboard/quick-action', [AdminDashboardController::class, 'quickAction'])->name('dashboard.quick-action');

    Route::get('/assign-masking-number', [CustomerController::class, 'showAssignForm'])->name('assign.masking.get');
    Route::post('/assign-masking-number', [CustomerController::class, 'assignMaskingNumber'])->name('assign.masking.post');

    Route::resource('servers', ServerController::class);
    Route::get('servers/gateway-config', [ServerController::class, 'getGatewayConfig'])->name('servers.gateway-config');
    Route::post('servers/{server}/test', [ServerController::class, 'testConfiguration'])->name('servers.test');
    Route::resource('gateways', GatewayController::class);
    Route::resource('roles', RoleController::class);
    Route::resource('permission', PermissionController::class);
    Route::resource('recharges', RechargeController::class);
    Route::resource('notices', NoticeController::class);

    // Settings Routes
    Route::prefix('settings')->name('settings.')->group(function () {
        Route::get('/', [SettingsController::class, 'index'])->name('index');
        Route::post('/', [SettingsController::class, 'update'])->name('update');
        Route::get('/value', [SettingsController::class, 'getValue'])->name('value');
        Route::delete('/file', [SettingsController::class, 'deleteFile'])->name('delete-file');

        // Bulk Pricing Routes (Super Admin Only)
        Route::middleware(['role:super-admin'])->group(function () {
            Route::get('/bulk-pricing/filter-options', [SettingsController::class, 'getBulkPricingFilterOptions'])->name('bulk-pricing.filter-options');
            Route::post('/bulk-pricing/preview', [SettingsController::class, 'previewBulkPricingChanges'])->name('bulk-pricing.preview');
            Route::post('/bulk-pricing/apply', [SettingsController::class, 'applyBulkPricingChanges'])->name('bulk-pricing.apply');
            Route::get('/bulk-pricing/updated-data', [SettingsController::class, 'getUpdatedPricingData'])->name('bulk-pricing.updated-data');
        });
    });

    // Support Ticket Admin Routes (Super Admin Only)
    Route::prefix('support-tickets')->name('support-tickets.')->middleware(['role:super-admin'])->group(function () {
        Route::get('/', [SupportTicketController::class, 'adminIndex'])->name('index');
        Route::get('/data', [SupportTicketController::class, 'getAdminTickets'])->name('data');
        Route::get('/statistics', [SupportTicketController::class, 'getStatistics'])->name('statistics');
        Route::get('/{supportTicket}', [SupportTicketController::class, 'adminShow'])->name('show');
        Route::get('/{supportTicket}/edit', [SupportTicketController::class, 'adminEdit'])->name('edit');
        Route::put('/{supportTicket}', [SupportTicketController::class, 'adminUpdate'])->name('update');
        Route::delete('/{supportTicket}', [SupportTicketController::class, 'destroy'])->name('destroy');
    });

    // Test route for popup functionality (remove in production)
    Route::get('/test-popup', function () {
        return view('test-popup');
    })->name('test-popup');
});
