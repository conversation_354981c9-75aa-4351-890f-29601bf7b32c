<?php

use App\Http\Controllers\AccountController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\CoverageController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ImpersonationController;
use App\Http\Controllers\MessageController;
use App\Http\Controllers\OtherController;
use App\Http\Controllers\RechargeController;
use App\Http\Controllers\ReportController;
use App\Http\Controllers\ReportsController;
use App\Http\Controllers\SenderController;
use App\Http\Controllers\SupportTicketController;
use App\Http\Controllers\TemplateController;
use App\Http\Controllers\TransactionController;
use App\Http\Controllers\UserController;
use Illuminate\Support\Facades\Route;

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('messages/dynamic-sms', [MessageController::class, 'getDynamicSms'])->name('messages.dynamic-sms.get');
    Route::post('messages/dynamic-sms', [MessageController::class, 'postDynamicSms'])->name('messages.dynamic-sms.post');
    Route::post('messages/group-sms', [MessageController::class, 'messagesGroupStore'])->name('messages.group.store');

    // Main dashboard route - redirects to admin dashboard for admin users
    Route::get('/', function () {
        if (auth()->check() && auth()->user()->hasAnyRole(['super-admin', 'master-reseller', 'reseller'])) {
            return app(\App\Http\Controllers\AdminDashboardController::class)->index();
        }
        return app(\App\Http\Controllers\DashboardController::class)->index();
    })->name('home');

    Route::resource('users', UserController::class);

    Route::patch('users/{user}/status', [UserController::class, 'updateStatus'])->name('users.status.update');
    Route::get('users/{user}/recharge-history', [UserController::class, 'getRechargeHistoryData'])->name('users.recharge-history.data');
    Route::post('users/{user}/verify', [UserController::class, 'verifyUser'])->name('users.verify');
    Route::get('recharges/{recharge}', [RechargeController::class, 'show'])->name('recharges.show');

    // User coverage routes
    Route::prefix('users/{user}/coverages')->name('users.coverages.')->group(function () {
        Route::get('/data', [UserController::class, 'getCoverageData'])->name('data');
        Route::post('/', [UserController::class, 'storeCoverage'])->name('store');
        Route::get('/{coverage}', [UserController::class, 'getCoverage'])->name('show');
        Route::put('/{coverage}', [UserController::class, 'updateCoverage'])->name('update');
        Route::delete('/{coverage}', [UserController::class, 'deleteCoverage'])->name('destroy');
    });

    // Impersonation routes - only for super-admin
    Route::prefix('impersonate')->name('impersonate.')->group(function () {
        Route::post('/start/{user}', [ImpersonationController::class, 'start'])->name('start');
        Route::post('/stop', [ImpersonationController::class, 'stop'])->name('stop');
    });

    Route::resource('messages', MessageController::class);
    Route::resource('contacts', ContactController::class);
    Route::resource('templates', TemplateController::class);
    Route::resource('coverages', CoverageController::class);
    Route::resource('senders', SenderController::class);

    // Additional sender routes
    Route::post('/senders/{sender}/approve', [SenderController::class, 'approve'])->name('senders.approve');

    Route::get('/price-coverage', [OtherController::class, 'priceCoverage'])->name('price-coverage');
    Route::get('/developers', [OtherController::class, 'developers'])->name('developers');
    Route::get('/developers/pdf', [App\Http\Controllers\DevelopersPdfController::class, 'downloadPdf'])->name('developers.pdf');
    Route::post('/generate_api_key', [OtherController::class, 'generateApiKey'])->name('generate-api-key');
    Route::post('/add-group', [ContactController::class, 'addGroup'])->name('group');

    Route::prefix('account')->name('account.')->group(function () {
        Route::get('/overview', [AccountController::class, 'overview'])->name('overview');
        Route::get('/api-keys', [AccountController::class, 'apiKeys'])->name('api-keys');
        Route::get('/add-funds', [AccountController::class, 'addFunds'])->name('add-funds');
        Route::get('/recharges', [AccountController::class, 'rechargeHistory'])->name('recharge-history');
        Route::get('/logs', [AccountController::class, 'logs'])->name('logs');
        Route::get('/settings', [AccountController::class, 'settings'])->name('settings');
        Route::patch('/settings-update', [AccountController::class, 'settingsUpdate'])->name('settings.update');
        Route::patch('/update-email', [AccountController::class, 'updateEmail'])->name('update-email');
        Route::patch('/update-password', [AccountController::class, 'updatePassword'])->name('update-password');

        // Domain management routes - restricted to vendor roles and above
        Route::middleware(['role:super-admin|master-reseller|reseller'])->group(function () {
            Route::get('/domains', [AccountController::class, 'domains'])->name('domains');
            Route::post('/domains', [AccountController::class, 'storeDomain'])->name('domains.store');
            Route::post('/domains/{domainId}/verify', [AccountController::class, 'verifyDomain'])->name('domains.verify');
            Route::delete('/domains/{domainId}', [AccountController::class, 'destroyDomain'])->name('domains.destroy');
        });

        // Support Ticket routes
        Route::get('/support-tickets', [SupportTicketController::class, 'index'])->name('support-tickets');
        Route::get('/support-tickets/create', [SupportTicketController::class, 'create'])->name('support-tickets.create');
        Route::post('/support-tickets', [SupportTicketController::class, 'store'])->name('support-tickets.store');
        Route::get('/support-tickets/{supportTicket}', [SupportTicketController::class, 'show'])->name('support-tickets.show');
        Route::get('/support-tickets/{supportTicket}/edit', [SupportTicketController::class, 'edit'])->name('support-tickets.edit');
        Route::put('/support-tickets/{supportTicket}', [SupportTicketController::class, 'update'])->name('support-tickets.update');
    });
    Route::get('/recharges', [AccountController::class, 'recharge'])->name('recharges');

    // Reports Routes
    Route::prefix('reports')->middleware(['auth'])->name('reports.')->group(function () {
        Route::get('/recharges', [ReportsController::class, 'report'])
            ->name('recharges');
        Route::get('/transactions', [TransactionController::class, 'index'])
            ->name('transactions');
        Route::get('/view-dlr/{type}', [ReportsController::class, 'viewDlr'])
            ->name('view-dlr');
        Route::get('/export-dlr', [ReportController::class, 'exportDlr'])
            ->name('export-dlr');
        Route::get('/summary-log', [ReportsController::class, 'summaryLogs'])
            ->name('summary-log');
        Route::get('/export-summary-log', [ReportController::class, 'exportSummaryLog'])
            ->name('export-summary-log');
        Route::get('/export-transactions', [TransactionController::class, 'exportTransactions'])
            ->name('export-transactions');



        // today's-dlr, today's-details, archived-dlr
        /* Route::get('/campaign-dlr/{type}', [ReportsController::class, 'campaignDlr'])
             ->name('campaign-dlr'); // todays-dlr, archived-dlr
         Route::get('/statistics/summary-logs', [ReportsController::class, 'summaryLogs'])
             ->name('statistics-summary-logs');
         Route::get('/statistics/details-logs', [ReportsController::class, 'detailsLogs'])
             ->name('statistics-details-logs');
         Route::get('/statistics/day-wise-logs', [ReportsController::class, 'dayWiseLogs'])
             ->name('statistics-day-wise-logs');
         Route::get('/statistics/api-sms-purpose', [ReportsController::class, 'apiSmsPurpose'])
             ->name('statistics-api-sms-purpose');
         Route::get('/scheduled-sms', [ReportsController::class, 'scheduledSms'])
             ->name('scheduled-sms');
         Route::get('/transactions', [ReportsController::class, 'transactions'])
             ->name('transactions');
         Route::get('/usages/today', [ReportsController::class, 'todayUsages'])
             ->name('usages-today');
         Route::get('/usages/last-7-days', [ReportsController::class, 'last7DaysUsages'])
             ->name('usages-last-7-days');
         Route::get('/usages/this-month', [ReportsController::class, 'thisMonthUsages'])
             ->name('usages-this-month');*/
    });

    Route::get('/{company}', [DashboardController::class, 'switch']);
});
