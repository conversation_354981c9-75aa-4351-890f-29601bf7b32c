<?php

use App\Http\Controllers\API\SendSmsController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::match(['get', 'post'], '/sms-send', [SendSmsController::class, 'smsSend'])->middleware('api.tenant');

// Credit Balance API
Route::match(['get', 'post'], '/{apiKey}/getBalance', [SendSmsController::class, 'getBalance'])->middleware('api.tenant');

// Delivery Report API
Route::match(['get', 'post'], '/{apiKey}/getDLR/getAll', [SendSmsController::class, 'getDLR'])->middleware('api.tenant');

// API Key Retrieval (no middleware needed as it authenticates via username/password)
Route::match(['get', 'post'], '/getkey/{username}/{password}', [SendSmsController::class, 'getApiKey']);
