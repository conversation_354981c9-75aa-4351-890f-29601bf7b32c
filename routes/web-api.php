<?php

use App\Http\Controllers\ContactController;
use App\Http\Controllers\SenderController;
use App\Http\Controllers\UserController;
use Illuminate\Support\Facades\Route;

Route::name('webapi.')->middleware(['auth', 'verified'])->group(function () {
    Route::post('/senders/{sender}/make-default', [SenderController::class, 'makeDefault'])->name('default');
    Route::post('/add-sender', [SenderController::class, 'store'])->name('store');
    Route::post('/add-balance', [UserController::class, 'balance'])->name('user.balance');
    Route::delete('/delete-group/{group}', [ContactController::class, 'deleteGroup']);
    Route::get('/user/{user}', [UserController::class, 'getUser'])->name('user');

});
