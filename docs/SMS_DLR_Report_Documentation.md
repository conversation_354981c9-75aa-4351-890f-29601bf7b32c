# SMS Delivery Report (DLR) Documentation

## Overview

The SMS Delivery Report (DLR) provides a comprehensive view of SMS delivery status, costs, and detailed information for all sent messages. This feature has been enhanced to provide better user experience, data privacy, and detailed analytics.

## Features

### 1. Enhanced Table Structure
- **Serial Number**: Auto-incrementing row numbers for easy reference
- **Sent Time**: Formatted timestamp showing when SMS was sent or scheduled
- **Sender**: Sender ID/name used for the SMS
- **Schedule Status**: Visual indicator showing if SMS was sent immediately or scheduled
- **Charge Per SMS**: Individual SMS cost calculation
- **Total Sent**: Number of SMS segments sent
- **Total Cost**: Complete cost breakdown
- **API Response Status**: Success/failure status with color-coded badges
- **View Details**: Action button to open detailed modal

### 2. Advanced Filtering
- **Date Range Filter**: Filter messages by date range (defaults to today)
- **Real-time Search**: Search across all visible columns
- **Tenant-based Filtering**: Automatic filtering based on user hierarchy

### 3. Detailed Modal View
The "View Details" modal provides comprehensive information:

#### Basic Information
- Message ID and phone number
- Sender details (name and type: masking/non-masking)
- SMS type and operator information
- Server used for delivery
- Batch number for tracking

#### Cost Breakdown
- SMS count and per-SMS rate
- Total cost calculation
- Delivery status
- Company and user information

#### Timing Information
- Creation timestamp
- Scheduled time (if applicable)

#### SMS Content
- Full message content (hidden from main table for privacy)
- Formatted display with proper line breaks

#### API Response
- Complete API response from SMS gateway
- JSON formatted for easy reading
- Scrollable view for long responses

## User Access Control

### Hierarchical Data Access
The system respects the company hierarchy:

- **Super Admin**: Can view all SMS records across all companies
- **Master Reseller**: Can view their own and child company records
- **Reseller**: Can view their own and client company records  
- **Client**: Can only view their own SMS records

### Data Privacy
- SMS content is hidden in the main table view
- Full content only visible in the detailed modal
- API responses are only shown to authorized users

## Technical Implementation

### Backend Features
- **Optimized Queries**: Uses eager loading and proper joins for performance
- **Tenant Filtering**: Automatic filtering based on user permissions
- **Error Handling**: Proper error responses for unauthorized access
- **Data Validation**: Input validation for date ranges and parameters

### Frontend Features
- **Responsive Design**: Works on desktop and mobile devices
- **Real-time Updates**: DataTables with server-side processing
- **Loading States**: Visual feedback during data loading
- **Color-coded Status**: Easy-to-understand status indicators

### Performance Optimizations
- Server-side pagination for large datasets
- Indexed database queries for fast filtering
- Lazy loading of detailed information
- Efficient JSON responses

## Usage Instructions

### Accessing the Report
1. Navigate to Reports → View DLR
2. Select date range (defaults to today)
3. Use search to find specific messages
4. Click "View Details" for comprehensive information

### Filtering Data
- **Date Filter**: Use "From" and "To" date inputs, then click "Filter"
- **Search**: Type in the search box to filter across all columns
- **Reset**: Clear search or change dates to reset filters

### Understanding Status Indicators
- **Green Badge (Success)**: SMS delivered successfully
- **Red Badge (Failed)**: SMS delivery failed
- **Yellow Badge (Pending)**: SMS is pending delivery
- **Blue Badge (Scheduled)**: SMS is scheduled for future delivery

### Cost Information
- **Charge/SMS**: Shows the rate per individual SMS
- **Total Sent**: Number of SMS segments (long messages = multiple segments)
- **Total Cost**: Complete cost including all segments

## API Endpoints

### Main Report Data
```
GET /datatables/today-detail-list
Parameters:
- date_from: Start date (YYYY-MM-DD)
- date_to: End date (YYYY-MM-DD)
- search: Search term
- pagination parameters
```

### SMS Details
```
GET /datatables/sms-details/{id}
Returns: JSON object with complete SMS information
```

## Database Schema

### Key Tables Used
- `messages`: Core SMS data
- `senders`: Sender ID information
- `companies`: Company/tenant data
- `users`: User information
- `coverages`: Pricing and operator data
- `servers`: SMS gateway information

### Important Fields
- `messages.sms_count`: Number of SMS segments
- `messages.sms_cost`: Total cost for the message
- `messages.status`: Delivery status
- `messages.api_response`: Gateway response
- `messages.schedule_at`: Scheduled delivery time

## Troubleshooting

### Common Issues
1. **No data showing**: Check date range and user permissions
2. **Modal not loading**: Verify message ID and access rights
3. **Search not working**: Ensure proper column indexing
4. **Cost calculation errors**: Check coverage table pricing

### Performance Tips
- Use specific date ranges for better performance
- Avoid very broad searches on large datasets
- Use pagination for large result sets

## Future Enhancements

### Planned Features
- Export functionality (CSV, PDF)
- Advanced analytics and charts
- Bulk operations on messages
- Real-time delivery status updates
- Custom report templates

This enhanced DLR report provides a professional, comprehensive view of SMS delivery with proper security, performance, and user experience considerations.
