# New SMS API Endpoints Documentation

## Overview

Three new API endpoints have been successfully implemented for the SMS application, following the existing API architecture patterns, authentication methods, and response formats.

## Implemented Endpoints

### 1. Credit Balance API
**Endpoint:** `api/{apiKey}/getBalance`  
**Methods:** GET, POST  
**Authentication:** API Key or Username/Password  
**Middleware:** api.tenant

#### Request Parameters
- `api_key` (string, optional): API key for authentication
- `username` (string, optional): Username for authentication (required if api_key not provided)
- `password` (string, optional): Password for authentication (required if api_key not provided)

#### Response Format
```json
{
    "response_code": "0000",
    "message": "Balance retrieved successfully",
    "data": {
        "current_balance": 20000.00,
        "balance_expired": "2035-06-30 03:35:58",
        "company_name": "SMSFrom"
    }
}
```

#### Example Usage
```bash
# Using API Key
curl -X POST "http://your-domain.com/api/your-api-key/getBalance" \
     -H "Content-Type: application/json" \
     -d '{"api_key": "your-api-key"}'

# Using Username/Password
curl -X POST "http://your-domain.com/api/your-api-key/getBalance" \
     -H "Content-Type: application/json" \
     -d '{"username": "your-username", "password": "your-password"}'
```

### 2. Delivery Report API
**Endpoint:** `api/{apiKey}/getDLR/getAll`  
**Methods:** GET, POST  
**Authentication:** API Key or Username/Password  
**Middleware:** api.tenant

#### Request Parameters
- `api_key` (string, optional): API key for authentication
- `username` (string, optional): Username for authentication (required if api_key not provided)
- `password` (string, optional): Password for authentication (required if api_key not provided)
- `limit` (integer, optional): Number of records to return (default: 100, max: 1000)
- `offset` (integer, optional): Number of records to skip (default: 0)
- `date_from` (date, optional): Start date filter (YYYY-MM-DD format)
- `date_to` (date, optional): End date filter (YYYY-MM-DD format)

#### Response Format
```json
{
    "response_code": "0000",
    "message": "Delivery reports retrieved successfully",
    "data": {
        "total": 150,
        "limit": 10,
        "offset": 0,
        "messages": [
            {
                "message_id": "ABC1234567",
                "phone_number": "1234567890",
                "sender_name": "YourSender",
                "sms_content": "Your message content",
                "sms_type": "text",
                "sms_count": 1,
                "sms_cost": 0.05,
                "status": "delivered",
                "sent_at": "2024-01-15 10:30:00",
                "scheduled_at": null,
                "batch_number": "BATCH123",
                "api_response": {"status": "success"},
                "is_masking": true
            }
        ]
    }
}
```

#### Example Usage
```bash
# Basic request
curl -X POST "http://your-domain.com/api/your-api-key/getDLR/getAll" \
     -H "Content-Type: application/json" \
     -d '{"api_key": "your-api-key", "limit": 10}'

# With date filters
curl -X POST "http://your-domain.com/api/your-api-key/getDLR/getAll" \
     -H "Content-Type: application/json" \
     -d '{"api_key": "your-api-key", "date_from": "2024-01-01", "date_to": "2024-01-31"}'
```

### 3. API Key Retrieval
**Endpoint:** `api/getkey/{username}/{password}`  
**Methods:** GET, POST  
**Authentication:** Username/Password (via URL parameters)  
**Middleware:** None (handles authentication internally)

#### Request Parameters
- `username` (string, required): Username in URL path
- `password` (string, required): Password in URL path

#### Response Format
```json
{
    "response_code": "0000",
    "message": "API key retrieved successfully",
    "data": {
        "api_key": "your-generated-api-key",
        "company_name": "Your Company Name",
        "user_name": "Your User Name"
    }
}
```

#### Example Usage
```bash
curl -X POST "http://your-domain.com/api/getkey/your-username/your-password" \
     -H "Content-Type: application/json"
```

## Error Handling

All endpoints follow the existing error response format using the SmsResponse helper class:

### Common Error Codes
- `1003`: Invalid API key
- `1010`: Invalid User & Password
- `422`: Validation errors

### Error Response Format
```json
{
    "response_code": "1003",
    "message": "Invalid API key"
}
```

### Validation Error Format
```json
{
    "success": false,
    "message": "Validation errors",
    "errors": {
        "username": ["The username field is required."],
        "password": ["The password field is required."]
    }
}
```

## Authentication Methods

All endpoints support two authentication methods:

1. **API Key Authentication**: Pass the API key in the request body
2. **Username/Password Authentication**: Pass credentials in the request body

The API Key Retrieval endpoint specifically uses URL parameters for credentials.

## Implementation Details

### Files Created/Modified
- `app/Http/Requests/GetBalanceAPIRequest.php` - Validation for balance endpoint
- `app/Http/Requests/GetDLRAPIRequest.php` - Validation for DLR endpoint  
- `app/Http/Requests/GetApiKeyRequest.php` - Validation for API key endpoint
- `app/Http/Services/ApiService.php` - Business logic for all three endpoints
- `app/Http/Controllers/API/SendSmsController.php` - Controller methods added
- `routes/api.php` - New routes added

### Architecture Consistency
- Uses existing `api.tenant` middleware for company identification
- Follows existing `SmsResponse` helper for response formatting
- Uses existing `SmsSendingResponseException` for error handling
- Maintains same authentication patterns as existing SMS send API
- Uses existing `CompanyRepository` for data access

## Testing

All endpoints have been manually tested and verified to work correctly:
- ✅ Balance retrieval with API key authentication
- ✅ Balance retrieval with username/password authentication
- ✅ DLR retrieval with pagination and filtering
- ✅ API key retrieval with credentials
- ✅ Error handling for invalid credentials
- ✅ Error handling for invalid API keys
- ✅ Proper validation error responses

## Security Considerations

- All endpoints use the same security model as existing APIs
- API keys are validated against the database
- Username/password authentication uses Laravel's built-in Auth system
- Tenant isolation is maintained through the api.tenant middleware
- Input validation prevents malicious data injection
- Rate limiting can be applied using Laravel's throttle middleware if needed
