# Payment Approval Permissions Documentation

## Overview

The payment approval system implements hierarchical permission-based access control to ensure that users can only approve payments for accounts they have authority over. This system follows the established SMS application patterns for role-based data filtering and hierarchical permissions.

## Permission Hierarchy

### Super Admin
- **Access Level**: Full access to all payments
- **Can Approve**: All pending payments across all companies and users
- **Restrictions**: None

### Master Reseller
- **Access Level**: Hierarchical access within their organization
- **Can Approve**: Payments for any user within their hierarchy (recursive parent-child relationship)
- **Restrictions**: Cannot approve payments for users outside their hierarchy

### Reseller
- **Access Level**: Direct clients only
- **Can Approve**: Payments for their direct clients only (where payment user's parent_id equals current user's id)
- **Restrictions**: Cannot approve payments for users who are not their direct clients

### Client
- **Access Level**: Read-only
- **Can Approve**: No payments (approval button is hidden)
- **Restrictions**: Cannot approve any payments

## Implementation Details

### Core Permission Method

The system uses a reusable helper method `canApprovePayment(User $currentUser, Payment $payment): bool` that:

1. Checks if the payment has an associated user
2. Applies role-based permission logic
3. Uses the existing `isUserInHierarchy` method for hierarchical checks
4. Returns boolean permission result

### Integration Points

#### RechargeController
- **Location**: `app/Http/Controllers/RechargeController.php`
- **Method**: `list()` method's action column
- **Implementation**: Only shows "Approved" button when `canApprovePayment()` returns true
- **Query Optimization**: Includes `user.parent` relationship to avoid N+1 queries

#### HierarchicalPermissions Trait
- **Location**: `app/Helpers/Traits/HierarchicalPermissions.php`
- **Purpose**: Centralized permission logic for reuse across controllers
- **Methods**: Includes all hierarchical permission methods (canApprovePayment, canEditUser, etc.)

## Security Features

### Null Safety
- Checks for payment->user relationship existence before permission evaluation
- Graceful handling of missing relationships

### Hierarchical Validation
- Recursive parent-child relationship verification
- Prevents unauthorized access to payments outside user's hierarchy

### Role-Based Access Control
- Integration with Spatie Laravel Permission package
- Consistent role checking across all permission methods

## Usage Examples

### Basic Permission Check
```php
$currentUser = auth()->user();
$payment = Payment::with('user.parent')->find($paymentId);

if ($this->canApprovePayment($currentUser, $payment)) {
    // Show approval button or allow approval action
}
```

### In DataTable Action Column
```php
->addColumn('action', function (Payment $payment) {
    $action = '<!-- View Details Button -->';
    
    if ($payment->payment_status == 'pending' && 
        $this->canApprovePayment(auth()->user(), $payment)) {
        $action .= '<!-- Approval Button -->';
    }
    
    return $action;
})
```

## Database Considerations

### Required Relationships
- Payment must have `user` relationship loaded
- User must have `parent` relationship loaded for hierarchy checks
- Recommended eager loading: `Payment::with(['user.parent'])`

### Performance Optimization
- Uses eager loading to prevent N+1 queries
- Efficient recursive hierarchy traversal
- Minimal database queries for permission checks

## Testing Scenarios

### Test Cases to Verify
1. **Super Admin**: Can approve all payments
2. **Master Reseller**: Can approve payments within hierarchy, cannot approve outside
3. **Reseller**: Can approve direct client payments only
4. **Client**: Cannot approve any payments (button hidden)
5. **Null Safety**: Handles payments without associated users gracefully
6. **Hierarchy Validation**: Correctly identifies parent-child relationships

### Sample Test Data Structure
```
Super Admin (ID: 1)
├── Master Reseller (ID: 2, parent_id: 1)
│   ├── Reseller (ID: 3, parent_id: 2)
│   │   └── Client (ID: 4, parent_id: 3)
│   └── Client (ID: 5, parent_id: 2)
└── Client (ID: 6, parent_id: 1)
```

## Migration from Previous System

### Before
- Approval button shown to all users
- No permission validation
- Security risk of unauthorized approvals

### After
- Role-based approval button visibility
- Hierarchical permission validation
- Secure payment approval workflow

## Troubleshooting

### Common Issues
1. **Approval button not showing**: Check user role and hierarchy relationships
2. **Permission denied errors**: Verify parent_id relationships in database
3. **N+1 query issues**: Ensure proper eager loading of relationships

### Debug Steps
1. Verify user roles: `$user->hasRole('role-name')`
2. Check hierarchy: `$this->isUserInHierarchy($currentUser, $targetUser)`
3. Validate relationships: `$payment->user` and `$user->parent`
