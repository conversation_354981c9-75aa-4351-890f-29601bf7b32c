{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.0", "barryvdh/laravel-dompdf": "^3.1", "guzzlehttp/guzzle": "^7.2", "laravel/framework": "^10.0", "laravel/sanctum": "^3.2", "laravel/telescope": "^5.0", "laravel/tinker": "^2.8", "maatwebsite/excel": "^3.1", "paypal/rest-api-sdk-php": "^1.6", "rappasoft/laravel-authentication-log": "^3.0", "spatie/laravel-permission": "^5.10", "stripe/stripe-php": "^10.13", "torann/geoip": "^3.0", "yajra/laravel-datatables": "^10.0"}, "require-dev": {"fakerphp/faker": "^1.9.1", "laravel/breeze": "^1.20", "laravel/pint": "^1.0", "laravel/sail": "^1.26", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "pestphp/pest": "^2.0", "pestphp/pest-plugin-laravel": "^2.0", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/SettingsHelper.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}