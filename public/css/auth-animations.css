/* SMS Provider Futuristic Authentication Animations */

/* Background Animations */
@keyframes dataFlow {
    from { transform: translateY(0px) translateX(0px); }
    to { transform: translateY(-100px) translateX(-20px); }
}

@keyframes circuitPulse {
    0%, 100% { opacity: 0.2; }
    50% { opacity: 0.4; }
}

/* Particle Animations */
@keyframes particleFlow {
    0% {
        top: 100%;
        opacity: 0;
        transform: scale(0.5);
    }
    10% {
        opacity: 1;
        transform: scale(1);
    }
    90% {
        opacity: 1;
        transform: scale(1);
    }
    100% {
        top: -10%;
        opacity: 0;
        transform: scale(0.5);
    }
}

/* Network Topology Animations */
@keyframes dataTransmission {
    0%, 100% {
        opacity: 0.3;
        box-shadow: 0 0 5px rgba(0, 255, 255, 0.3);
    }
    50% {
        opacity: 1;
        box-shadow: 0 0 15px rgba(0, 255, 255, 0.8);
    }
}

/* SMS Tower Animations */
@keyframes towerGlow {
    0%, 100% { 
        filter: drop-shadow(0 0 20px #00ffff) brightness(1);
        transform: translateY(-50%) scale(1);
    }
    50% { 
        filter: drop-shadow(0 0 30px #00ffff) brightness(1.2);
        transform: translateY(-50%) scale(1.02);
    }
}

/* Signal Wave Animations */
@keyframes signalPulse {
    0% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(0.5);
    }
    100% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(1.5);
    }
}

/* Message Bubble Animations */
@keyframes messageFly {
    0% {
        opacity: 0;
        transform: translateY(20px) scale(0.8);
    }
    10%, 90% {
        opacity: 1;
        transform: translateY(0px) scale(1);
    }
    100% {
        opacity: 0;
        transform: translateY(-20px) scale(0.8);
    }
}

/* Login Panel Animations */
@keyframes panelGlow {
    0%, 100% {
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.4),
            0 0 30px rgba(0, 255, 255, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.1),
            inset 0 -1px 0 rgba(0, 255, 255, 0.2);
    }
    50% {
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.4),
            0 0 40px rgba(0, 255, 255, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.15),
            inset 0 -1px 0 rgba(0, 255, 255, 0.3);
    }
}

/* Holographic Scan Line Animation */
@keyframes scanLine {
    0% {
        top: 0;
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        top: 100%;
        opacity: 0;
    }
}

/* Typography Animations */
@keyframes titleGlow {
    0%, 100% {
        text-shadow: 
            0 0 10px rgba(0, 255, 255, 0.8),
            0 0 20px rgba(0, 255, 255, 0.4),
            0 0 30px rgba(0, 255, 255, 0.2);
    }
    50% {
        text-shadow: 
            0 0 15px rgba(0, 255, 255, 1),
            0 0 25px rgba(0, 255, 255, 0.6),
            0 0 35px rgba(0, 255, 255, 0.3);
    }
}

@keyframes titleUnderline {
    0%, 100% { width: 60px; opacity: 0.6; }
    50% { width: 80px; opacity: 1; }
}

/* Network Pulse Animation */
@keyframes networkPulse {
    0%, 100% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.05) rotate(2deg); }
}

/* Loading Spinner Animation */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Fade In Animation */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Slide In Animation */
@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Pulse Animation */
@keyframes pulse {
    0%, 100% {
        box-shadow:
            0 0 30px rgba(0, 255, 255, 0.4),
            inset 0 0 30px rgba(0, 255, 255, 0.2);
    }
    50% {
        box-shadow:
            0 0 50px rgba(0, 255, 255, 0.6),
            inset 0 0 50px rgba(0, 255, 255, 0.3);
    }
}

/* Glow Animation */
@keyframes glow {
    0%, 100% {
        filter: brightness(1) drop-shadow(0 0 10px #00ffff);
    }
    50% {
        filter: brightness(1.5) drop-shadow(0 0 20px #00ffff);
    }
}

/* Shimmer Animation */
@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

/* Float Animation */
@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* Bounce Animation */
@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

/* Scale Animation */
@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Rotate Animation */
@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Heartbeat Animation */
@keyframes heartbeat {
    0% {
        transform: scale(1);
    }
    14% {
        transform: scale(1.1);
    }
    28% {
        transform: scale(1);
    }
    42% {
        transform: scale(1.1);
    }
    70% {
        transform: scale(1);
    }
}
