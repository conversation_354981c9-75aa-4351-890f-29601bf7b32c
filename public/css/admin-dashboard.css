/* Admin Dashboard Custom Styles */

/* Real-time Metric Updates */
.metric-updating {
    animation: metricPulse 0.3s ease-in-out;
}

@keyframes metricPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); color: #009ef7; }
    100% { transform: scale(1); }
}

/* Connection Status Indicators */
.admin-toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    max-width: 400px;
}

/* Critical Alerts Animation */
.alert-section .alert {
    animation: slideInDown 0.5s ease-out;
    border-left: 4px solid;
}

.alert-section .alert.bg-light-danger {
    border-left-color: #F1416C;
}

.alert-section .alert.bg-light-warning {
    border-left-color: #FFC700;
}

.alert-section .alert.bg-light-info {
    border-left-color: #009EF7;
}

@keyframes slideInDown {
    from {
        transform: translateY(-20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Priority Badges */
.badge.badge-light-danger {
    background-color: rgba(241, 65, 108, 0.1);
    color: #F1416C;
    border: 1px solid rgba(241, 65, 108, 0.2);
}

.badge.badge-light-warning {
    background-color: rgba(255, 199, 0, 0.1);
    color: #FFC700;
    border: 1px solid rgba(255, 199, 0, 0.2);
}

.badge.badge-light-info {
    background-color: rgba(0, 158, 247, 0.1);
    color: #009EF7;
    border: 1px solid rgba(0, 158, 247, 0.2);
}

/* Action Queue Items */
.action-queue-item {
    transition: all 0.3s ease;
    border-radius: 8px;
}

.action-queue-item:hover {
    background-color: #f8f9fa;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Priority Indicators */
.priority-indicator {
    width: 8px;
    height: 100%;
    border-radius: 4px;
    margin-right: 15px;
}

.priority-indicator.priority-1 {
    background-color: #F1416C;
}

.priority-indicator.priority-2 {
    background-color: #FFC700;
}

.priority-indicator.priority-3 {
    background-color: #009EF7;
}

/* Summary Cards Hover Effects */
.card-flush:hover {
    transform: translateY(-5px);
    transition: transform 0.3s ease;
}

/* Compact Summary Cards */
.summary-card-compact {
    min-height: 140px; /* Reduced from default ~180px */
}

.summary-card-compact .card-header {
    padding-bottom: 0.25rem !important;
}

.summary-card-compact .card-body {
    padding-top: 0.25rem !important;
}

.summary-card-compact .card-title {
    margin-bottom: 0 !important;
}

.summary-card-compact .btn {
    font-size: 0.75rem !important;
    padding: 0.25rem 0.75rem !important;
    line-height: 1.2 !important;
}

.summary-card-compact .border-dashed {
    border-width: 1px !important;
}

/* Action Queue Container */
#action-queue-container {
    overflow: hidden; /* Prevent content from breaking out */
    width: 100%;
    max-width: 100%;
}

/* Override Metronic min-w-750px class for action queue items */
#action-queue-container .min-w-750px,
#action-queue-container [class*="min-w-750px"] {
    min-width: auto !important;
    max-width: 100% !important;
    width: 100% !important;
}

.action-queue-item {
    max-width: 100% !important;
    min-width: auto !important;
    width: 100% !important;
    overflow: hidden;
    word-wrap: break-word;
    box-sizing: border-box;
}

.action-queue-item .flex-grow-1 {
    min-width: 0; /* Allow flex item to shrink below content size */
    overflow: hidden;
}

.action-queue-item .text-gray-800,
.action-queue-item .text-gray-400 {
    word-break: break-word;
    overflow-wrap: break-word;
}

/* Quick Actions Panel */
.quick-actions-panel .symbol:hover {
    transform: scale(1.1);
    transition: transform 0.2s ease;
}

/* Loading States */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

/* DataTable Responsive Optimizations */
.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

/* Price & Coverage DataTable Optimizations */
#kt_table_user_coverages {
    width: 100% !important;
    table-layout: auto;
}

#kt_table_user_coverages th,
#kt_table_user_coverages td {
    white-space: nowrap;
    vertical-align: middle;
}

/* Action column optimization */
#kt_table_user_coverages .btn-icon {
    width: 30px;
    height: 30px;
    padding: 0;
    margin: 0 1px;
}

#kt_table_user_coverages .btn-icon .svg-icon {
    width: 16px;
    height: 16px;
}

/* Responsive column visibility */
@media (max-width: 1199.98px) {
    #kt_table_user_coverages .d-none.d-lg-table-cell {
        display: none !important;
    }
}

@media (max-width: 767.98px) {
    #kt_table_user_coverages .d-none.d-md-table-cell {
        display: none !important;
    }

    /* Compact action buttons on mobile */
    #kt_table_user_coverages .btn-icon {
        width: 28px;
        height: 28px;
    }

    /* Adjust table font size on mobile */
    #kt_table_user_coverages {
        font-size: 0.875rem;
    }
}

/* Desktop optimizations (1920px+) */
@media (min-width: 1920px) {
    #kt_table_user_coverages th,
    #kt_table_user_coverages td {
        padding: 0.75rem 0.5rem;
    }
}

/* Tablet optimizations (768px-1199px) */
@media (min-width: 768px) and (max-width: 1199.98px) {
    #kt_table_user_coverages th,
    #kt_table_user_coverages td {
        padding: 0.5rem 0.25rem;
        font-size: 0.875rem;
    }
}

/* Mobile optimizations (320px-767px) */
@media (max-width: 767.98px) {
    #kt_table_user_coverages th,
    #kt_table_user_coverages td {
        padding: 0.5rem 0.25rem;
        font-size: 0.8rem;
    }

    /* Stack action buttons vertically on very small screens */
    @media (max-width: 480px) {
        #kt_table_user_coverages .d-flex {
            flex-direction: column;
            gap: 2px;
        }

        #kt_table_user_coverages .btn-icon {
            width: 100%;
            height: 26px;
            border-radius: 4px;
        }
    }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    /* Container adjustments */
    .container-xxl {
        padding-left: 15px !important;
        padding-right: 15px !important;
    }

    /* Alert section legacy support */
    .alert-section .alert {
        flex-direction: column;
        text-align: center;
    }

    .alert-section .alert .btn {
        margin-top: 15px;
        margin-left: 0 !important;
    }

    /* Compact Alert Cards Mobile */
    .alert-card .card-header {
        min-height: 40px !important;
        padding: 8px 12px !important;
    }

    .alert-card .symbol-25px {
        width: 25px !important;
        height: 25px !important;
    }

    .alert-card .symbol-25px .symbol-label {
        width: 25px !important;
        height: 25px !important;
    }

    .alert-card h6 {
        font-size: 0.75rem !important;
        line-height: 1.2 !important;
    }

    .alert-card .card-body {
        padding: 8px 12px !important;
    }

    .alert-card .card-body p {
        font-size: 0.7rem !important;
        margin-bottom: 8px !important;
        line-height: 1.3 !important;
    }

    .alert-card .btn {
        font-size: 0.7rem !important;
        padding: 4px 8px !important;
    }

    /* Action Queue Mobile */
    #action-queue-container {
        padding-left: 8px !important;
        padding-right: 8px !important;
        overflow-x: hidden !important;
    }

    .action-queue-item {
        padding: 12px !important;
        margin-bottom: 12px !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
        flex-direction: column;
        align-items: flex-start !important;
        max-width: 100% !important;
        width: 100% !important;
        box-sizing: border-box !important;
    }

    .action-queue-item .d-flex.align-items-center:last-child {
        margin-top: 10px;
        width: 100%;
        justify-content: flex-start;
    }

    .action-queue-item .badge {
        font-size: 0.7rem !important;
        padding: 2px 6px !important;
    }

    .action-queue-item h6,
    .action-queue-item a {
        font-size: 0.875rem !important;
    }

    .action-queue-item .fs-6 {
        font-size: 0.875rem !important;
    }

    .action-queue-item .fs-7 {
        font-size: 0.75rem !important;
    }

    .action-queue-item .fs-8 {
        font-size: 0.7rem !important;
    }

    .action-queue-item .flex-grow-1 {
        width: 100% !important;
        max-width: 100% !important;
        overflow: hidden !important;
    }

    .action-queue-item .d-flex.flex-wrap {
        width: 100% !important;
        margin-top: 8px !important;
    }

    .action-queue-item .btn {
        font-size: 0.75rem !important;
        padding: 6px 12px !important;
        margin-bottom: 4px !important;
    }

    /* Summary Cards Mobile */
    .summary-card-compact {
        min-height: 120px !important; /* Even more compact on mobile */
    }

    .summary-card-compact .card-header {
        padding-top: 8px !important;
        padding-bottom: 4px !important;
    }

    .summary-card-compact .card-body {
        padding-top: 4px !important;
        padding-bottom: 8px !important;
    }

    .summary-card-compact .border-dashed {
        padding: 4px 8px !important;
        min-width: 25px !important;
    }

    .summary-card-compact .btn {
        font-size: 0.7rem !important;
        padding: 0.2rem 0.5rem !important;
    }

    /* Balance Summary Mobile */
    .card[data-bs-theme="light"] .card-header {
        padding-top: 12px !important;
        padding-bottom: 8px !important;
    }

    .card[data-bs-theme="light"] .card-body {
        padding: 8px 12px 12px !important;
    }

    /* Font size adjustments for mobile */
    .fs-1 {
        font-size: 1.5rem !important;
    }

    .fs-2x {
        font-size: 1.25rem !important;
    }

    .fs-3 {
        font-size: 1rem !important;
    }
}

/* Status Indicators */
.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
}

.status-indicator.critical {
    background-color: #F1416C;
    animation: pulse 2s infinite;
}

.status-indicator.warning {
    background-color: #FFC700;
}

.status-indicator.info {
    background-color: #009EF7;
}

.status-indicator.success {
    background-color: #50CD89;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(241, 65, 108, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(241, 65, 108, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(241, 65, 108, 0);
    }
}

/* Dashboard Metrics Animation */
.metric-value {
    animation: countUp 1s ease-out;
}

@keyframes countUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Action Buttons */
.btn-action {
    min-width: 80px;
    font-size: 12px;
    padding: 6px 12px;
}

.btn-action:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Empty State */
.empty-state {
    padding: 60px 20px;
    text-align: center;
}

.empty-state i {
    margin-bottom: 20px;
}

/* Refresh Button Animation */
.btn-refresh:hover i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Card Gradients */
.card-gradient-danger {
    background: linear-gradient(135deg, #F1416C 0%, #E4002B 100%);
}

.card-gradient-warning {
    background: linear-gradient(135deg, #FFC700 0%, #FF8A00 100%);
}

.card-gradient-success {
    background: linear-gradient(135deg, #50CD89 0%, #0F9D58 100%);
}

.card-gradient-info {
    background: linear-gradient(135deg, #009EF7 0%, #0077B6 100%);
}

/* Dashboard Header */
.dashboard-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
}

/* Notification Dot */
.notification-dot {
    position: absolute;
    top: -5px;
    right: -5px;
    width: 12px;
    height: 12px;
    background-color: #F1416C;
    border-radius: 50%;
    border: 2px solid white;
    animation: pulse 2s infinite;
}

/* Medium screens adjustments */
@media (max-width: 992px) and (min-width: 769px) {
    .alert-card .card-header {
        min-height: 55px !important;
    }

    .alert-card h6 {
        font-size: 0.9rem !important;
    }
}

/* Small mobile screens (320px - 375px) */
@media (max-width: 375px) {
    .container-xxl {
        padding-left: 10px !important;
        padding-right: 10px !important;
    }

    .row {
        margin-left: -5px !important;
        margin-right: -5px !important;
    }

    .row > * {
        padding-left: 5px !important;
        padding-right: 5px !important;
    }

    /* Extra compact alert cards */
    .alert-card .card-header {
        min-height: 35px !important;
        padding: 6px 8px !important;
    }

    .alert-card h6 {
        font-size: 0.7rem !important;
    }

    .alert-card .card-body {
        padding: 6px 8px !important;
    }

    .alert-card .card-body p {
        font-size: 0.65rem !important;
    }

    /* Summary cards extra compact */
    .summary-card-compact {
        min-height: 100px !important; /* Ultra compact for small screens */
    }

    .summary-card-compact .fs-2 {
        font-size: 1.1rem !important;
    }

    .summary-card-compact .fs-1 {
        font-size: 1.25rem !important;
    }

    .summary-card-compact .fs-9 {
        font-size: 0.6rem !important;
    }

    .summary-card-compact .fs-8 {
        font-size: 0.65rem !important;
    }

    .summary-card-compact .card-header {
        padding-top: 6px !important;
        padding-bottom: 2px !important;
    }

    .summary-card-compact .card-body {
        padding-top: 2px !important;
        padding-bottom: 6px !important;
    }

    /* Action queue extra compact */
    #action-queue-container {
        padding-left: 4px !important;
        padding-right: 4px !important;
    }

    .action-queue-item {
        padding: 8px !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
        width: 100% !important;
        max-width: 100% !important;
    }

    .action-queue-item .badge {
        font-size: 0.65rem !important;
        padding: 1px 4px !important;
    }

    .action-queue-item .fs-6 {
        font-size: 0.8rem !important;
    }

    .action-queue-item .fs-7 {
        font-size: 0.7rem !important;
    }

    .action-queue-item .fs-8 {
        font-size: 0.65rem !important;
    }

    .action-queue-item .btn {
        font-size: 0.7rem !important;
        padding: 4px 8px !important;
    }
}
