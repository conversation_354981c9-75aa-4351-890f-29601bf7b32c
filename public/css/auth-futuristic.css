/* SMS Provider Futuristic Authentication Styles */

/* Container and Background */
.futuristic-container {
    background: 
        radial-gradient(circle at 20% 80%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(0, 128, 255, 0.1) 0%, transparent 50%),
        linear-gradient(135deg, #0a1428 0%, #1a2b4a 50%, #0f1b2e 100%);
    min-height: 100vh;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding-right: 8%;
}

/* Enhanced starfield with data particles */
.stars {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(2px 2px at 20px 30px, #00ffff, transparent),
        radial-gradient(1px 1px at 40px 70px, rgba(0,255,255,0.8), transparent),
        radial-gradient(1px 1px at 90px 40px, #fff, transparent),
        radial-gradient(2px 2px at 130px 80px, rgba(0,255,255,0.6), transparent),
        radial-gradient(1px 1px at 160px 30px, #00ccff, transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: dataFlow 4s linear infinite;
}

/* Circuit board pattern overlay */
.circuit-overlay {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image: 
        linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px),
        linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    opacity: 0.3;
    animation: circuitPulse 6s ease-in-out infinite;
}

/* Data Stream Particles */
.data-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
}

.data-particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: #00ffff;
    border-radius: 50%;
    box-shadow: 0 0 10px #00ffff;
    animation: particleFlow 8s linear infinite;
}

.data-particle:nth-child(1) { left: 10%; animation-delay: 0s; }
.data-particle:nth-child(2) { left: 20%; animation-delay: 1s; }
.data-particle:nth-child(3) { left: 30%; animation-delay: 2s; }
.data-particle:nth-child(4) { left: 40%; animation-delay: 3s; }
.data-particle:nth-child(5) { left: 50%; animation-delay: 4s; }
.data-particle:nth-child(6) { left: 60%; animation-delay: 5s; }

/* Network Topology Lines */
.network-lines {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
}

.network-line {
    position: absolute;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.6), transparent);
    animation: dataTransmission 4s ease-in-out infinite;
}

.network-line:nth-child(1) {
    top: 20%;
    left: 5%;
    width: 30%;
    animation-delay: 0s;
    transform: rotate(15deg);
}

.network-line:nth-child(2) {
    top: 40%;
    left: 10%;
    width: 25%;
    animation-delay: 1s;
    transform: rotate(-10deg);
}

.network-line:nth-child(3) {
    top: 60%;
    left: 8%;
    width: 35%;
    animation-delay: 2s;
    transform: rotate(20deg);
}

.network-line:nth-child(4) {
    top: 80%;
    left: 15%;
    width: 20%;
    animation-delay: 3s;
    transform: rotate(-15deg);
}

/* SMS Network Tower */
.sms-tower {
    position: absolute;
    left: 8%;
    top: 50%;
    transform: translateY(-50%);
    width: 120px;
    height: 200px;
    background: linear-gradient(to top, #00ffff 0%, #0080ff 50%, #00ffff 100%);
    clip-path: polygon(35% 0%, 65% 0%, 80% 100%, 20% 100%);
    animation: towerGlow 3s ease-in-out infinite;
    filter: drop-shadow(0 0 20px #00ffff);
}

.sms-tower::before {
    content: '';
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 6px;
    height: 25px;
    background: #00ffff;
    box-shadow: 0 0 15px #00ffff;
}

.sms-tower::after {
    content: '';
    position: absolute;
    top: -10px;
    left: 20%;
    width: 60%;
    height: 3px;
    background: #00ffff;
    box-shadow: 0 0 10px #00ffff;
}

/* Signal Waves */
.signal-waves {
    position: absolute;
    left: 8%;
    top: 50%;
    transform: translateY(-50%);
    width: 300px;
    height: 300px;
    pointer-events: none;
}

.signal-wave {
    position: absolute;
    left: 50%;
    top: 30%;
    transform: translate(-50%, -50%);
    border: 2px solid rgba(0, 255, 255, 0.6);
    border-radius: 50%;
    animation: signalPulse 2s ease-out infinite;
}

.signal-wave:nth-child(1) {
    width: 80px;
    height: 80px;
    animation-delay: 0s;
}

.signal-wave:nth-child(2) {
    width: 120px;
    height: 120px;
    animation-delay: 0.5s;
}

.signal-wave:nth-child(3) {
    width: 160px;
    height: 160px;
    animation-delay: 1s;
}

.signal-wave:nth-child(4) {
    width: 200px;
    height: 200px;
    animation-delay: 1.5s;
}

/* Floating Message Bubbles */
.message-bubbles {
    position: absolute;
    left: 20%;
    top: 20%;
    width: 400px;
    height: 400px;
    pointer-events: none;
}

.message-bubble {
    position: absolute;
    background: linear-gradient(135deg, rgba(0, 255, 255, 0.2), rgba(0, 128, 255, 0.1));
    border: 1px solid rgba(0, 255, 255, 0.4);
    border-radius: 20px;
    padding: 8px 12px;
    font-size: 12px;
    color: #00ffff;
    backdrop-filter: blur(10px);
    animation: messageFly 8s linear infinite;
}

.message-bubble::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 20px;
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 8px solid rgba(0, 255, 255, 0.2);
}

.message-bubble:nth-child(1) {
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.message-bubble:nth-child(2) {
    top: 30%;
    left: 60%;
    animation-delay: 2s;
}

.message-bubble:nth-child(3) {
    top: 60%;
    left: 20%;
    animation-delay: 4s;
}

.message-bubble:nth-child(4) {
    top: 80%;
    left: 70%;
    animation-delay: 6s;
}

/* Login Panel */
.login-panel {
    position: absolute;
    right: 8%;
    top: 50%;
    transform: translateY(-50%);
    width: 350px;
    background:
        linear-gradient(135deg, rgba(15, 27, 46, 0.95) 0%, rgba(26, 43, 74, 0.9) 100%),
        radial-gradient(circle at 20% 20%, rgba(0, 255, 255, 0.1) 0%, transparent 50%);
    backdrop-filter: blur(25px);
    border: 1px solid rgba(0, 255, 255, 0.4);
    border-radius: 15px;
    padding: 30px;
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.4),
        0 0 30px rgba(0, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        inset 0 -1px 0 rgba(0, 255, 255, 0.2);
    z-index: 10;
    animation: panelGlow 4s ease-in-out infinite;
}

/* Holographic scan line effect */
.login-panel::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, #00ffff, transparent);
    animation: scanLine 3s ease-in-out infinite;
    opacity: 0.7;
}

/* Typography */
.platform-title {
    color: #00ffff;
    font-size: 22px;
    font-weight: 600;
    font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
    text-align: center;
    margin-bottom: 25px;
    text-shadow:
        0 0 10px rgba(0, 255, 255, 0.8),
        0 0 20px rgba(0, 255, 255, 0.4),
        0 0 30px rgba(0, 255, 255, 0.2);
    letter-spacing: 1px;
    text-transform: uppercase;
    position: relative;
    animation: titleGlow 3s ease-in-out infinite;
    border: 1px solid rgba(0, 255, 255, 0.4);
    padding: 10px 15px;
    border-radius: 10px;
    background:
        linear-gradient(135deg, rgba(0, 255, 255, 0.1) 0%, rgba(0, 128, 255, 0.05) 100%);
    backdrop-filter: blur(10px);
}

.platform-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg, transparent, #00ffff, transparent);
    animation: titleUnderline 2s ease-in-out infinite;
}

/* Login Tabs */
.login-tabs {
    display: flex;
    margin-bottom: 20px;
    border-bottom: 1px solid rgba(0, 255, 255, 0.2);
}

.login-tab {
    flex: 1;
    padding: 8px;
    text-align: center;
    color: rgba(255, 255, 255, 0.6);
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
    font-size: 14px;
}

.login-tab.active {
    color: #00ffff;
    border-bottom-color: #00ffff;
    text-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
}

/* Form Inputs */
.futuristic-input {
    width: 100%;
    background:
        linear-gradient(135deg, rgba(0, 20, 40, 0.8) 0%, rgba(0, 30, 60, 0.6) 100%);
    border: 1px solid rgba(0, 255, 255, 0.4);
    border-radius: 8px;
    padding: 12px 15px;
    color: #ffffff;
    font-size: 14px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin-bottom: 15px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.futuristic-input::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.1), transparent);
    transition: left 0.6s;
}

.futuristic-input:focus::before {
    left: 100%;
}

.futuristic-input:focus {
    outline: none;
    border-color: #00ffff;
    box-shadow:
        0 0 15px rgba(0, 255, 255, 0.4),
        inset 0 0 15px rgba(0, 255, 255, 0.1);
    background:
        linear-gradient(135deg, rgba(0, 30, 60, 0.9) 0%, rgba(0, 40, 80, 0.7) 100%);
    transform: translateY(-1px);
}

.futuristic-input:invalid {
    border-color: #ff4444;
    box-shadow: 0 0 10px rgba(255, 68, 68, 0.3);
}

.futuristic-input:valid {
    border-color: rgba(0, 255, 255, 0.5);
}

.futuristic-input::placeholder {
    color: rgba(0, 255, 255, 0.6);
    font-weight: 300;
}

/* Input Icons */
.input-icon {
    position: relative;
}

.input-icon::before {
    content: "👤";
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(0, 255, 255, 0.6);
    z-index: 1;
    font-size: 14px;
}

.input-icon.password::before {
    content: "🔒";
}

.input-icon.email::before {
    content: "📧";
}

.input-icon.phone::before {
    content: "📱";
}

.input-icon.name::before {
    content: "👤";
}

.input-icon input {
    padding-left: 38px;
}

/* Checkbox Styling */
.terms-checkbox {
    margin-bottom: 15px;
}

.terms-checkbox .form-check-input {
    background: rgba(0, 20, 40, 0.6);
    border: 1px solid rgba(0, 255, 255, 0.3);
    border-radius: 3px;
}

.terms-checkbox .form-check-input:checked {
    background-color: #00ffff;
    border-color: #00ffff;
}

/* Forgot Password Link */
.forgot-password-link {
    text-align: right;
    margin-bottom: 15px;
    margin-top: -5px;
}

.forgot-password-text {
    color: rgba(0, 255, 255, 0.8);
    text-decoration: none;
    font-size: 14px;
    position: relative;
    transition: all 0.3s ease;
}

.forgot-password-text::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 1px;
    background: #00ffff;
    transition: width 0.3s ease;
}

.forgot-password-text:hover {
    color: #00ffff;
    text-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
}

.forgot-password-text:hover::after {
    width: 100%;
}

/* Futuristic Button */
.futuristic-button {
    width: 100%;
    background:
        linear-gradient(135deg, #00ffff 0%, #0080ff 50%, #00ccff 100%);
    border: 1px solid rgba(0, 255, 255, 0.6);
    border-radius: 15px;
    padding: 12px 16px;
    color: #000;
    font-weight: 600;
    font-size: 15px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    margin-top: 20px;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.futuristic-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s;
}

.futuristic-button:hover::before {
    left: 100%;
}

.futuristic-button:hover {
    background:
        linear-gradient(135deg, #0080ff 0%, #00ffff 50%, #0099ff 100%);
    box-shadow:
        0 0 20px rgba(0, 255, 255, 0.6),
        0 0 40px rgba(0, 255, 255, 0.3),
        inset 0 0 15px rgba(255, 255, 255, 0.1);
    transform: translateY(-2px) scale(1.01);
    border-color: #00ffff;
}

.futuristic-button:active {
    transform: translateY(-1px) scale(0.99);
}

/* Verification Code Styling */
.verification-code {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
}

.verification-code input {
    flex: 1;
}

.verification-code button {
    background: rgba(0, 255, 255, 0.1);
    border: 1px solid rgba(0, 255, 255, 0.3);
    color: #00ffff;
    padding: 8px 12px;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    font-size: 12px;
}

.verification-code button:hover {
    background: rgba(0, 255, 255, 0.3);
}

/* Alert Styling */
.alert-danger {
    background: rgba(255, 68, 68, 0.1);
    border: 1px solid rgba(255, 68, 68, 0.3);
    color: #ff6b6b;
    padding: 10px;
    border-radius: 8px;
    margin-top: 10px;
    font-size: 14px;
}

.alert-success {
    background: rgba(0, 255, 255, 0.1);
    border: 1px solid rgba(0, 255, 255, 0.3);
    color: #00ffff;
    padding: 10px;
    border-radius: 8px;
    margin-top: 10px;
    font-size: 14px;
}
