/* SMS Provider Futuristic Authentication Responsive Styles */

/* Tablet and Mobile Responsive Design */
@media (max-width: 768px) {
    /* Hide decorative elements on mobile */
    .sms-tower, 
    .signal-waves, 
    .message-bubbles, 
    .circuit-overlay, 
    .data-particles, 
    .network-lines {
        display: none;
    }

    /* Adjust container for mobile */
    .futuristic-container {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px 0;
        min-height: 100vh;
        padding-right: 0;
    }

    /* Adjust login panel for mobile */
    .login-panel {
        position: relative;
        right: auto;
        top: auto;
        transform: none;
        margin: 20px;
        width: calc(100% - 40px);
        max-width: 350px;
        margin-top: auto;
        margin-bottom: auto;
        padding: 25px;
    }

    /* Adjust platform title for mobile */
    .platform-title {
        font-size: 20px;
        margin-bottom: 20px;
        padding: 8px 12px;
    }

    /* Adjust input styling for mobile */
    .futuristic-input {
        padding: 12px 15px;
        font-size: 16px; /* Prevent zoom on iOS */
        margin-bottom: 15px;
    }

    /* Adjust input icons for mobile */
    .input-icon input {
        padding-left: 35px;
    }

    /* Adjust button for mobile */
    .futuristic-button {
        padding: 12px 15px;
        font-size: 14px;
        margin-top: 15px;
    }
}

/* Small Mobile Devices */
@media (max-width: 480px) {
    /* Further adjust login panel */
    .login-panel {
        margin: 15px;
        width: calc(100% - 30px);
        padding: 20px;
    }

    /* Adjust platform title */
    .platform-title {
        font-size: 18px;
        margin-bottom: 20px;
        letter-spacing: 1px;
        padding: 8px 12px;
    }

    /* Adjust input styling */
    .futuristic-input {
        padding: 10px 12px;
        font-size: 16px;
        margin-bottom: 12px;
    }

    /* Adjust input icons */
    .input-icon input {
        padding-left: 32px;
    }

    .input-icon::before {
        left: 10px;
        font-size: 12px;
    }

    /* Adjust button */
    .futuristic-button {
        padding: 10px 14px;
        font-size: 13px;
        letter-spacing: 0.5px;
    }

    /* Adjust tabs */
    .login-tab {
        padding: 6px;
        font-size: 13px;
    }

    /* Adjust verification code */
    .verification-code {
        flex-direction: column;
        gap: 8px;
    }

    .verification-code button {
        width: 100%;
        padding: 10px;
        font-size: 12px;
    }
}

/* Large Desktop Screens */
@media (min-width: 1200px) {
    /* Enhance effects on larger screens */
    .futuristic-container {
        padding-right: 10%;
    }

    .login-panel {
        width: 380px;
        padding: 35px;
    }

    .platform-title {
        font-size: 24px;
        margin-bottom: 30px;
    }

    .futuristic-input {
        padding: 14px 18px;
        font-size: 15px;
        margin-bottom: 18px;
    }

    .futuristic-button {
        padding: 14px 18px;
        font-size: 16px;
        margin-top: 25px;
    }

    /* Enhance SMS tower on large screens */
    .sms-tower {
        width: 140px;
        height: 220px;
        left: 6%;
    }

    /* Enhance signal waves */
    .signal-waves {
        width: 350px;
        height: 350px;
        left: 6%;
    }

    /* Enhance message bubbles */
    .message-bubbles {
        width: 450px;
        height: 450px;
        left: 18%;
    }
}

/* Ultra-wide Screens */
@media (min-width: 1600px) {
    .futuristic-container {
        padding-right: 12%;
    }

    .login-panel {
        width: 400px;
        padding: 40px;
    }

    .sms-tower {
        width: 160px;
        height: 240px;
        left: 5%;
    }

    .signal-waves {
        width: 400px;
        height: 400px;
        left: 5%;
    }

    .message-bubbles {
        width: 500px;
        height: 500px;
        left: 15%;
    }
}

/* Landscape Mobile Orientation */
@media (max-width: 768px) and (orientation: landscape) {
    .futuristic-container {
        padding: 10px 0;
    }

    .login-panel {
        margin: 10px;
        width: calc(100% - 20px);
        max-width: 400px;
        padding: 20px 25px;
    }

    .platform-title {
        font-size: 18px;
        margin-bottom: 15px;
    }

    .futuristic-input {
        padding: 10px 12px;
        margin-bottom: 12px;
    }

    .futuristic-button {
        padding: 10px 14px;
        margin-top: 12px;
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    /* Enhance visual effects for retina displays */
    .login-panel {
        backdrop-filter: blur(30px);
    }

    .futuristic-input {
        backdrop-filter: blur(15px);
    }

    .platform-title {
        text-shadow: 
            0 0 12px rgba(0, 255, 255, 0.9),
            0 0 24px rgba(0, 255, 255, 0.5),
            0 0 36px rgba(0, 255, 255, 0.3);
    }
}

/* Reduced Motion Preferences */
@media (prefers-reduced-motion: reduce) {
    /* Disable animations for users who prefer reduced motion */
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .stars,
    .circuit-overlay,
    .data-particles,
    .network-lines,
    .sms-tower,
    .signal-waves,
    .message-bubbles,
    .login-panel {
        animation: none !important;
    }
}

/* Print Styles */
@media print {
    .futuristic-container {
        background: white !important;
        color: black !important;
    }

    .login-panel {
        background: white !important;
        border: 1px solid black !important;
        box-shadow: none !important;
        backdrop-filter: none !important;
    }

    .platform-title {
        color: black !important;
        text-shadow: none !important;
    }

    .futuristic-input {
        background: white !important;
        border: 1px solid black !important;
        color: black !important;
    }

    .futuristic-button {
        background: #ccc !important;
        color: black !important;
        border: 1px solid black !important;
    }

    /* Hide decorative elements in print */
    .stars,
    .circuit-overlay,
    .data-particles,
    .network-lines,
    .sms-tower,
    .signal-waves,
    .message-bubbles {
        display: none !important;
    }
}
