"use strict";

// Pop-up Notification Class
var KTPopupNotification = function () {
    var modal;
    var modalElement;
    var isInitialized = false;

    // Check if popup should be shown
    var shouldShowPopup = function() {
        // Check if popup is enabled
        if (!window.popupSettings || !window.popupSettings.enabled) {
            return false;
        }

        // According to requirements: "Display every time user logs in"
        // We'll use sessionStorage to show once per session (which resets on browser close/new login)
        const sessionKey = 'popup_shown_' + (window.popupSettings.id || 'default');
        if (sessionStorage.getItem(sessionKey)) {
            return false;
        }

        return true;
    };

    // Create modal HTML
    var createModalHTML = function() {
        const settings = window.popupSettings;
        
        let fileContent = '';
        // Only show file content if file exists and has valid properties
        if (settings.file && settings.file.url && settings.file.name && settings.file.type) {
            if (settings.file.type === 'pdf') {
                fileContent = `
                    <div class="border rounded p-4 mb-4 bg-light">
                        <div class="d-flex align-items-center">
                            <i class="ki-duotone ki-file-text fs-2x text-primary me-3">
                                <span class="path1"></span>
                                <span class="path2"></span>
                            </i>
                            <div>
                                <div class="fw-bold text-dark">Attached Document</div>
                                <div class="text-muted fs-7">${settings.file.name}</div>
                            </div>
                            <a href="${settings.file.url}" target="_blank" class="btn btn-sm btn-primary ms-auto">
                                <i class="ki-duotone ki-down fs-5">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                </i>
                                Download
                            </a>
                        </div>
                    </div>
                `;
            } else if (settings.file.type === 'image') {
                fileContent = `
                    <div class="text-center mb-4">
                        <div class="border rounded p-3 bg-light">
                            <img src="${settings.file.url}" alt="Notification Image" class="img-fluid rounded" style="max-height: 300px;">
                            <div class="mt-2">
                                <small class="text-muted">${settings.file.name}</small>
                            </div>
                        </div>
                    </div>
                `;
            }
        }

        return `
            <div class="modal fade" id="kt_popup_notification_modal" tabindex="-1" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
                <div class="modal-dialog modal-dialog-centered modal-lg">
                    <div class="modal-content">
                        <div class="modal-header border-0 pb-0">
                            <h3 class="modal-title fw-bold text-dark">${settings.title}</h3>
                            <!-- No close button - modal is mandatory -->
                        </div>
                        <div class="modal-body" style="max-height: 70vh; overflow-y: auto;">
                            <div class="mb-4" style="white-space: pre-line; line-height: 1.6;">
                                ${settings.description}
                            </div>
                            ${fileContent}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-primary" id="popup_agree_btn">
                                <i class="ki-duotone ki-check fs-2">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                </i>
                                Agree
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    };

    // Show popup
    var showPopup = function() {
        if (!shouldShowPopup()) {
            return;
        }

        // Create modal HTML
        const modalHTML = createModalHTML();
        
        // Remove existing modal if any
        const existingModal = document.getElementById('kt_popup_notification_modal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to body
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Get modal element
        modalElement = document.getElementById('kt_popup_notification_modal');

        // Initialize Bootstrap modal with strict blocking behavior
        modal = new bootstrap.Modal(modalElement, {
            backdrop: 'static',  // Prevent closing by clicking backdrop
            keyboard: false,     // Prevent closing with ESC key
            focus: true         // Ensure modal gets focus
        });

        // Prevent any attempts to close the modal except through the Agree button
        modalElement.addEventListener('hide.bs.modal', function(e) {
            // Only allow hiding if explicitly triggered by agree button
            if (!modalElement.dataset.allowClose) {
                e.preventDefault();
                e.stopPropagation();
                return false;
            }
        });

        // Handle agree button click
        const agreeBtn = document.getElementById('popup_agree_btn');
        if (agreeBtn) {
            agreeBtn.addEventListener('click', function() {
                // Mark modal as allowed to close
                modalElement.dataset.allowClose = 'true';
                hidePopup();
            });
        }

        // Show modal
        modal.show();

        // Add additional blocking behavior
        modalElement.addEventListener('shown.bs.modal', function() {
            // Prevent ESC key globally while modal is open
            document.addEventListener('keydown', preventEscapeKey);

            // Ensure modal stays focused
            modalElement.focus();

            // Prevent tab navigation outside modal
            trapFocusInModal(modalElement);
        });

        // Mark as shown in session storage
        const sessionKey = 'popup_shown_' + (window.popupSettings.id || 'default');
        sessionStorage.setItem(sessionKey, 'true');
    };

    // Hide popup
    var hidePopup = function() {
        if (modal) {
            // Remove event listeners
            document.removeEventListener('keydown', preventEscapeKey);

            modal.hide();
        }

        // Remove modal from DOM after hiding
        if (modalElement) {
            modalElement.addEventListener('hidden.bs.modal', function() {
                modalElement.remove();
            });
        }
    };

    // Prevent ESC key from closing modal
    var preventEscapeKey = function(e) {
        if (e.key === 'Escape' || e.keyCode === 27) {
            e.preventDefault();
            e.stopPropagation();
            return false;
        }
    };

    // Trap focus within modal
    var trapFocusInModal = function(modalEl) {
        const focusableElements = modalEl.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );

        if (focusableElements.length === 0) return;

        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];

        modalEl.addEventListener('keydown', function(e) {
            if (e.key === 'Tab') {
                if (e.shiftKey) {
                    if (document.activeElement === firstElement) {
                        e.preventDefault();
                        lastElement.focus();
                    }
                } else {
                    if (document.activeElement === lastElement) {
                        e.preventDefault();
                        firstElement.focus();
                    }
                }
            }
        });

        // Focus the first element
        firstElement.focus();
    };

    // Initialize popup
    var init = function() {
        if (isInitialized) {
            return;
        }

        // Wait for page to be fully loaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                setTimeout(showPopup, 1000); // Show popup 1 second after page load
            });
        } else {
            setTimeout(showPopup, 1000);
        }

        isInitialized = true;
    };

    // Public methods
    return {
        init: init,
        show: showPopup,
        hide: hidePopup
    };
}();

// Auto-initialize when script loads
if (typeof window !== 'undefined') {
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            KTPopupNotification.init();
        });
    } else {
        KTPopupNotification.init();
    }
}
