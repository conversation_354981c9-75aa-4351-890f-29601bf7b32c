/**
 * Admin Notification System
 * Provides toast notifications and alert management for admin dashboard
 */

class AdminNotificationSystem {
    constructor() {
        this.toasts = [];
        this.maxToasts = 5;
        this.defaultDuration = 5000; // 5 seconds
        this.container = null;
        
        this.initializeContainer();
        this.setupStyles();
    }

    /**
     * Initialize the toast container
     */
    initializeContainer() {
        // Check if container already exists
        this.container = document.getElementById('admin-toast-container');
        
        if (!this.container) {
            this.container = document.createElement('div');
            this.container.id = 'admin-toast-container';
            this.container.className = 'admin-toast-container';
            document.body.appendChild(this.container);
        }
    }

    /**
     * Setup CSS styles for notifications
     */
    setupStyles() {
        if (document.getElementById('admin-notification-styles')) return;
        
        const style = document.createElement('style');
        style.id = 'admin-notification-styles';
        style.textContent = `
            .admin-toast-container {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                max-width: 400px;
            }
            
            .admin-toast {
                background: white;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                margin-bottom: 10px;
                padding: 16px;
                border-left: 4px solid;
                display: flex;
                align-items: flex-start;
                animation: slideInRight 0.3s ease-out;
                transition: all 0.3s ease;
                max-width: 100%;
                word-wrap: break-word;
            }
            
            .admin-toast.success {
                border-left-color: #50cd89;
            }
            
            .admin-toast.warning {
                border-left-color: #ffc700;
            }
            
            .admin-toast.error {
                border-left-color: #f1416c;
            }
            
            .admin-toast.info {
                border-left-color: #009ef7;
            }
            
            .admin-toast-icon {
                margin-right: 12px;
                font-size: 20px;
                flex-shrink: 0;
                margin-top: 2px;
            }
            
            .admin-toast-content {
                flex: 1;
                min-width: 0;
            }
            
            .admin-toast-title {
                font-weight: 600;
                font-size: 14px;
                margin-bottom: 4px;
                color: #181c32;
            }
            
            .admin-toast-message {
                font-size: 13px;
                color: #5e6278;
                line-height: 1.4;
            }
            
            .admin-toast-close {
                background: none;
                border: none;
                font-size: 18px;
                color: #a1a5b7;
                cursor: pointer;
                padding: 0;
                margin-left: 12px;
                flex-shrink: 0;
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            
            .admin-toast-close:hover {
                color: #7e8299;
            }
            
            .admin-toast-progress {
                position: absolute;
                bottom: 0;
                left: 0;
                height: 3px;
                background: rgba(0, 0, 0, 0.1);
                border-radius: 0 0 8px 8px;
                transition: width linear;
            }
            
            .admin-toast.success .admin-toast-progress {
                background: #50cd89;
            }
            
            .admin-toast.warning .admin-toast-progress {
                background: #ffc700;
            }
            
            .admin-toast.error .admin-toast-progress {
                background: #f1416c;
            }
            
            .admin-toast.info .admin-toast-progress {
                background: #009ef7;
            }
            
            @keyframes slideInRight {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
            
            @keyframes slideOutRight {
                from {
                    transform: translateX(0);
                    opacity: 1;
                }
                to {
                    transform: translateX(100%);
                    opacity: 0;
                }
            }
            
            .admin-toast.removing {
                animation: slideOutRight 0.3s ease-in forwards;
            }
            
            .metric-updating {
                animation: metricPulse 0.3s ease-in-out;
            }
            
            @keyframes metricPulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.05); color: #009ef7; }
                100% { transform: scale(1); }
            }
        `;
        
        document.head.appendChild(style);
    }

    /**
     * Show a toast notification
     */
    showToast(message, type = 'info', options = {}) {
        const toast = this.createToast(message, type, options);
        this.addToast(toast);
        return toast.id;
    }

    /**
     * Show success notification
     */
    showSuccess(message, options = {}) {
        return this.showToast(message, 'success', options);
    }

    /**
     * Show warning notification
     */
    showWarning(message, options = {}) {
        return this.showToast(message, 'warning', options);
    }

    /**
     * Show error notification
     */
    showError(message, options = {}) {
        return this.showToast(message, 'error', { duration: 8000, ...options });
    }

    /**
     * Show info notification
     */
    showInfo(message, options = {}) {
        return this.showToast(message, 'info', options);
    }

    /**
     * Create a toast element
     */
    createToast(message, type, options) {
        const id = 'toast-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
        const duration = options.duration || this.defaultDuration;
        const title = options.title || this.getDefaultTitle(type);
        const persistent = options.persistent || false;
        
        const toast = {
            id,
            type,
            message,
            title,
            duration,
            persistent,
            element: null,
            progressElement: null,
            timeoutId: null,
            startTime: Date.now()
        };
        
        toast.element = this.createToastElement(toast);
        return toast;
    }

    /**
     * Create the DOM element for a toast
     */
    createToastElement(toast) {
        const element = document.createElement('div');
        element.className = `admin-toast ${toast.type}`;
        element.setAttribute('data-toast-id', toast.id);
        
        const icon = this.getIcon(toast.type);
        
        element.innerHTML = `
            <div class="admin-toast-icon">${icon}</div>
            <div class="admin-toast-content">
                <div class="admin-toast-title">${toast.title}</div>
                <div class="admin-toast-message">${toast.message}</div>
            </div>
            <button class="admin-toast-close" type="button">&times;</button>
            ${!toast.persistent ? '<div class="admin-toast-progress"></div>' : ''}
        `;
        
        // Add close button event
        const closeButton = element.querySelector('.admin-toast-close');
        closeButton.addEventListener('click', () => {
            this.removeToast(toast.id);
        });
        
        // Store progress element reference
        toast.progressElement = element.querySelector('.admin-toast-progress');
        
        return element;
    }

    /**
     * Add toast to container and manage lifecycle
     */
    addToast(toast) {
        // Remove oldest toast if we have too many
        if (this.toasts.length >= this.maxToasts) {
            this.removeToast(this.toasts[0].id);
        }
        
        this.toasts.push(toast);
        this.container.appendChild(toast.element);
        
        // Start auto-remove timer if not persistent
        if (!toast.persistent) {
            this.startProgressAnimation(toast);
            toast.timeoutId = setTimeout(() => {
                this.removeToast(toast.id);
            }, toast.duration);
        }
    }

    /**
     * Start progress bar animation
     */
    startProgressAnimation(toast) {
        if (!toast.progressElement) return;
        
        toast.progressElement.style.width = '100%';
        toast.progressElement.style.transition = `width ${toast.duration}ms linear`;
        
        // Start animation after a small delay to ensure the element is rendered
        setTimeout(() => {
            toast.progressElement.style.width = '0%';
        }, 10);
    }

    /**
     * Remove a toast by ID
     */
    removeToast(toastId) {
        const toastIndex = this.toasts.findIndex(t => t.id === toastId);
        if (toastIndex === -1) return;
        
        const toast = this.toasts[toastIndex];
        
        // Clear timeout
        if (toast.timeoutId) {
            clearTimeout(toast.timeoutId);
        }
        
        // Add removing animation
        toast.element.classList.add('removing');
        
        // Remove from DOM after animation
        setTimeout(() => {
            if (toast.element.parentNode) {
                toast.element.parentNode.removeChild(toast.element);
            }
            this.toasts.splice(toastIndex, 1);
        }, 300);
    }

    /**
     * Clear all toasts
     */
    clearAll() {
        this.toasts.forEach(toast => {
            this.removeToast(toast.id);
        });
    }

    /**
     * Get default title for toast type
     */
    getDefaultTitle(type) {
        const titles = {
            success: 'Success',
            warning: 'Warning',
            error: 'Error',
            info: 'Information'
        };
        return titles[type] || 'Notification';
    }

    /**
     * Get icon for toast type
     */
    getIcon(type) {
        const icons = {
            success: '<i class="ki-duotone ki-check-circle text-success"><span class="path1"></span><span class="path2"></span></i>',
            warning: '<i class="ki-duotone ki-warning-2 text-warning"><span class="path1"></span><span class="path2"></span><span class="path3"></span></i>',
            error: '<i class="ki-duotone ki-cross-circle text-danger"><span class="path1"></span><span class="path2"></span></i>',
            info: '<i class="ki-duotone ki-information text-primary"><span class="path1"></span><span class="path2"></span><span class="path3"></span></i>'
        };
        return icons[type] || icons.info;
    }

    /**
     * Show notification for critical alerts
     */
    showCriticalAlert(alertData) {
        return this.showError(
            `${alertData.title}: ${alertData.message}`,
            {
                title: 'Critical Alert',
                duration: 10000,
                persistent: false
            }
        );
    }

    /**
     * Show notification for system status changes
     */
    showStatusChange(status, message) {
        const type = status === 'online' ? 'success' : 'warning';
        return this.showToast(message, type, {
            title: 'System Status',
            duration: 3000
        });
    }
}
