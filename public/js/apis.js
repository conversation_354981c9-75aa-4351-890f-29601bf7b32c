"use strict";

var token = $("meta[name=csrf-token]").attr("content");

function Request(url, method, data = {}, success = () => {}, error = () => {}, headers = {}) {
    if (method.toUpperCase() === 'POST') {
        data = {
            ...data,
            _token: token
        }
    }

    $.ajax({
        url: url,
        type: method,
        data: data,
        headers: {
            'X-CSRF-TOKEN': token,
            ...headers
        },
        dataType: 'json',
        success: success,
        error: error,
    });
}

// Define a global function for the delete action
$(document).on('click', '.delete-button', function () {
    let endPoint = $(this).attr('data-endpoint');
    Swal.fire({
        text: "Are you sure you would like to delete?",
        icon: "warning",
        showCancelButton: true,
        buttonsStyling: false,
        confirmButtonText: "Yes, delete it!",
        cancelButtonText: "No, return",
        customClass: {
            confirmButton: "btn btn-primary",
            cancelButton: "btn btn-active-light"
        }
    }).then(function (result) {
        if (result.isConfirmed) {
            Request(`${endPoint}`,
                'DELETE',
                {},
                (resp) => {
                    if (!window.dataTable) {
                        window.location.href = window.location.href;
                    }
                    window.dataTable.draw();
                }, (e) => {
                    Swal.fire({
                        text: e.responseJSON.message,
                        icon: "warning"
                    });
                }
            );
        }
    });
});

$(document).on('click', '.delete-button-view', function () {
    let endPoint = $(this).attr('href'); // Use the 'href' attribute for the endpoint
    let redirectUrl = $(this).data('redirect-url'); // Use 'data-redirect-url' attribute for redirection
    Swal.fire({
        text: "Are you sure you would like to delete?",
        icon: "warning",
        showCancelButton: true,
        buttonsStyling: false,
        confirmButtonText: "Yes, delete it!",
        cancelButtonText: "No, return",
        customClass: {
            confirmButton: "btn btn-primary",
            cancelButton: "btn btn-active-light"
        }
    }).then(function (result) {
        if (result.isConfirmed) {
            Request(`${endPoint}`,
                'DELETE',
                {},
                (resp) => {
                    if (resp.success) {
                        window.location.href = redirectUrl; // Redirect to specified route
                    }
                }, (e) => {
                    Swal.fire({
                        text: e.responseJSON.message,
                        icon: "warning"
                    });
                }
            );
        }
    });

    return false; // Prevent default link behavior
});


// Define a global function for the close action
$(document).on('click', '.cancel-button', function (e) {
    e.preventDefault();
    let modelId = $(this).attr('data-model');
    const element = document.getElementById(modelId);
    const form = element.querySelector(`#${modelId}_form`);
    const modal = $(`#${modelId}`)

    Swal.fire({
        text: "Are you sure you would like to cancel?",
        icon: "warning",
        showCancelButton: true,
        buttonsStyling: false,
        confirmButtonText: "Yes, cancel it!",
        cancelButtonText: "No, return",
        customClass: {
            confirmButton: "btn btn-primary",
            cancelButton: "btn btn-active-light"
        }
    }).then(function (result) {
        if (result.value) {
            form.reset();
            modal.modal('hide');
        } else if (result.dismiss === 'cancel') {
            Swal.fire({
                text: "Your form has not been cancelled!.",
                icon: "error",
                buttonsStyling: false,
                confirmButtonText: "Ok, got it!",
                customClass: {
                    confirmButton: "btn btn-primary",
                }
            });
        }
    });
});


$(document).on('click', '.edit-user', function (e) {
    e.preventDefault();
    let endPoint = $(this).attr('data-endpoint');
    let userId = endPoint.split('/').pop(); // Extract user ID from endpoint

    Request(`${endPoint}`,
        'GET',
        {},
        (data) => {
            let user = data.data;

            // Set form action URL for update
            $('#kt_modal_edit_user_form').attr('action', `/users/${userId}`);
            $('#user_id').val(userId);

            // Populate basic fields
            $('#edit_user_name').val(user.name || '');
            $('#edit_user_phone').val(user.phone || '');
            $('#edit_user_email').val(user.email || '');

            // Populate company-related fields if available
            if (user.company) {
                $('#edit_user_balance').val(user.company.current_balance || 0);
                $('#edit_user_min_recharge').val(user.company.minimum_recharge_amount || 0);
            } else {
                $('#edit_user_balance').val(0);
                $('#edit_user_min_recharge').val(0);
            }

            // Handle role selection based on current user permissions
            if (window.isSuperAdmin || window.isMasterReseller) {
                $('#role_selection_row').show();
                const userRoleId = user.roles && user.roles.length > 0 ? user.roles[0].id : null;
                if (userRoleId) {
                    $('#edit_user_role').val(userRoleId);
                }
            } else {
                $('#role_selection_row').hide();
            }

            // Handle status and remarks for super-admin only
            if (window.isSuperAdmin) {
                $('#status_selection_row').show();
                $('#remarks_row').show();
                $('#edit_user_status').val(user.status || 1);
                $('#edit_user_remarks').val(user.remarks || '');
            } else {
                $('#status_selection_row').hide();
                $('#remarks_row').hide();
            }

        }, (e) => {
            Swal.fire({
                text: e.responseJSON.message,
                icon: "warning"
            });
        }
    );
});



// Handle form submission for user update
$(document).on('submit', '#kt_modal_edit_user_form', function (e) {
    e.preventDefault();

    const form = $(this);
    const submitButton = $('#update-user');
    const formData = new FormData(this);

    // Show loading state
    submitButton.attr('data-kt-indicator', 'on');
    submitButton.prop('disabled', true);

    // Clear previous errors
    $('.invalid-feedback').text('');
    $('.is-invalid').removeClass('is-invalid');

    $.ajax({
        url: form.attr('action'),
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        },
        success: function(response) {
            if (response.success) {
                // Hide modal
                $('#kt_modal_edit_user').modal('hide');

                // Show success message
                Swal.fire({
                    text: response.message,
                    icon: "success",
                    buttonsStyling: false,
                    confirmButtonText: "Ok, got it!",
                    customClass: {
                        confirmButton: "btn btn-primary"
                    }
                }).then(function (result) {
                    if (result.isConfirmed) {
                        // Reload the page to show updated data
                        window.location.reload();
                    }
                });
            }
        },
        error: function(xhr) {
            if (xhr.status === 422) {
                // Validation errors
                const errors = xhr.responseJSON.errors;
                Object.keys(errors).forEach(function(field) {
                    const input = $(`[name="${field}"]`);
                    input.addClass('is-invalid');
                    input.siblings('.invalid-feedback').text(errors[field][0]);
                });
            } else {
                // Other errors
                Swal.fire({
                    text: xhr.responseJSON?.message || 'An error occurred while updating the user.',
                    icon: "error",
                    buttonsStyling: false,
                    confirmButtonText: "Ok, got it!",
                    customClass: {
                        confirmButton: "btn btn-primary"
                    }
                });
            }
        },
        complete: function() {
            // Hide loading state
            submitButton.removeAttr('data-kt-indicator');
            submitButton.prop('disabled', false);
        }
    });
});


function showToast(message, icon, timer = 0) {
    Swal.fire({
        text: message,
        icon: icon,
        buttonsStyling: false,
        timer: timer,
        showConfirmButton: timer === 0,
        confirmButtonText: "Ok, got it!",
        customClass: {
            confirmButton: "btn btn-primary"
        }
    });
}
