"use strict";

// Class definition
var KTAdminSettings = function () {
    var form;
    var submitButton;
    var resetButton;
    var validator;

    // Character counter elements
    var characterCounters = {};

    // File upload elements
    var fileUploads = {};

    // Initialize character counters
    var initCharacterCounters = function() {
        const counters = [
            { id: 'meta_description', max: 160 },
            { id: 'og_title', max: 60 },
            { id: 'og_description', max: 160 },
            { id: 'robots_txt', max: 5000 }
        ];

        counters.forEach(function(counter) {
            const element = document.getElementById(counter.id);
            const counterElement = document.getElementById(counter.id + '_counter');
            
            if (element && counterElement) {
                characterCounters[counter.id] = {
                    element: element,
                    counter: counterElement,
                    max: counter.max
                };

                // Update counter on input
                element.addEventListener('input', function() {
                    updateCharacterCounter(counter.id);
                });

                // Initialize counter
                updateCharacterCounter(counter.id);
            }
        });
    };

    // Update character counter
    var updateCharacterCounter = function(id) {
        const config = characterCounters[id];
        if (!config) return;

        const length = config.element.value.length;
        const remaining = config.max - length;
        
        config.counter.textContent = length + '/' + config.max;
        
        // Update counter color based on usage
        config.counter.classList.remove('warning', 'danger');
        if (remaining <= 20) {
            config.counter.classList.add('danger');
        } else if (remaining <= 50) {
            config.counter.classList.add('warning');
        }
    };

    // Initialize file uploads
    var initFileUploads = function() {
        const uploads = [
            { 
                id: 'website_logo', 
                area: 'logo-upload-area', 
                preview: 'logo-preview',
                accept: ['image/jpeg', 'image/jpg', 'image/png', 'image/svg+xml'],
                maxSize: 2 * 1024 * 1024 // 2MB
            },
            { 
                id: 'website_favicon', 
                area: 'favicon-upload-area', 
                preview: 'favicon-preview',
                accept: ['image/x-icon', 'image/png'],
                maxSize: 512 * 1024 // 512KB
            }
        ];

        uploads.forEach(function(upload) {
            initFileUpload(upload);
        });
    };

    // Initialize individual file upload
    var initFileUpload = function(config) {
        const input = document.getElementById(config.id);
        const uploadArea = document.getElementById(config.area);
        const preview = document.getElementById(config.preview);

        if (!input || !uploadArea || !preview) return;

        fileUploads[config.id] = config;

        // Click to upload
        uploadArea.addEventListener('click', function() {
            input.click();
        });

        // File input change
        input.addEventListener('change', function(e) {
            handleFileSelect(e.target.files[0], config);
        });

        // Drag and drop
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFileSelect(files[0], config);
            }
        });
    };

    // Handle file selection
    var handleFileSelect = function(file, config) {
        if (!file) return;

        // Validate file type
        if (!config.accept.includes(file.type)) {
            showToast('Invalid file type. Please select a valid file.', 'error');
            return;
        }

        // Validate file size
        if (file.size > config.maxSize) {
            const maxSizeMB = (config.maxSize / (1024 * 1024)).toFixed(1);
            showToast(`File size too large. Maximum size is ${maxSizeMB}MB.`, 'error');
            return;
        }

        // Update input
        const input = document.getElementById(config.id);
        const dataTransfer = new DataTransfer();
        dataTransfer.items.add(file);
        input.files = dataTransfer.files;

        // Show preview
        showFilePreview(file, config);
    };

    // Show file preview
    var showFilePreview = function(file, config) {
        const preview = document.getElementById(config.preview);
        if (!preview) return;

        const reader = new FileReader();
        reader.onload = function(e) {
            preview.innerHTML = `
                <div class="d-flex align-items-center justify-content-between">
                    <img src="${e.target.result}" alt="Preview" class="me-3" style="max-height: 60px;">
                    <button type="button" class="btn btn-sm btn-light-danger" onclick="clearFilePreview('${config.id}')">
                        <i class="ki-duotone ki-trash fs-6"></i>
                        Remove
                    </button>
                </div>
            `;
            preview.style.display = 'block';
        };
        reader.readAsDataURL(file);
    };

    // Clear file preview
    window.clearFilePreview = function(id) {
        const input = document.getElementById(id);
        const preview = document.getElementById(fileUploads[id].preview);
        
        if (input) input.value = '';
        if (preview) {
            preview.innerHTML = '';
            preview.style.display = 'none';
        }
    };

    // Delete existing file
    window.deleteFile = function(key) {
        if (!confirm('Are you sure you want to delete this file?')) {
            return;
        }

        fetch('/admin/settings/file', {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ key: key })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Hide the current file preview
                const currentPreview = document.getElementById('current-' + key.replace('website_', '') + '-preview');
                if (currentPreview) {
                    currentPreview.style.display = 'none';
                }
                showToast('File deleted successfully', 'success');
            } else {
                showToast(data.message || 'Failed to delete file', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('Failed to delete file', 'error');
        });
    };

    // Initialize form validation
    var initValidation = function() {
        validator = FormValidation.formValidation(
            form,
            {
                fields: {
                    'website_title': {
                        validators: {
                            stringLength: {
                                max: 255,
                                message: 'Website title cannot exceed 255 characters'
                            }
                        }
                    },
                    'meta_description': {
                        validators: {
                            stringLength: {
                                max: 160,
                                message: 'Meta description cannot exceed 160 characters'
                            }
                        }
                    },
                    'meta_keywords': {
                        validators: {
                            stringLength: {
                                max: 500,
                                message: 'Meta keywords cannot exceed 500 characters'
                            }
                        }
                    },
                    'og_title': {
                        validators: {
                            stringLength: {
                                max: 60,
                                message: 'Open Graph title cannot exceed 60 characters'
                            }
                        }
                    },
                    'og_description': {
                        validators: {
                            stringLength: {
                                max: 160,
                                message: 'Open Graph description cannot exceed 160 characters'
                            }
                        }
                    },
                    'google_analytics_id': {
                        validators: {
                            regexp: {
                                regexp: /^(G-|UA-|GT-)[A-Z0-9-]+$/i,
                                message: 'Google Analytics ID format is invalid'
                            }
                        }
                    },
                    'robots_txt': {
                        validators: {
                            stringLength: {
                                max: 5000,
                                message: 'Robots.txt content cannot exceed 5000 characters'
                            }
                        }
                    }
                },
                plugins: {
                    trigger: new FormValidation.plugins.Trigger(),
                    bootstrap: new FormValidation.plugins.Bootstrap5({
                        rowSelector: '.fv-row',
                        eleInvalidClass: '',
                        eleValidClass: ''
                    })
                }
            }
        );
    };

    // Handle form submission
    var handleSubmit = function() {
        submitButton.addEventListener('click', function (e) {
            e.preventDefault();

            // Validate form
            if (validator) {
                validator.validate().then(function (status) {
                    if (status == 'Valid') {
                        // Show loading state
                        submitButton.setAttribute('data-kt-indicator', 'on');
                        submitButton.disabled = true;

                        // Submit form
                        form.submit();
                    } else {
                        showToast('Please correct the errors in the form', 'error');
                    }
                });
            } else {
                // Submit form if no validator
                submitButton.setAttribute('data-kt-indicator', 'on');
                submitButton.disabled = true;
                form.submit();
            }
        });
    };

    // Handle form reset
    var handleReset = function() {
        resetButton.addEventListener('click', function (e) {
            e.preventDefault();
            
            if (confirm('Are you sure you want to reset all changes?')) {
                form.reset();
                
                // Clear file previews
                Object.keys(fileUploads).forEach(function(id) {
                    clearFilePreview(id);
                });
                
                // Reset character counters
                Object.keys(characterCounters).forEach(function(id) {
                    updateCharacterCounter(id);
                });
                
                // Clear validation
                if (validator) {
                    validator.resetForm();
                }
                
                showToast('Form reset successfully', 'info');
            }
        });
    };

    // Show toast notification
    var showToast = function(message, type = 'info') {
        if (typeof toastr !== 'undefined') {
            toastr[type](message);
        } else {
            alert(message);
        }
    };

    // Public methods
    return {
        init: function () {
            form = document.getElementById('kt_settings_form');
            submitButton = document.getElementById('submit-settings');
            resetButton = document.getElementById('reset-form');

            if (!form) {
                return;
            }

            initCharacterCounters();
            initFileUploads();
            initValidation();
            handleSubmit();
            handleReset();
        }
    };
}();

// Bulk Pricing Management Class
var KTBulkPricing = function () {
    var form;
    var previewButton;
    var applyButton;
    var resetButton;
    var previewSection;
    var validator;
    var currentPreviewData = null;
    var currentPreviewPagination = {
        current_page: 1,
        per_page: 25
    };
    var currentUpdatedPagination = {
        current_page: 1,
        per_page: 25
    };

    // Initialize bulk pricing functionality
    var initBulkPricing = function() {
        form = document.getElementById('kt_bulk_pricing_form');
        previewButton = document.getElementById('preview-pricing-changes');
        applyButton = document.getElementById('apply-pricing-changes');
        resetButton = document.getElementById('reset-bulk-pricing-form');
        previewSection = document.getElementById('previewSection');

        if (!form) return;

        // Load filter options on page load
        loadFilterOptions();

        // Initialize event handlers
        initEventHandlers();
        initValidation();
    };

    // Initialize event handlers
    var initEventHandlers = function() {
        // Role filter change
        document.getElementById('targetRoleFilter').addEventListener('change', function() {
            loadAccountOptions(this.value);
            hidePreview();
        });

        // Account filter change
        document.getElementById('targetAccountFilter').addEventListener('change', function() {
            hidePreview();
        });

        // Adjustment type change
        document.getElementById('adjustmentType').addEventListener('change', function() {
            updateAdjustmentUnit(this.value);
            hidePreview();
        });

        // Form input changes
        ['adjustmentValue', 'smsType'].forEach(function(id) {
            document.getElementById(id).addEventListener('change', function() {
                hidePreview();
            });
        });

        // Preview button
        previewButton.addEventListener('click', function(e) {
            e.preventDefault();
            handlePreview();
        });

        // Apply button
        applyButton.addEventListener('click', function(e) {
            e.preventDefault();
            handleApply();
        });

        // Reset button
        resetButton.addEventListener('click', function(e) {
            e.preventDefault();
            handleReset();
        });

        // Back to bulk pricing button
        const backButton = document.getElementById('back-to-bulk-pricing');
        if (backButton) {
            backButton.addEventListener('click', function(e) {
                e.preventDefault();
                resetFormToInitialState();
            });
        }

        // Preview pagination controls
        initPreviewPaginationControls();

        // Updated pricing pagination controls
        initUpdatedPricingPaginationControls();
    };

    // Load filter options
    var loadFilterOptions = function() {
        fetch('/admin/settings/bulk-pricing/filter-options')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Populate role filter (already has options, just ensure they're available)

                    // Populate account filter with all companies initially
                    const accountFilter = document.getElementById('targetAccountFilter');
                    accountFilter.innerHTML = '<option value="">Select Target Account</option><option value="all">All Accounts</option>';

                    Object.keys(data.companies).forEach(function(id) {
                        accountFilter.innerHTML += `<option value="${id}">${data.companies[id]}</option>`;
                    });
                } else {
                    showToast('Failed to load filter options', 'error');
                }
            })
            .catch(error => {
                console.error('Error loading filter options:', error);
                showToast('Failed to load filter options', 'error');
            });
    };

    // Load account options based on role
    var loadAccountOptions = function(roleFilter) {
        const accountFilter = document.getElementById('targetAccountFilter');

        // Show loading state
        accountFilter.innerHTML = '<option value="">Loading...</option>';

        const url = `/admin/settings/bulk-pricing/filter-options?role_filter=${roleFilter}`;

        fetch(url)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    accountFilter.innerHTML = '<option value="">Select Target Account</option><option value="all">All Accounts</option>';

                    Object.keys(data.companies).forEach(function(id) {
                        accountFilter.innerHTML += `<option value="${id}">${data.companies[id]}</option>`;
                    });
                } else {
                    accountFilter.innerHTML = '<option value="">Error loading accounts</option>';
                    showToast('Failed to load accounts', 'error');
                }
            })
            .catch(error => {
                console.error('Error loading accounts:', error);
                accountFilter.innerHTML = '<option value="">Error loading accounts</option>';
                showToast('Failed to load accounts', 'error');
            });
    };

    // Update adjustment unit display
    var updateAdjustmentUnit = function(adjustmentType) {
        const unitElement = document.getElementById('adjustment-unit');
        if (adjustmentType === 'percentage') {
            unitElement.textContent = '%';
        } else if (adjustmentType === 'fixed') {
            unitElement.textContent = '৳';
        } else {
            unitElement.textContent = '';
        }
    };

    // Handle preview
    var handlePreview = function() {
        if (validator) {
            validator.validate().then(function (status) {
                if (status == 'Valid') {
                    performPreview();
                } else {
                    showToast('Please correct the errors in the form', 'error');
                }
            });
        } else {
            performPreview();
        }
    };

    // Perform preview request
    var performPreview = function(page = 1, perPage = 25) {
        // Show loading state
        previewButton.setAttribute('data-kt-indicator', 'on');
        previewButton.disabled = true;

        const formData = new FormData(form);
        formData.append('page', page);
        formData.append('per_page', perPage);

        fetch('/admin/settings/bulk-pricing/preview', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                currentPreviewData = data;
                currentPreviewPagination = data.pagination || { current_page: 1, per_page: 25 };
                displayPreview(data);
                updatePreviewPagination(data.pagination);
                showToast('Preview generated successfully', 'success');
            } else {
                showToast(data.message || 'Failed to generate preview', 'error');
            }
        })
        .catch(error => {
            console.error('Error generating preview:', error);
            showToast('Failed to generate preview', 'error');
        })
        .finally(() => {
            // Hide loading state
            previewButton.removeAttribute('data-kt-indicator');
            previewButton.disabled = false;
        });
    };

    // Display preview data
    var displayPreview = function(data) {
        // Update stats
        document.getElementById('previewCompaniesCount').textContent = data.stats.companies_count;
        document.getElementById('previewCoveragesCount').textContent = data.stats.coverages_count;
        document.getElementById('previewAdjustmentType').textContent = data.stats.adjustment_type;
        document.getElementById('previewAdjustmentValue').textContent = data.stats.adjustment_value;

        // Update preview table
        const tableBody = document.getElementById('previewTableBody');
        tableBody.innerHTML = '';

        data.preview_data.forEach(function(row) {
            const tr = document.createElement('tr');
            tr.innerHTML = `
                <td>${row.company_name}</td>
                <td>${row.prefix}</td>
                <td>${row.operator}</td>
                <td class="${row.masking_changed ? 'text-muted' : ''}">${row.current_masking_price}</td>
                <td class="${row.masking_changed ? 'text-success fw-bold' : ''}">${row.new_masking_price}</td>
                <td class="${row.non_masking_changed ? 'text-muted' : ''}">${row.current_non_masking_price}</td>
                <td class="${row.non_masking_changed ? 'text-success fw-bold' : ''}">${row.new_non_masking_price}</td>
            `;
            tableBody.appendChild(tr);
        });

        // Show preview section and apply button
        previewSection.classList.remove('bulk-pricing-preview-section');
        previewSection.style.display = 'block';
        applyButton.classList.remove('bulk-pricing-apply-button');
        applyButton.style.display = 'inline-block';

        // Show pagination if there's more than one page
        const previewPagination = document.getElementById('previewPagination');
        if (data.pagination && data.pagination.total_pages > 1) {
            previewPagination.classList.add('show');
        } else {
            previewPagination.classList.remove('show');
        }

        // Scroll to preview section
        previewSection.scrollIntoView({ behavior: 'smooth' });
    };

    // Handle apply changes
    var handleApply = function() {
        Swal.fire({
            title: 'Apply Pricing Changes?',
            text: 'Are you sure you want to apply these pricing changes? This action cannot be undone.',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#009ef7',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, Apply Changes',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                // Show loading state
                applyButton.setAttribute('data-kt-indicator', 'on');
                applyButton.disabled = true;

                const formData = new FormData(form);

                fetch('/admin/settings/bulk-pricing/apply', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Swal.fire({
                            title: 'Success!',
                            text: data.message,
                            icon: 'success',
                            confirmButtonColor: '#009ef7'
                        }).then(() => {
                            // Show post-application interface
                            showPostApplicationInterface(data);
                        });
                    } else {
                        Swal.fire({
                            title: 'Error!',
                            text: data.message || 'Failed to apply changes',
                            icon: 'error',
                            confirmButtonColor: '#009ef7'
                        });
                    }
                })
                .catch(error => {
                    console.error('Error applying changes:', error);
                    Swal.fire({
                        title: 'Error!',
                        text: 'Failed to apply changes',
                        icon: 'error',
                        confirmButtonColor: '#009ef7'
                    });
                })
                .finally(() => {
                    // Hide loading state
                    applyButton.removeAttribute('data-kt-indicator');
                    applyButton.disabled = false;
                });
            }
        });
    };

    // Handle form reset (no confirmation needed)
    var handleReset = function() {
        resetFormToInitialState();
    };

    // Reset form to initial state
    var resetFormToInitialState = function() {
        // Reset form data
        form.reset();

        // Hide all sections completely
        hidePreview();
        hidePostApplicationInterface();

        // Reset adjustment unit display
        updateAdjustmentUnit('');

        // Reset validation
        if (validator) {
            validator.resetForm();
        }

        // Show form controls and action buttons
        showFormControls();

        // Hide updated pricing table completely
        const updatedPricingTable = document.getElementById('updatedPricingTable');
        if (updatedPricingTable) {
            updatedPricingTable.classList.add('bulk-pricing-updated-table');
            updatedPricingTable.style.display = 'none';
        }

        // Clear updated pricing table content
        const updatedPricingTableBody = document.getElementById('updatedPricingTableBody');
        if (updatedPricingTableBody) {
            updatedPricingTableBody.innerHTML = '';
        }

        // Hide updated pricing pagination
        const updatedPricingPagination = document.getElementById('updatedPricingPagination');
        if (updatedPricingPagination) {
            updatedPricingPagination.classList.remove('show');
        }

        // Clear preview statistics
        const previewCompaniesCount = document.getElementById('previewCompaniesCount');
        const previewCoveragesCount = document.getElementById('previewCoveragesCount');
        const previewAdjustmentType = document.getElementById('previewAdjustmentType');
        const previewAdjustmentValue = document.getElementById('previewAdjustmentValue');

        if (previewCompaniesCount) previewCompaniesCount.textContent = '0';
        if (previewCoveragesCount) previewCoveragesCount.textContent = '0';
        if (previewAdjustmentType) previewAdjustmentType.textContent = '';
        if (previewAdjustmentValue) previewAdjustmentValue.textContent = '';

        // Clear post-application statistics
        const postAppCompaniesCount = document.getElementById('postAppCompaniesCount');
        const postAppCoveragesCount = document.getElementById('postAppCoveragesCount');

        if (postAppCompaniesCount) postAppCompaniesCount.textContent = '0';
        if (postAppCoveragesCount) postAppCoveragesCount.textContent = '0';

        // Reset pagination states
        currentPreviewPagination = { current_page: 1, per_page: 25 };
        currentUpdatedPagination = { current_page: 1, per_page: 25 };

        // Clear any stored preview data
        currentPreviewData = null;

        // Reset select2 dropdowns to default state
        const targetRoleFilter = document.getElementById('targetRoleFilter');
        const targetAccountFilter = document.getElementById('targetAccountFilter');

        if (targetRoleFilter && targetRoleFilter.tomselect) {
            targetRoleFilter.tomselect.clear();
        }
        if (targetAccountFilter && targetAccountFilter.tomselect) {
            targetAccountFilter.tomselect.clear();
        }
    };

    // Hide preview section
    var hidePreview = function() {
        previewSection.classList.add('bulk-pricing-preview-section');
        previewSection.style.display = 'none';
        applyButton.classList.add('bulk-pricing-apply-button');
        applyButton.style.display = 'none';

        // Hide preview pagination
        const previewPagination = document.getElementById('previewPagination');
        if (previewPagination) {
            previewPagination.classList.remove('show');
        }

        // Clear preview table content
        const previewTableBody = document.getElementById('previewTableBody');
        if (previewTableBody) {
            previewTableBody.innerHTML = '';
        }
    };

    // Show post-application interface
    var showPostApplicationInterface = function(data) {
        // Hide form controls
        hideFormControls();

        // Hide preview section
        hidePreview();

        // Hide all form action buttons
        hideFormActionButtons();

        // Show post-application section
        const postApplicationSection = document.getElementById('postApplicationSection');
        if (postApplicationSection) {
            postApplicationSection.classList.remove('bulk-pricing-post-application');
            postApplicationSection.style.display = 'block';

            // Update statistics
            if (data.stats) {
                document.getElementById('postAppCompaniesCount').textContent = data.stats.companies_affected || 0;
                document.getElementById('postAppCoveragesCount').textContent = data.stats.coverages_updated || 0;
            }

            // Load updated pricing data with pagination
            loadUpdatedPricingData(1, 25);

            // Scroll to post-application section
            postApplicationSection.scrollIntoView({ behavior: 'smooth' });
        }
    };

    // Hide post-application interface
    var hidePostApplicationInterface = function() {
        const postApplicationSection = document.getElementById('postApplicationSection');
        if (postApplicationSection) {
            postApplicationSection.classList.add('bulk-pricing-post-application');
            postApplicationSection.style.display = 'none';
        }
    };

    // Hide form controls
    var hideFormControls = function() {
        const formControlsSection = document.getElementById('formControlsSection');
        if (formControlsSection) {
            formControlsSection.classList.add('bulk-pricing-hidden');
        }
    };

    // Show form controls
    var showFormControls = function() {
        const formControlsSection = document.getElementById('formControlsSection');
        if (formControlsSection) {
            formControlsSection.classList.remove('bulk-pricing-hidden');
            formControlsSection.style.display = 'block';
        }

        // Also show form action buttons
        showFormActionButtons();
    };

    // Hide form action buttons
    var hideFormActionButtons = function() {
        const formActionsSection = document.getElementById('formActionsSection');
        if (formActionsSection) {
            formActionsSection.classList.add('bulk-pricing-hidden');
        }
    };

    // Show form action buttons
    var showFormActionButtons = function() {
        const formActionsSection = document.getElementById('formActionsSection');
        if (formActionsSection) {
            formActionsSection.classList.remove('bulk-pricing-hidden');
            formActionsSection.style.display = 'block';
        }
    };



    // Initialize form validation
    var initValidation = function() {
        validator = FormValidation.formValidation(
            form,
            {
                fields: {
                    'target_role': {
                        validators: {
                            notEmpty: {
                                message: 'Target role is required'
                            }
                        }
                    },
                    'target_account': {
                        validators: {
                            notEmpty: {
                                message: 'Target account is required'
                            }
                        }
                    },
                    'adjustment_type': {
                        validators: {
                            notEmpty: {
                                message: 'Adjustment type is required'
                            }
                        }
                    },
                    'adjustment_value': {
                        validators: {
                            notEmpty: {
                                message: 'Adjustment value is required'
                            },
                            numeric: {
                                message: 'Adjustment value must be a number'
                            },
                            between: {
                                min: -100,
                                max: 1000,
                                message: 'Adjustment value must be between -100 and 1000'
                            }
                        }
                    },
                    'sms_type': {
                        validators: {
                            notEmpty: {
                                message: 'SMS type is required'
                            }
                        }
                    }
                },
                plugins: {
                    trigger: new FormValidation.plugins.Trigger(),
                    bootstrap: new FormValidation.plugins.Bootstrap5({
                        rowSelector: '.fv-row',
                        eleInvalidClass: '',
                        eleValidClass: ''
                    })
                }
            }
        );
    };

    // Show toast notification
    var showToast = function(message, type = 'info') {
        if (typeof toastr !== 'undefined') {
            toastr[type](message);
        } else {
            alert(message);
        }
    };

    // Initialize preview pagination controls
    var initPreviewPaginationControls = function() {
        // Per page selector
        const previewPerPage = document.getElementById('previewPerPage');
        if (previewPerPage) {
            previewPerPage.addEventListener('change', function() {
                currentPreviewPagination.per_page = parseInt(this.value);
                currentPreviewPagination.current_page = 1;
                performPreview(1, parseInt(this.value));
            });
        }

        // Previous page button
        const previewPrevPage = document.getElementById('previewPrevPage');
        if (previewPrevPage) {
            previewPrevPage.addEventListener('click', function() {
                if (currentPreviewPagination.current_page > 1) {
                    performPreview(currentPreviewPagination.current_page - 1, currentPreviewPagination.per_page);
                }
            });
        }

        // Next page button
        const previewNextPage = document.getElementById('previewNextPage');
        if (previewNextPage) {
            previewNextPage.addEventListener('click', function() {
                if (currentPreviewData && currentPreviewData.pagination && currentPreviewData.pagination.has_next_page) {
                    performPreview(currentPreviewPagination.current_page + 1, currentPreviewPagination.per_page);
                }
            });
        }
    };

    // Update preview pagination controls
    var updatePreviewPagination = function(pagination) {
        if (!pagination) return;

        // Update pagination info
        const previewPaginationInfo = document.getElementById('previewPaginationInfo');
        if (previewPaginationInfo) {
            previewPaginationInfo.textContent = `Showing ${pagination.from} to ${pagination.to} of ${pagination.total} entries`;
        }

        // Update previous button state
        const previewPrevPage = document.getElementById('previewPrevPage');
        if (previewPrevPage) {
            previewPrevPage.disabled = !pagination.has_prev_page;
        }

        // Update next button state
        const previewNextPage = document.getElementById('previewNextPage');
        if (previewNextPage) {
            previewNextPage.disabled = !pagination.has_next_page;
        }

        // Update per page selector
        const previewPerPage = document.getElementById('previewPerPage');
        if (previewPerPage) {
            previewPerPage.value = pagination.per_page;
        }
    };

    // Initialize updated pricing pagination controls
    var initUpdatedPricingPaginationControls = function() {
        // Per page selector
        const updatedPricingPerPage = document.getElementById('updatedPricingPerPage');
        if (updatedPricingPerPage) {
            updatedPricingPerPage.addEventListener('change', function() {
                currentUpdatedPagination.per_page = parseInt(this.value);
                currentUpdatedPagination.current_page = 1;
                loadUpdatedPricingData(1, parseInt(this.value));
            });
        }

        // Previous page button
        const updatedPricingPrevPage = document.getElementById('updatedPricingPrevPage');
        if (updatedPricingPrevPage) {
            updatedPricingPrevPage.addEventListener('click', function() {
                if (currentUpdatedPagination.current_page > 1) {
                    loadUpdatedPricingData(currentUpdatedPagination.current_page - 1, currentUpdatedPagination.per_page);
                }
            });
        }

        // Next page button
        const updatedPricingNextPage = document.getElementById('updatedPricingNextPage');
        if (updatedPricingNextPage) {
            updatedPricingNextPage.addEventListener('click', function() {
                loadUpdatedPricingData(currentUpdatedPagination.current_page + 1, currentUpdatedPagination.per_page);
            });
        }
    };

    // Load updated pricing data with pagination
    var loadUpdatedPricingData = function(page = 1, perPage = 25) {
        fetch(`/admin/settings/bulk-pricing/updated-data?page=${page}&per_page=${perPage}`, {
            method: 'GET',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                currentUpdatedPagination = data.pagination;
                displayUpdatedPricingDataPaginated(data.data);
                updateUpdatedPricingPagination(data.pagination);
            } else {
                showToast(data.message || 'Failed to load updated pricing data', 'error');
            }
        })
        .catch(error => {
            console.error('Error loading updated pricing data:', error);
            showToast('Failed to load updated pricing data', 'error');
        });
    };

    // Display updated pricing data (paginated version)
    var displayUpdatedPricingDataPaginated = function(data) {
        const updatedPricingTableBody = document.getElementById('updatedPricingTableBody');
        if (!updatedPricingTableBody || !data) {
            return;
        }

        // Clear existing content
        updatedPricingTableBody.innerHTML = '';

        // Populate with updated pricing data
        data.forEach(function(row) {
            const tr = document.createElement('tr');
            tr.innerHTML = `
                <td class="fw-semibold text-gray-800">${row.company_name}</td>
                <td class="text-gray-600">${row.prefix}</td>
                <td class="text-gray-600">${row.operator}</td>
                <td class="fw-bold text-primary">৳${row.new_masking_price}</td>
                <td class="fw-bold text-primary">৳${row.new_non_masking_price}</td>
            `;
            updatedPricingTableBody.appendChild(tr);
        });

        // Show the updated pricing table
        const updatedPricingTable = document.getElementById('updatedPricingTable');
        if (updatedPricingTable) {
            updatedPricingTable.classList.remove('bulk-pricing-updated-table');
            updatedPricingTable.style.display = 'block';
        }
    };

    // Update updated pricing pagination controls
    var updateUpdatedPricingPagination = function(pagination) {
        if (!pagination) return;

        // Update pagination info
        const updatedPricingPaginationInfo = document.getElementById('updatedPricingPaginationInfo');
        if (updatedPricingPaginationInfo) {
            updatedPricingPaginationInfo.textContent = `Showing ${pagination.from} to ${pagination.to} of ${pagination.total} entries`;
        }

        // Update previous button state
        const updatedPricingPrevPage = document.getElementById('updatedPricingPrevPage');
        if (updatedPricingPrevPage) {
            updatedPricingPrevPage.disabled = !pagination.has_prev_page;
        }

        // Update next button state
        const updatedPricingNextPage = document.getElementById('updatedPricingNextPage');
        if (updatedPricingNextPage) {
            updatedPricingNextPage.disabled = !pagination.has_next_page;
        }

        // Update per page selector
        const updatedPricingPerPage = document.getElementById('updatedPricingPerPage');
        if (updatedPricingPerPage) {
            updatedPricingPerPage.value = pagination.per_page;
        }

        // Show pagination if there's more than one page
        const updatedPricingPagination = document.getElementById('updatedPricingPagination');
        if (updatedPricingPagination) {
            if (pagination.total_pages > 1) {
                updatedPricingPagination.classList.add('show');
            } else {
                updatedPricingPagination.classList.remove('show');
            }
        }
    };

    // Public methods
    return {
        init: function () {
            initBulkPricing();
        }
    };
}();

// Pop-up Settings Management Class
var KTPopupSettings = function () {
    var form;
    var submitButton;
    var resetButton;
    var validator;

    // Initialize pop-up settings
    var initPopupSettings = function() {
        form = document.getElementById('kt_popup_settings_form');
        if (!form) {
            return;
        }

        submitButton = document.getElementById('submit-popup-settings');
        resetButton = document.getElementById('reset-popup-form');

        initValidation();
        handleSubmit();
        handleReset();
    };

    // Initialize validation
    var initValidation = function() {
        if (!form) return;

        validator = FormValidation.formValidation(form, {
            fields: {
                popup_title: {
                    validators: {
                        notEmpty: {
                            message: 'Pop-up title is required'
                        },
                        stringLength: {
                            max: 255,
                            message: 'Pop-up title cannot exceed 255 characters'
                        }
                    }
                },
                popup_description: {
                    validators: {
                        notEmpty: {
                            message: 'Pop-up description is required'
                        },
                        stringLength: {
                            max: 10000,
                            message: 'Pop-up description cannot exceed 10,000 characters'
                        }
                    }
                }
            },
            plugins: {
                trigger: new FormValidation.plugins.Trigger(),
                bootstrap: new FormValidation.plugins.Bootstrap5({
                    rowSelector: '.fv-row',
                    eleInvalidClass: '',
                    eleValidClass: ''
                })
            }
        });
    };

    // Handle form submission
    var handleSubmit = function() {
        if (!submitButton) return;

        submitButton.addEventListener('click', function(e) {
            e.preventDefault();

            if (validator) {
                validator.validate().then(function(status) {
                    if (status === 'Valid') {
                        // Show loading
                        submitButton.setAttribute('data-kt-indicator', 'on');
                        submitButton.disabled = true;

                        // Submit form
                        form.submit();
                    }
                });
            } else {
                // Show loading
                submitButton.setAttribute('data-kt-indicator', 'on');
                submitButton.disabled = true;

                // Submit form
                form.submit();
            }
        });
    };

    // Handle form reset
    var handleReset = function() {
        if (!resetButton) return;

        resetButton.addEventListener('click', function(e) {
            e.preventDefault();

            // Reset form
            form.reset();

            // Reset validation
            if (validator) {
                validator.resetForm();
            }
        });
    };

    // Public methods
    return {
        init: function () {
            initPopupSettings();
        }
    };
}();

// On document ready
KTUtil.onDOMContentLoaded(function () {
    KTAdminSettings.init();
    KTBulkPricing.init();
    KTPopupSettings.init();
});
