/**
 * DataTable Initialization and Coverage Management
 * Centralized JavaScript for user coverage management functionality
 */

// Global variables
var coverageTable;
var rechargeHistoryTable;

$(document).ready(function() {
    // Initialize DataTable for coverage
    initializeCoverageDataTable();

    // Initialize DataTable for recharge history
    initializeRechargeHistoryDataTable();

    // Initialize event handlers
    initializeCoverageEventHandlers();
    initializeModalEventHandlers();
    initializeFormValidation();
    initializeUserStatusToggle();
    initializeRechargeHistoryEventHandlers();
});

/**
 * Initialize Coverage DataTable
 */
function initializeCoverageDataTable() {
    // Get user ID from data attribute or global variable
    var userId = $('#kt_table_user_coverages').data('user-id') || window.userId;

    if (!userId) {
        console.error('User ID not found for DataTable initialization');
        return;
    }

    coverageTable = $('#kt_table_user_coverages').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: window.routes.coverageData,
            type: 'GET',
            error: function(xhr, error, code) {
                console.log('DataTables Ajax Error:', error);
                Swal.fire({
                    text: "Error loading coverage data. Please refresh the page.",
                    icon: "error",
                    buttonsStyling: false,
                    confirmButtonText: "Ok, got it!",
                    customClass: {
                        confirmButton: "btn btn-primary"
                    }
                });
            }
        },
        columns: [
            {
                data: 'company_name',
                name: 'company_name',
                className: 'text-nowrap',
                responsivePriority: 3
            },
            {
                data: 'operator',
                name: 'operator',
                className: 'text-nowrap',
                responsivePriority: 1
            },
            {
                data: 'prefix',
                name: 'prefix',
                width: '80px',
                className: 'text-center',
                responsivePriority: 1
            },
            {
                data: 'masking_price_formatted',
                name: 'masking_price',
                width: '100px',
                className: 'text-end',
                responsivePriority: 4
            },
            {
                data: 'non_masking_price_formatted',
                name: 'non_masking_price',
                width: '110px',
                className: 'text-end',
                responsivePriority: 5
            },
            {
                data: 'status_badge',
                name: 'status',
                width: '80px',
                className: 'text-center',
                responsivePriority: 3
            },
            {
                data: 'action',
                name: 'action',
                orderable: false,
                searchable: false,
                width: '80px',
                className: 'text-end',
                responsivePriority: 1
            }
        ],
        order: [[2, 'asc']], // Order by prefix
        pageLength: 10,
        responsive: {
            details: {
                type: 'column',
                target: 'tr'
            }
        },
        columnDefs: [
            {
                targets: -1,
                orderable: false,
                className: 'text-end'
            },
            {
                targets: [3, 4],
                className: 'text-end text-nowrap'
            },
            {
                targets: [2, 5],
                className: 'text-center'
            }
        ],
        language: {
            emptyTable: "No coverage data available for this user's company",
            processing: "Loading coverage data..."
        },
        drawCallback: function(settings) {
            // Initialize tooltips after table draw
            $('[title]').tooltip();
        }
    });
}

/**
 * Initialize Recharge History DataTable
 */
function initializeRechargeHistoryDataTable() {
    // Check if recharge history table exists
    if (!$('#kt_table_user_recharge_history').length) {
        return;
    }

    // Get user ID from data attribute or global variable
    var userId = $('#kt_table_user_recharge_history').data('user-id') || window.userId;

    if (!userId) {
        console.error('User ID not found for Recharge History DataTable initialization');
        return;
    }

    rechargeHistoryTable = $('#kt_table_user_recharge_history').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: window.routes.rechargeHistoryData,
            type: 'GET',
            error: function(xhr, error, code) {
                console.log('DataTables Ajax Error:', error);
                Swal.fire({
                    text: "Error loading recharge history data. Please refresh the page.",
                    icon: "error",
                    buttonsStyling: false,
                    confirmButtonText: "Ok, got it!",
                    customClass: {
                        confirmButton: "btn btn-primary"
                    }
                });
            }
        },
        columns: [
            {
                data: 'recharge_date',
                name: 'recharge_date',
                className: 'text-nowrap',
                responsivePriority: 1
            },
            {
                data: 'gateway',
                name: 'gateway',
                className: 'text-nowrap',
                responsivePriority: 2
            },
            {
                data: 'transaction_id',
                name: 'transaction_id',
                className: 'text-nowrap',
                responsivePriority: 4
            },
            {
                data: 'amount',
                name: 'amount',
                className: 'text-end text-nowrap',
                responsivePriority: 1
            },
            {
                data: 'status',
                name: 'status',
                className: 'text-center',
                responsivePriority: 3
            },
            {
                data: 'remarks',
                name: 'remarks',
                className: 'text-nowrap',
                responsivePriority: 5
            },
            {
                data: 'action',
                name: 'action',
                orderable: false,
                searchable: false,
                className: 'text-end',
                responsivePriority: 1
            }
        ],
        responsive: {
            details: {
                type: 'column',
                target: 'tr'
            }
        },
        columnDefs: [
            {
                targets: [3],
                className: 'text-end text-nowrap'
            },
            {
                targets: [4],
                className: 'text-center'
            },
            {
                targets: -1,
                orderable: false,
                className: 'text-end'
            }
        ],
        language: {
            emptyTable: "No recharge history found for this user"
        },
        drawCallback: function(settings) {
            // Initialize tooltips after table draw
            $('[title]').tooltip();
        }
    });
}

/**
 * Initialize Coverage Event Handlers
 */
function initializeCoverageEventHandlers() {
    // Add Coverage Form Submit
    $('#add-coverage-form').on('submit', handleAddCoverageSubmit);
    
    // Edit Coverage Button Click
    $(document).on('click', '.edit-coverage-btn', handleEditCoverageClick);
    
    // Edit Coverage Form Submit
    $('#edit-coverage-form').on('submit', handleEditCoverageSubmit);
    
    // Delete Coverage Button Click
    $(document).on('click', '.delete-coverage-btn', handleDeleteCoverageClick);
}

/**
 * Handle Add Coverage Form Submit
 */
function handleAddCoverageSubmit(e) {
    e.preventDefault();

    var userId = window.userId;
    if (!userId) {
        console.error('User ID not found for add coverage');
        return;
    }

    // Clear previous validation errors
    $(this).find('.is-invalid').removeClass('is-invalid');
    $(this).find('.invalid-feedback').remove();

    var formData = $(this).serialize();
    var submitBtn = $(this).find('button[type="submit"]');

    // Show loading state
    submitBtn.attr('disabled', true);
    submitBtn.find('.indicator-label').hide();
    submitBtn.find('.indicator-progress').show();

    $.ajax({
        url: window.routes.coverageStore,
        type: 'POST',
        data: formData,
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                $('#kt_modal_add_coverage').modal('hide');
                $('#add-coverage-form')[0].reset();
                coverageTable.ajax.reload(null, false); // Keep current page

                // Show success message
                Swal.fire({
                    text: response.message,
                    icon: "success",
                    buttonsStyling: false,
                    confirmButtonText: "Ok, got it!",
                    customClass: {
                        confirmButton: "btn btn-primary"
                    }
                });
            }
        },
        error: function(xhr) {
            handleFormError(xhr, 'An error occurred while adding coverage');
        },
        complete: function() {
            // Reset button state
            submitBtn.attr('disabled', false);
            submitBtn.find('.indicator-label').show();
            submitBtn.find('.indicator-progress').hide();
        }
    });
}

/**
 * Handle Edit Coverage Button Click
 */
function handleEditCoverageClick() {
    var coverageId = $(this).data('coverage-id');
    var userId = window.userId;

    if (!userId || !coverageId) {
        console.error('User ID or Coverage ID not found');
        return;
    }

    $.ajax({
        url: window.routes.coverageShow.replace(':userId', userId).replace(':coverageId', coverageId),
        type: 'GET',
        success: function(response) {
            if (response.success) {
                var coverage = response.data;

                // Populate edit form with fresh data
                $('#edit-coverage-form input[name="coverage_id"]').val(coverage.id);
                $('#edit-coverage-form select[name="operator"]').val(coverage.operator);
                $('#edit-coverage-form select[name="prefix"]').val(coverage.prefix);
                $('#edit-coverage-form input[name="masking_price"]').val(coverage.masking_price);
                $('#edit-coverage-form input[name="non_masking_price"]').val(coverage.non_masking_price);
                $('#edit-coverage-form input[name="status"][value="' + coverage.status + '"]').prop('checked', true);

                // Trigger button styling update for radio buttons
                $('#edit-coverage-form input[name="status"]:checked').closest('label').addClass('active');
                $('#edit-coverage-form input[name="status"]:not(:checked)').closest('label').removeClass('active');
            } else {
                Swal.fire({
                    text: response.message || 'Error loading coverage data',
                    icon: "error",
                    buttonsStyling: false,
                    confirmButtonText: "Ok, got it!",
                    customClass: {
                        confirmButton: "btn btn-primary"
                    }
                });
            }
        },
        error: function(xhr) {
            var errorMessage = xhr.responseJSON?.message || 'Error loading coverage data';
            Swal.fire({
                text: errorMessage,
                icon: "error",
                buttonsStyling: false,
                confirmButtonText: "Ok, got it!",
                customClass: {
                    confirmButton: "btn btn-primary"
                }
            });
        }
    });
}

/**
 * Handle Edit Coverage Form Submit
 */
function handleEditCoverageSubmit(e) {
    e.preventDefault();

    var userId = window.userId;
    if (!userId) {
        console.error('User ID not found for edit coverage');
        return;
    }

    // Clear previous validation errors
    $(this).find('.is-invalid').removeClass('is-invalid');
    $(this).find('.invalid-feedback').remove();

    var coverageId = $(this).find('input[name="coverage_id"]').val();
    var formData = $(this).serialize();
    var submitBtn = $(this).find('button[type="submit"]');

    // Show loading state
    submitBtn.attr('disabled', true);
    submitBtn.find('.indicator-label').hide();
    submitBtn.find('.indicator-progress').show();

    $.ajax({
        url: window.routes.coverageUpdate.replace(':userId', userId).replace(':coverageId', coverageId),
        type: 'PUT',
        data: formData,
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                $('#kt_modal_edit_coverage').modal('hide');
                coverageTable.ajax.reload(null, false); // Keep current page

                // Show success message
                Swal.fire({
                    text: response.message,
                    icon: "success",
                    buttonsStyling: false,
                    confirmButtonText: "Ok, got it!",
                    customClass: {
                        confirmButton: "btn btn-primary"
                    }
                });
            }
        },
        error: function(xhr) {
            handleFormError(xhr, 'An error occurred while updating coverage');
        },
        complete: function() {
            // Reset button state
            submitBtn.attr('disabled', false);
            submitBtn.find('.indicator-label').show();
            submitBtn.find('.indicator-progress').hide();
        }
    });
}

/**
 * Handle Delete Coverage Button Click
 */
function handleDeleteCoverageClick() {
    var coverageId = $(this).data('coverage-id');
    var userId = window.userId;
    var button = $(this);

    if (!userId || !coverageId) {
        console.error('User ID or Coverage ID not found');
        return;
    }

    Swal.fire({
        title: "Are you sure?",
        text: "You won't be able to revert this coverage deletion!",
        icon: "warning",
        showCancelButton: true,
        buttonsStyling: false,
        confirmButtonText: "Yes, delete it!",
        cancelButtonText: "No, cancel",
        customClass: {
            confirmButton: "btn fw-bold btn-danger",
            cancelButton: "btn fw-bold btn-active-light-primary"
        }
    }).then(function (result) {
        if (result.value) {
            // Disable button during deletion
            button.attr('disabled', true);

            $.ajax({
                url: window.routes.coverageDestroy.replace(':userId', userId).replace(':coverageId', coverageId),
                type: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.success) {
                        coverageTable.ajax.reload(null, false); // Keep current page

                        Swal.fire({
                            title: "Deleted!",
                            text: response.message,
                            icon: "success",
                            buttonsStyling: false,
                            confirmButtonText: "Ok, got it!",
                            customClass: {
                                confirmButton: "btn btn-primary"
                            }
                        });
                    }
                },
                error: function(xhr) {
                    var errorMessage = xhr.responseJSON?.message || "An error occurred while deleting the coverage.";

                    Swal.fire({
                        title: "Error!",
                        text: errorMessage,
                        icon: "error",
                        buttonsStyling: false,
                        confirmButtonText: "Ok, got it!",
                        customClass: {
                            confirmButton: "btn btn-primary"
                        }
                    });
                },
                complete: function() {
                    // Re-enable button
                    button.attr('disabled', false);
                }
            });
        }
    });
}

/**
 * Initialize Modal Event Handlers
 */
function initializeModalEventHandlers() {
    // Reset edit modal when it's shown
    $('#kt_modal_edit_coverage').on('show.bs.modal', function() {
        // Clear form data
        $('#edit-coverage-form')[0].reset();
        $('#edit-coverage-form .is-invalid').removeClass('is-invalid');
        $('#edit-coverage-form .invalid-feedback').remove();

        // Reset radio button styling
        $('#edit-coverage-form input[name="status"]').closest('label').removeClass('active');
    });

    // Modal close functionality
    $('[data-kt-users-modal-action="close"]').on('click', function() {
        $(this).closest('.modal').modal('hide');
    });

    $('[data-kt-users-modal-action="cancel"]').on('click', function() {
        $(this).closest('.modal').modal('hide');
    });

    // Reset forms when modals are hidden
    $('#kt_modal_add_coverage').on('hidden.bs.modal', function() {
        $('#add-coverage-form')[0].reset();
        // Reset radio button styling
        $('#add-coverage-form input[name="status"][value="enabled"]').prop('checked', true);
        $('#add-coverage-form input[name="status"][value="enabled"]').closest('label').addClass('active');
        $('#add-coverage-form input[name="status"][value="disabled"]').closest('label').removeClass('active');
    });

    $('#kt_modal_edit_coverage').on('hidden.bs.modal', function() {
        $('#edit-coverage-form')[0].reset();
        // Clear validation errors
        $('#edit-coverage-form').find('.is-invalid').removeClass('is-invalid');
        $('#edit-coverage-form').find('.invalid-feedback').remove();
    });
}

/**
 * Initialize Form Validation
 */
function initializeFormValidation() {
    // Form validation on input change
    $(document).on('input change', 'input[name="masking_price"], input[name="non_masking_price"]', function() {
        var value = parseFloat($(this).val());
        if (value < 0) {
            $(this).val(0);
        }
    });

    // Clear validation errors on input focus
    $(document).on('focus', '.form-control', function() {
        $(this).removeClass('is-invalid');
        $(this).siblings('.invalid-feedback').remove();
    });
}

/**
 * Initialize User Status Toggle
 */
function initializeUserStatusToggle() {
    $('#user_status_toggle').on('change', function() {
        var isChecked = $(this).is(':checked');
        var newStatus = isChecked ? 'active' : 'inactive';
        var userId = $(this).data('user-id') || window.userId;
        var statusText = $(this).siblings('label').find('.status-text');
        var toggle = $(this);

        if (!userId) {
            console.error('User ID not found for status toggle');
            return;
        }

        // Disable toggle during request
        toggle.prop('disabled', true);

        $.ajax({
            url: window.routes.userStatusUpdate,
            type: 'PATCH',
            data: {
                status: newStatus
            },
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    // Update status text
                    statusText.text(response.status.charAt(0).toUpperCase() + response.status.slice(1));

                    // Show success message
                    Swal.fire({
                        text: response.message,
                        icon: "success",
                        buttonsStyling: false,
                        confirmButtonText: "Ok, got it!",
                        customClass: {
                            confirmButton: "btn btn-primary"
                        },
                        timer: 2000,
                        timerProgressBar: true
                    });
                }
            },
            error: function(xhr) {
                // Revert toggle state
                toggle.prop('checked', !isChecked);

                var errorMessage = xhr.responseJSON?.message || 'An error occurred while updating user status.';

                Swal.fire({
                    text: errorMessage,
                    icon: "error",
                    buttonsStyling: false,
                    confirmButtonText: "Ok, got it!",
                    customClass: {
                        confirmButton: "btn btn-primary"
                    }
                });
            },
            complete: function() {
                // Re-enable toggle
                toggle.prop('disabled', false);
            }
        });
    });
}

/**
 * Handle Form Errors
 */
function handleFormError(xhr, defaultMessage) {
    if (xhr.status === 422) {
        // Validation errors
        var errors = xhr.responseJSON.errors;
        $.each(errors, function(field, messages) {
            var input = $('[name="' + field + '"]');
            input.addClass('is-invalid');
            input.after('<div class="invalid-feedback">' + messages[0] + '</div>');
        });
    } else {
        var errorMessage = xhr.responseJSON?.message || defaultMessage;

        Swal.fire({
            text: errorMessage,
            icon: "error",
            buttonsStyling: false,
            confirmButtonText: "Ok, got it!",
            customClass: {
                confirmButton: "btn btn-primary"
            }
        });
    }
}

/**
 * Initialize Recharge History Event Handlers
 */
function initializeRechargeHistoryEventHandlers() {
    // Export CSV button click
    $('#kt_export_recharge_history').on('click', function() {
        if (rechargeHistoryTable) {
            // Get current table data
            var data = rechargeHistoryTable.ajax.json();
            if (data && data.data && data.data.length > 0) {
                // Create CSV content
                var csvContent = "Date,Gateway,Transaction ID,Amount,Status,Remarks\n";

                data.data.forEach(function(row) {
                    // Clean HTML tags from status column
                    var status = row.status.replace(/<[^>]*>/g, '');

                    csvContent += [
                        '"' + (row.recharge_date || '') + '"',
                        '"' + (row.gateway || '') + '"',
                        '"' + (row.transaction_id || '') + '"',
                        '"' + (row.amount || '') + '"',
                        '"' + status + '"',
                        '"' + (row.remarks || '') + '"'
                    ].join(',') + '\n';
                });

                // Download CSV
                var blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                var link = document.createElement("a");
                var url = URL.createObjectURL(blob);
                link.setAttribute("href", url);
                link.setAttribute("download", "recharge_history_" + new Date().toISOString().slice(0,10) + ".csv");
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // Show success message
                Swal.fire({
                    text: "Recharge history exported successfully!",
                    icon: "success",
                    buttonsStyling: false,
                    confirmButtonText: "Ok, got it!",
                    customClass: {
                        confirmButton: "btn btn-primary"
                    }
                });
            } else {
                Swal.fire({
                    text: "No data available to export.",
                    icon: "warning",
                    buttonsStyling: false,
                    confirmButtonText: "Ok, got it!",
                    customClass: {
                        confirmButton: "btn btn-primary"
                    }
                });
            }
        }
    });
}
