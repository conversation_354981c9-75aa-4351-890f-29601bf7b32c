"use strict";

/**
 * initialize DataTables method
 * @param datatables
 * @returns {boolean}
 */
function initializeDataTables(datatables) {
    $(".date_picker").flatpickr();
    $(".date_time_picker").flatpickr({
        enableTime: true,
        dateFormat: "Y-m-d H:i",
    });

    let keys = Object.keys(datatables);

    if (keys.length < 0) {
        return false;
    }
//kt_table_users //kt_table_names
    keys.forEach(function (tableKey, index) {
        let table = $("#kt_table_" + tableKey + "s");

        if (!table.length) {
            return;
        }

        let tableObj = new Object();
        let apiUrl = "/datatables/" + tableKey + "-list" +
            (datatables[tableKey]["key"]
                ? "/" + datatables[tableKey]["key"]
                : "");

        let $ajaxData = {
            url: apiUrl,
            data: {}
        };

        if (datatables[tableKey]["data"]) {
            $ajaxData.data = datatables[tableKey]["data"];
        }

        resolveData(table[0], $ajaxData.data)

        let tableOptions = {
            processing: true,
            serverSide: true,
            bAutoWidth: false,
            order: [[1, "desc"]],
            ajax: $ajaxData,
            columnDefs: datatables[tableKey]["columnDefs"]
                ? datatables[tableKey]["columnDefs"]
                : [],
            columns: datatables[tableKey]["columns"]
                ? datatables[tableKey]["columns"]
                : datatables[tableKey],
        };

        if (datatables[tableKey]["dom"]) {
            tableOptions.dom = datatables[tableKey]["dom"];
        }

        // Initialize DataTable for the current table
        window.dataTable = tableObj = table.DataTable(tableOptions);

        if (datatables[tableKey]["events"]) {
            datatables[tableKey]["events"](tableObj);
        }
    });
}

/**
 * Rescue attribute and value from selector
 *
 * @param {object} table
 * @param {object} data
 */
function resolveData(table, data) {
    let attr, node = "";

    Object.keys(table.attributes).forEach(e => {
        node = table.attributes[e];
        attr = node.nodeName.match(/^(data\-)/);

        if (!attr) {
            return;
        }

        data[node.nodeName.replace('data-', '')] = node.nodeValue;
    });

    return data;
}

$(document).ready(function () {
    let datatables = {
        // Here is single datatable example format. you can follow this format to initiate any table
        user: {
            data: function (data) {
                data.role_id = $("#filterByUserType").val();
                data.user_id = $("#filterByUser").val();
            },
            events: function (context) {
                $("#filterByUserType").on("change", function () {
                    context.draw();
                });
                $("#filterByUser").on("change", function () {
                    context.draw();
                });
                $("#searchInput").on("keyup change", function () {
                    context.search($(this).val()).draw();
                });
            },
            responsive: {
                details: {
                    type: 'column',
                    target: 'tr'
                }
            },
            columnDefs: function() {
                // Adjust column target based on whether parent account column is visible
                let actionColumnIndex = window.isSuperAdmin ? 9 : 8;
                let columnDefs = [
                    {targets: actionColumnIndex, orderable: false, responsivePriority: 1},
                    {targets: 0, responsivePriority: 2}, // ID column
                    {targets: 1, responsivePriority: 3}, // Name column
                    {targets: 2, responsivePriority: 4}, // Username column
                    {targets: 3, responsivePriority: 10}, // Email column - lower priority
                    {targets: 4, responsivePriority: 8}, // Phone column
                    {targets: 5, responsivePriority: 6}, // Credits column
                    {targets: 6, responsivePriority: 9}, // Expiry Date column
                ];

                // Add parent account column priority if visible
                if (window.isSuperAdmin) {
                    columnDefs.push({targets: 7, responsivePriority: 7}); // Parent Account column
                    columnDefs.push({targets: 8, responsivePriority: 5}); // User Type column
                } else {
                    columnDefs.push({targets: 7, responsivePriority: 5}); // User Type column
                }

                return columnDefs;
            }(),
            columns: function() {
                let columns = [
                    {
                        data: "id",
                        title: "Sl",
                        width: "50px",
                        className: "text-center"
                    },
                    {
                        data: "name",
                        title: "Name",
                        className: "text-nowrap"
                    },
                    {
                        data: "username",
                        title: "Login ID",
                        className: "text-nowrap"
                    },
                    {
                        data: "email",
                        title: "Email",
                        className: "text-break"
                    },
                    {
                        data: "phone",
                        title: "Contact #",
                        className: "text-nowrap"
                    },
                    {
                        data: "current_balance",
                        title: "Credits",
                        className: "text-end text-nowrap"
                    },
                    {
                        data: "balance_expired",
                        title: "Expiry Date",
                        className: "text-nowrap"
                    }
                ];

                // Add parent account column only for super-admin users
                if (window.isSuperAdmin) {
                    columns.push({
                        data: "parent_account",
                        title: "Parent Account",
                        className: "text-nowrap"
                    });
                }

                columns = columns.concat([
                    {
                        data: "user_type",
                        title: "User Type",
                        className: "text-nowrap"
                    },
                    {
                        data: "action",
                        title: "Action",
                        width: "120px",
                        className: "text-end text-nowrap"
                    }
                ]);

                return columns;
            }()
        },
        sender: {
            data: function (data) {
            },
            events: function (context) {
                $("#searchInput").on("keyup change", function () {
                    context.search($(this).val()).draw();
                });
            },
            columnDefs: function() {
                // Adjust column target based on whether server column is visible
                let actionColumnIndex = window.isSuperAdmin ? 5 : 4;
                return [{targets: actionColumnIndex, orderable: false}];
            }(),
            columns: function() {
                let columns = [
                    {data: "id", title: "Sl"},
                    {data: "name", title: "Sender ID"},
                    {
                        data: "company.name",
                        title: "Company",
                        render: function (data, type, row) {
                            return data ? data : "Unused"; // Fallback to "N/A" if company.name is null
                        }
                    }
                ];

                // Add server column only for super-admin users
                if (window.isSuperAdmin) {
                    columns.push({data: "server", title: "Server"});
                }

                columns.push(
                    {data: "is_default", title: "Default"},
                    {data: "created_at", title: "Requested On"},
                    {data: "executed_at", title: "Executed On"},
                    {data: "status", title: "Status"},
                    {data: "action", title: "Action"}
                );

                return columns;
            }()
        },
        contact: {
            data: function (data) {
                data.operator = $("#filterByNameOperator").val();
                data.group = $("#filterByGroup").val();
                data.status = $("#filterByStatus").val();
            },
            events: function (context) {
                $("#searchInput").on("keyup change", function () {
                    context.search($(this).val()).draw();
                });
                $("#filterByNameOperator").on("keyup change", function () {
                    context.draw();
                });
                $("#filterByGroup").on("change", function () {
                    context.draw();
                });
                $("#filterByStatus").on("change", function () {
                    context.draw();
                });
            },
            columnDefs: [
                {targets: 7, orderable: false},
            ],
            columns: [
                {data: "id", title: "Sl"},
                {data: "name", title: "Name"},
                {data: "phone", title: "Contact"},
                {data: "email", title: "Email"},
                {data: "group_name", title: "Group"},
                {data: "operator", title: "Operator"},
                {data: "status", title: "Status"},
                {data: "action", title: "Action"},
            ]
        },
        coverage: {
            data: function (data) {
            },
            events: function (context) {
                $("#searchInput").on("keyup change", function () {
                    context.search($(this).val()).draw();
                });
            },
            columnDefs: [
                {targets: 6, orderable: false},
            ],
            columns: [
                {data: "id", title: "Sl"},
                {data: "operator", title: "Operator"},
                {data: "prefix", title: "Prefix"},
                {data: "masking_price", title: "Masking Price"},
                {data: "non_masking_price", title: "Non Masking Price"},
                {data: "status", title: "Status"},
                {data: "action", title: "Action"},
            ]
        },
        server: {
            data: function (data) {
            },
            events: function (context) {
                $("#searchInput").on("keyup change", function () {
                    context.search($(this).val()).draw();
                });
            },
            columnDefs: [
                {targets: 7, orderable: false},
            ],
            columns: [
                {data: "id", title: "Sl"},
                {data: "name", title: "Name"},
                {data: "provider_name", title: "Provider"},
                {data: "api_link", title: "Api"},
                {data: "api_key", title: "api key"},
                {data: "username", title: "Username"},
                {data: "status", title: "Status"},
                {data: "action", title: "Action"},
            ]
        },
        gateway: {
            data: function (data) {
            },
            events: function (context) {
                $("#searchInput").on("keyup change", function () {
                    context.search($(this).val()).draw();
                });
            },
            columnDefs: [
                {targets: 6, orderable: false},
            ],
            columns: [
                {data: "id", title: "Sl"},
                {data: "name", title: "Name"},
                {data: "msisdn", title: "MSISDN"},
                {data: "username", title: "Username"},
                {data: "password", title: "Password"},
                {data: "test_mode", title: "Test mode"},
                {data: "status", title: "Status"},
                {data: "action", title: "Action"},
            ]
        },
        recharge: {
            data: function (data) {
            },
            events: function (context) {
                $("#searchInput").on("keyup change", function () {
                    context.search($(this).val()).draw();
                });
            },
            columnDefs: [
                {targets: -1, orderable: false},
            ],
            columns: [
                {data: "id", title: "Sl"},
                {data: "recharge_date", title: "Date"},
                {data: "payment.gateway", title: "Gateway"},
                {data: "payment.remarks", title: "Remarks"},
                {data: "payment.transaction_id", title: "Trans ID"},
                {data: "payment.amount", title: "Amount"},
                {data: "payment.payment_status", title: "Status"},
                {data: "action", title: "Actions"}
            ]
        },
        'account-recharge': [
            {data: "id", title: "Sl"},
            {data: "company.name", title: "Company"},
            {data: "recharge_date", title: "Date"},
            {data: "gateway", title: "Gateway"},
            {data: "remarks", title: "Remarks"},
            {data: "transaction_id", title: "Trans ID"},
            {data: "recharge_amount", title: "Amount"},
            {data: "payment_status", title: "Status"},
            {data: "action", title: "Action"},
        ],
        template: {
            data: function (data) {
            },
            events: function (context) {
                $("#searchInput").on("keyup change", function () {
                    context.search($(this).val()).draw();
                });
            },
            columns: [
                {data: "id", title: "Sl"},
                {data: "name", title: "Name"},
                {data: "text", title: "Text"},
                {data: "created_at", title: "Created On"},
                {data: "action", title: "Action"},
            ]
        },
        content: {
            data: function (data) {
            },
            events: function (context) {
                $("#searchInput").on("keyup change", function () {
                    context.search($(this).val()).draw();
                });
            },
            columns: [
                {data: "id", title: "Sl"},
                {data: "name", title: "Name"},
                {data: "text", title: "Text"},
                {data: "action", title: "Action"},
            ]
        },
        'today-detail': {
            data: function (data) {
                data.date_from = $("#dateFrom").val();
                data.date_to = $("#dateTo").val();
                data.user_role = $("#userRoleFilter").val();
                data.company_id = $("#companyFilter").val();
            },
            events: function (context) {
                $("#searchInput").on("keyup change", function () {
                    context.search($(this).val()).draw();
                });
                $("#filterBtn").on("click", function () {
                    context.ajax.reload();
                });
                $("#userRoleFilter, #companyFilter").on("change", function () {
                    context.ajax.reload();
                });

                // View details modal functionality
                $(document).on('click', '.view-details-btn', function() {
                    const messageId = $(this).data('message-id');

                    // Reset modal content
                    $('#smsDetailsContent').html(`
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Loading SMS details...</p>
                        </div>
                    `);

                    // Fetch SMS details
                    $.ajax({
                        url: `/datatables/sms-details/${messageId}`,
                        method: 'GET',
                        success: function(data) {
                            const content = `
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="card border-0 bg-light-primary">
                                            <div class="card-body p-4">
                                                <h6 class="card-title text-primary mb-3">
                                                    <i class="fas fa-info-circle me-2"></i>Basic Information
                                                </h6>
                                                <div class="mb-2"><strong>Message ID:</strong> ${data.message_id}</div>
                                                <div class="mb-2"><strong>Phone Number:</strong> ${data.phone_number}</div>
                                                <div class="mb-2"><strong>Sender:</strong> ${data.sender_name} (${data.sender_type})</div>
                                                <div class="mb-2"><strong>SMS Type:</strong> ${data.sms_type}</div>
                                                <div class="mb-2"><strong>Operator:</strong> ${data.operator}</div>
                                                ${data.show_server ? `<div class="mb-2"><strong>Server:</strong> ${data.server_name}</div>` : ''}
                                                <div class="mb-2"><strong>Batch Number:</strong> ${data.batch_number}</div>
                                                <div class="mb-2"><strong>Schedule Status:</strong>
                                                    <span class="badge badge-${data.schedule_status_badge}">${data.schedule_status}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card border-0 bg-light-success">
                                            <div class="card-body p-4">
                                                <h6 class="card-title text-success mb-3">
                                                    <i class="fas fa-dollar-sign me-2"></i>Cost Breakdown
                                                </h6>
                                                <div class="mb-2"><strong>SMS Count:</strong> ${data.sms_count}</div>
                                                <div class="mb-2"><strong>Rate per SMS:</strong> ${data.per_sms_rate} BDT</div>
                                                <div class="mb-2"><strong>Total Cost:</strong> ${data.total_cost} BDT</div>
                                                <div class="mb-2"><strong>Status:</strong>
                                                    <span class="badge ${data.status === 'Success' ? 'badge-success' : data.status === 'Failed' ? 'badge-danger' : 'badge-warning'}">${data.status}</span>
                                                </div>
                                                ${data.show_admin_info ? `
                                                    <div class="mb-2"><strong>Company:</strong> ${data.company_name}</div>
                                                    <div class="mb-2"><strong>User:</strong> ${data.user_name}</div>
                                                ` : ''}
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row mt-4">
                                    <div class="col-12">
                                        <div class="card border-0 bg-light-info">
                                            <div class="card-body p-4">
                                                <h6 class="card-title text-info mb-3">
                                                    <i class="fas fa-clock me-2"></i>Timing Information
                                                </h6>
                                                <div class="mb-2"><strong>Created At:</strong> ${data.created_at}</div>
                                                ${data.schedule_at ? `<div class="mb-2"><strong>Scheduled At:</strong> ${data.schedule_at}</div>` : '<div class="mb-2"><em>No scheduled time (sent immediately)</em></div>'}
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row mt-4">
                                    <div class="col-12">
                                        <div class="card border-0 bg-light-warning">
                                            <div class="card-body p-4">
                                                <h6 class="card-title text-warning mb-3">
                                                    <i class="fas fa-comment-alt me-2"></i>SMS Content
                                                </h6>
                                                <div class="p-3 bg-white rounded border">
                                                    ${data.sms_content ?
                                                        `<pre class="mb-0" style="white-space: pre-wrap; font-family: inherit; line-height: 1.5;">${data.sms_content}</pre>` :
                                                        '<em class="text-muted">No SMS content available</em>'
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                ${data.api_response ? `
                                <div class="row mt-4">
                                    <div class="col-12">
                                        <div class="card border-0 bg-light-secondary">
                                            <div class="card-body p-4">
                                                <h6 class="card-title text-secondary mb-3">
                                                    <i class="fas fa-code me-2"></i>API Response
                                                </h6>
                                                <div class="p-3 bg-white rounded border">
                                                    <pre class="mb-0" style="font-size: 12px; max-height: 200px; overflow-y: auto; background-color: #f8f9fa; padding: 10px; border-radius: 4px;">${JSON.stringify(data.api_response, null, 2)}</pre>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                ` : `
                                <div class="row mt-4">
                                    <div class="col-12">
                                        <div class="card border-0 bg-light-secondary">
                                            <div class="card-body p-4">
                                                <h6 class="card-title text-secondary mb-3">
                                                    <i class="fas fa-code me-2"></i>API Response
                                                </h6>
                                                <div class="p-3 bg-white rounded border">
                                                    <em class="text-muted">No API response data available</em>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                `}
                            `;

                            $('#smsDetailsContent').html(content);
                        },
                        error: function(xhr, status, error) {
                            $('#smsDetailsContent').html(`
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    Error loading SMS details: ${error}
                                </div>
                            `);
                        }
                    });
                });
            },
            columnDefs: function() {
                // Adjust column targets based on whether company column is visible
                let nonOrderableTargets = [0]; // Serial number
                if (window.userHasCompanyColumn) {
                    nonOrderableTargets.push(4, 5, 9); // Schedule Status, Charge/SMS, Actions (with company column)
                } else {
                    nonOrderableTargets.push(3, 4, 8); // Schedule Status, Charge/SMS, Actions (without company column)
                }
                return [{targets: nonOrderableTargets, orderable: false}];
            }(),
            columns: function() {
                let columns = [
                    { data: "message_id", name: 'serial', title: 'Message ID' },
                    { data: "sent_time", name: 'messages.created_at', title: 'Sent Time' },
                    { data: "sender_name", name: 'senders.name', title: 'Sender' }
                ];

                // Add company column for admin users
                if (window.userHasCompanyColumn) {
                    columns.push({ data: "company_name", name: 'companies.name', title: 'Company' });
                }

                columns = columns.concat([
                    { data: "schedule_status", name: 'schedule_status', title: 'Schedule Status' },
                    { data: "charge_per_sms", name: 'charge_per_sms', title: 'Charge/SMS' },
                    { data: "sms_count", name: 'messages.sms_count', title: 'Total Sent' },
                    { data: "total_cost", name: 'messages.sms_cost', title: 'Total Cost' },
                    { data: "api_status", name: 'messages.status', title: 'API Status' },
                    { data: "view_details", name: 'view_details', title: 'Actions', className: 'text-end' }
                ]);

                return columns;
            }()
        },
        'summary-log': {
            data: function (data) {
                data.date_from = $("#dateFrom").val();
                data.date_to = $("#dateTo").val();
                data.user_role = $("#userRoleFilter").val();
                data.company_id = $("#companyFilter").val();
            },
            events: function (context) {
                $("#filterBtn").on("click", function () {
                    context.ajax.reload();
                });
                $("#userRoleFilter, #companyFilter").on("change", function () {
                    context.ajax.reload();
                });
            },
            columnDefs: [
                {targets: [0], orderable: false}
            ],
            columns: [
                {data: "serial", title: "SL"},
                {data: "company_name", title: "Company Name"},
                {data: "total_sms_sent", title: "Total SMS Sent"},
                {data: "total_amount_deducted", title: "Total Amount Deducted"}
            ]
        },
        transaction: {
            data: function (data) {
                // Add filter parameters to the request
                data.date_from = $('#dateFrom').val();
                data.date_to = $('#dateTo').val();
                data.user_role = $('#userRoleFilter').val();
                data.company_id = $('#companyFilter').val();
                data.transaction_type = $('#transactionTypeFilter').val();
            },
            events: function (context) {
                // Search functionality
                $("#searchInput").on("keyup change", function () {
                    context.search($(this).val()).draw();
                });

                // Filter button functionality
                $("#filterBtn").on("click", function () {
                    context.draw();
                });

                // Auto-apply filters on change
                $("#userRoleFilter, #companyFilter, #transactionTypeFilter, #dateFrom, #dateTo").on("change", function () {
                    context.draw();
                });
            },
            columnDefs: [
                { targets: 0, orderable: false }, // Serial number column
                { targets: -1, orderable: false }, // Last column (Balance After)
                { targets: -2, orderable: false }, // Balance impact column
                { className: "text-center", targets: [0, 3] }, // Center align specific columns
                { className: "text-nowrap", targets: [1, 3, 4] }, // No wrap for specific columns
                { className: "text-end", targets: function() {
                    // Right align balance column if user has admin role
                    return window.userHasCompanyColumn ? [-1] : [];
                }() }
            ],
            columns: function() {
                var columns = [
                    { data: "serial", name: 'serial', title: 'SL', width: '50px' },
                    { data: "date", name: 'date', title: 'Date', width: '150px' }
                ];

                // Add company column only for admin roles
                if (window.userHasCompanyColumn) {
                    columns.push({ data: "company_name", name: 'company_name', title: 'Account', width: '120px' });
                }

                columns = columns.concat([
                    { data: "transaction_type", name: 'transaction_type', title: 'Type', width: '100px' },
                    { data: "amount", name: 'amount', title: 'Amount', width: '100px' },
                    { data: "description", name: 'description', title: 'Description', width: '150px' },
                    { data: "balance_impact", name: 'balance_impact', title: 'Impact', width: '100px' }
                ]);

                // Add balance column only for admin roles
                if (window.userHasCompanyColumn) {
                    columns.push({ data: "balance_after_formatted", name: 'balance_after', title: 'Balance After', width: '120px' });
                }

                return columns;
            }()
        }
    };

    initializeDataTables(datatables);
});


