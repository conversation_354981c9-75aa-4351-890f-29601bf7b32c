"use strict";

// Class definition
var usersAddUser = function () {
    // Shared variables
    const element = document.getElementById('kt_modal_add_user');
    const form = element.querySelector('#kt_modal_add_user_form');
    const modal = new bootstrap.Modal(element);

    // Init add schedule modal
    var initAddUser = () => {

        // Init form validation rules. For more info check the FormValidation plugin's official documentation:https://formvalidation.io/
        var validator = FormValidation.formValidation(
            form,
            {
                fields: {
                    'role_id': {
                        validators: {
                            notEmpty: {
                                message: 'User type is required'
                            }
                        }
                    },
                    'name': {
                        validators: {
                            notEmpty: {
                                message: 'Full name is required'
                            }
                        }
                    },
                    'email': {
                        validators: {
                            notEmpty: {
                                message: 'Valid email address is required'
                            }
                        }
                    },
                    'phone': {
                        validators: {
                            notEmpty: {
                                message: 'Valid phone number is required'
                            }
                        }
                    },
                },

                plugins: {
                    trigger: new FormValidation.plugins.Trigger(),
                    bootstrap: new FormValidation.plugins.Bootstrap5({
                        rowSelector: '.fv-row',
                        eleInvalidClass: '',
                        eleValidClass: ''
                    })
                }
            }
        );

        // Submit button handler
        const submitButton = element.querySelector('[data-kt-users-modal-action="submit"]');
        submitButton.addEventListener('click', e => {
            e.preventDefault();

            // Validate form before submit
            if (validator) {
                validator.validate().then(function (status) {
                    if (status == 'Valid') {
                        // Show loading indication
                        submitButton.setAttribute('data-kt-indicator', 'on');

                        // Disable button to avoid multiple click
                        submitButton.disabled = true;

                        // Create a FormData object to collect form data
                        const formData = new FormData(form);
                        const formDataArray = [];

                        formData.forEach((value, key) => {
                            formDataArray.push([key, value]);
                        });
                        const formDataObject = Object.fromEntries(formDataArray);

                        Request('/users',
                            'POST',
                            formDataObject,
                            () => {
                                // Disable button to avoid multiple click
                                submitButton.disabled = false;
                                // Show loading indication
                                submitButton.setAttribute('data-kt-indicator', 'false');

                                // Show popup confirmation
                                Swal.fire({
                                    text: "User has been successfully created!",
                                    icon: "success",
                                    buttonsStyling: false,
                                    confirmButtonText: "Ok, got it!",
                                    customClass: {
                                        confirmButton: "btn btn-primary"
                                    }
                                }).then(function (result) {
                                    if (result.isConfirmed) {
                                        modal.hide();
                                        if (!window.dataTable) {
                                            window.location.href = window.location.href;
                                        }

                                        window.dataTable.draw();
                                    }
                                });

                            }, (e) => {
                                // Disable button to avoid multiple click
                                submitButton.disabled = false;
                                // Show loading indication
                                submitButton.setAttribute('data-kt-indicator', 'false');

                                Swal.fire({
                                    text: e.responseJSON.message,
                                    icon: "warning"
                                });
                            }
                        );
                    }
                });
            }
        });
    }

    return {
        // Public functions
        init: function () {
            initAddUser();
        }
    };
}();

// User Actions Dropdown Handler
var userActionsDropdown = function () {

    // Initialize dropdown functionality
    var initDropdowns = function () {
        // Re-initialize Bootstrap dropdowns after DataTable draw
        if (window.dataTable) {
            window.dataTable.on('draw', function () {
                // Initialize Bootstrap dropdowns for newly rendered rows
                var dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
                var dropdownList = dropdownElementList.map(function (dropdownToggleEl) {
                    return new bootstrap.Dropdown(dropdownToggleEl);
                });
            });
        }
    };

    // Handle impersonation confirmation
    var handleImpersonation = function () {
        // Add event delegation for impersonation buttons
        $(document).on('click', '.impersonate-btn', function(e) {
            e.preventDefault();

            var form = $(this).closest('form');
            var userName = $(this).data('user-name') || 'this user';

            Swal.fire({
                title: 'Confirm Impersonation',
                text: `Are you sure you want to login as ${userName}? You will be logged in as them and can return to your account anytime.`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#f1c40f',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'Yes, login as user',
                cancelButtonText: 'Cancel',
                customClass: {
                    confirmButton: "btn btn-warning",
                    cancelButton: "btn btn-secondary"
                },
                buttonsStyling: false
            }).then((result) => {
                if (result.isConfirmed) {
                    // Show loading state
                    Swal.fire({
                        title: 'Switching User...',
                        text: 'Please wait while we log you in as the selected user.',
                        allowOutsideClick: false,
                        allowEscapeKey: false,
                        showConfirmButton: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });

                    form.submit();
                }
            });
        });
    };

    // Handle user verification
    var handleUserVerification = function () {
        $(document).on('click', '.verify-user-btn', function(e) {
            e.preventDefault();

            const button = $(this);
            const form = button.closest('.verify-user-form');
            const userName = button.data('user-name');

            Swal.fire({
                text: `Are you sure you want to manually verify ${userName}?`,
                icon: "warning",
                showCancelButton: true,
                buttonsStyling: false,
                confirmButtonText: "Yes, verify!",
                cancelButtonText: "No, cancel",
                customClass: {
                    confirmButton: "btn fw-bold btn-danger",
                    cancelButton: "btn fw-bold btn-active-light-primary"
                }
            }).then(function (result) {
                if (result.value) {
                    // Show loading state
                    button.attr('disabled', true);
                    button.html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Verifying...');

                    // Submit form via AJAX
                    $.ajax({
                        url: form.attr('action'),
                        method: 'POST',
                        data: form.serialize(),
                        success: function(response) {
                            if (response.success) {
                                Swal.fire({
                                    text: response.message,
                                    icon: "success",
                                    buttonsStyling: false,
                                    confirmButtonText: "Ok, got it!",
                                    customClass: {
                                        confirmButton: "btn btn-primary"
                                    }
                                }).then(function() {
                                    // Reload the DataTable to reflect changes
                                    if (window.dataTable) {
                                        window.dataTable.ajax.reload();
                                    } else {
                                        location.reload();
                                    }
                                });
                            } else {
                                Swal.fire({
                                    text: response.message,
                                    icon: "error",
                                    buttonsStyling: false,
                                    confirmButtonText: "Ok, got it!",
                                    customClass: {
                                        confirmButton: "btn btn-primary"
                                    }
                                });
                            }
                        },
                        error: function(xhr) {
                            let message = 'An error occurred while verifying the user.';
                            if (xhr.responseJSON && xhr.responseJSON.message) {
                                message = xhr.responseJSON.message;
                            }

                            Swal.fire({
                                text: message,
                                icon: "error",
                                buttonsStyling: false,
                                confirmButtonText: "Ok, got it!",
                                customClass: {
                                    confirmButton: "btn btn-primary"
                                }
                            });
                        },
                        complete: function() {
                            // Reset button state
                            button.attr('disabled', false);
                            button.html('<i class="ki-duotone ki-check-circle fs-6 me-2 text-primary"><span class="path1"></span><span class="path2"></span></i>Verify User');
                        }
                    });
                }
            });
        });
    };

    return {
        init: function () {
            initDropdowns();
            handleImpersonation();
            handleUserVerification();
        }
    };
}();

// On document ready
KTUtil.onDOMContentLoaded(function () {
    usersAddUser.init();
    userActionsDropdown.init();
});
