/**
 * Admin Dashboard Real-time System
 * Provides sophisticated real-time data fetching with intelligent polling,
 * connection monitoring, and proper error handling
 */

class AdminDashboardRealtime {
    constructor() {
        this.isActive = false;
        this.intervals = {};
        this.retryAttempts = {};
        this.maxRetries = 3;
        this.baseInterval = 30000; // 30 seconds
        this.fastInterval = 10000;  // 10 seconds for critical updates
        this.slowInterval = 60000;  // 60 seconds for less critical data
        this.connectionStatus = 'connected';
        this.lastUpdateTimes = {};
        this.notificationSystem = null;

        // Initialize notification system
        this.initializeNotificationSystem();

        // Bind methods
        this.handleVisibilityChange = this.handleVisibilityChange.bind(this);
        this.handleOnline = this.handleOnline.bind(this);
        this.handleOffline = this.handleOffline.bind(this);

        // Setup event listeners
        this.setupEventListeners();
    }

    /**
     * Initialize the notification system
     */
    initializeNotificationSystem() {
        this.notificationSystem = new AdminNotificationSystem();
    }

    /**
     * Setup event listeners for connection and visibility changes
     */
    setupEventListeners() {
        // Page visibility API
        document.addEventListener('visibilitychange', this.handleVisibilityChange);

        // Online/offline detection
        window.addEventListener('online', this.handleOnline);
        window.addEventListener('offline', this.handleOffline);

        // Beforeunload cleanup
        window.addEventListener('beforeunload', () => {
            this.stop();
        });
    }

    /**
     * Check if required DOM elements exist
     */
    checkRequiredElements() {
        const requiredElements = [
            'action-queue-container',
            // Add other required element IDs here
        ];

        const missingElements = [];
        requiredElements.forEach(elementId => {
            if (!document.getElementById(elementId)) {
                missingElements.push(elementId);
            }
        });

        if (missingElements.length > 0) {
            console.warn('Missing required elements for real-time updates:', missingElements);
            if (this.notificationSystem) {
                this.notificationSystem.showWarning(
                    `Some dashboard elements are missing. Real-time updates may not work properly.`,
                    { title: 'Dashboard Warning' }
                );
            }
        }
    }

    /**
     * Handle page visibility changes
     */
    handleVisibilityChange() {
        if (document.hidden) {
            this.pauseUpdates();
        } else {
            this.resumeUpdates();
        }
    }

    /**
     * Handle online status
     */
    handleOnline() {
        this.connectionStatus = 'connected';
        this.updateConnectionIndicator();
        this.resumeUpdates();
        this.notificationSystem.showToast('Connection restored', 'success');
    }

    /**
     * Handle offline status
     */
    handleOffline() {
        this.connectionStatus = 'offline';
        this.updateConnectionIndicator();
        this.pauseUpdates();
        this.notificationSystem.showToast('Connection lost', 'warning');
    }

    /**
     * Start the real-time system
     */
    start() {
        if (this.isActive) return;

        this.isActive = true;
        this.updateConnectionIndicator();

        // Check if required elements exist
        this.checkRequiredElements();

        // Start different update cycles
        this.startCriticalUpdates();
        this.startRegularUpdates();
        this.startSlowUpdates();

        console.log('Admin Dashboard Real-time system started');
    }

    /**
     * Stop the real-time system
     */
    stop() {
        this.isActive = false;

        // Clear all intervals
        Object.values(this.intervals).forEach(interval => {
            if (interval) clearInterval(interval);
        });

        this.intervals = {};
        console.log('Admin Dashboard Real-time system stopped');
    }

    /**
     * Pause updates (when page is hidden)
     */
    pauseUpdates() {
        Object.keys(this.intervals).forEach(key => {
            if (this.intervals[key]) {
                clearInterval(this.intervals[key]);
                this.intervals[key] = null;
            }
        });
    }

    /**
     * Resume updates (when page becomes visible)
     */
    resumeUpdates() {
        if (!this.isActive) return;

        // Restart all update cycles
        this.startCriticalUpdates();
        this.startRegularUpdates();
        this.startSlowUpdates();

        // Immediate refresh when resuming
        this.refreshAll();
    }

    /**
     * Start critical updates (alerts, action queue)
     */
    startCriticalUpdates() {
        if (this.intervals.critical) clearInterval(this.intervals.critical);

        this.intervals.critical = setInterval(() => {
            this.fetchAlerts();
            this.fetchActionQueue();
        }, this.fastInterval);

        // Initial fetch
        this.fetchAlerts();
        this.fetchActionQueue();
    }

    /**
     * Start regular updates (metrics, summary cards)
     */
    startRegularUpdates() {
        if (this.intervals.regular) clearInterval(this.intervals.regular);

        this.intervals.regular = setInterval(() => {
            this.fetchMetrics();
            this.fetchSummaryCards();
        }, this.baseInterval);

        // Initial fetch
        this.fetchMetrics();
        this.fetchSummaryCards();
    }

    /**
     * Start slow updates (aggregated metrics)
     */
    startSlowUpdates() {
        if (this.intervals.slow) clearInterval(this.intervals.slow);

        this.intervals.slow = setInterval(() => {
            this.fetchAggregatedMetrics();
        }, this.slowInterval);

        // Initial fetch
        this.fetchAggregatedMetrics();
    }

    /**
     * Fetch alerts with error handling
     */
    async fetchAlerts() {
        try {
            const response = await this.makeRequest('/admin/dashboard/alerts');
            if (response.success) {
                this.updateAlertsDisplay(response.data);
                this.checkForNewAlerts(response.data);
                this.resetRetryCount('alerts');
            }
        } catch (error) {
            this.handleFetchError('alerts', error);
        }
    }

    /**
     * Fetch action queue with error handling
     */
    async fetchActionQueue() {
        try {
            console.log('Fetching action queue data...');
            const response = await this.makeRequest('/admin/dashboard/action-queue');
            console.log('Action queue response:', response);
            if (response.success) {
                this.updateActionQueueDisplay(response.data);
                this.resetRetryCount('actionQueue');
            } else {
                console.warn('Action queue fetch unsuccessful:', response);
            }
        } catch (error) {
            this.handleFetchError('actionQueue', error);
        }
    }

    /**
     * Fetch metrics with error handling
     */
    async fetchMetrics() {
        try {
            const response = await this.makeRequest('/admin/dashboard/metrics');
            if (response.success) {
                this.updateMetricsDisplay(response.data);
                this.resetRetryCount('metrics');
            }
        } catch (error) {
            this.handleFetchError('metrics', error);
        }
    }

    /**
     * Fetch summary cards with error handling
     */
    async fetchSummaryCards() {
        try {
            const response = await this.makeRequest('/admin/dashboard/summary-cards');
            if (response.success) {
                this.updateSummaryCardsDisplay(response.data);
                this.resetRetryCount('summaryCards');
            }
        } catch (error) {
            this.handleFetchError('summaryCards', error);
        }
    }

    /**
     * Fetch aggregated metrics with error handling
     */
    async fetchAggregatedMetrics() {
        try {
            const response = await this.makeRequest('/admin/dashboard/aggregated-metrics');
            if (response.success) {
                this.updateAggregatedMetricsDisplay(response.data);
                this.resetRetryCount('aggregatedMetrics');
            }
        } catch (error) {
            this.handleFetchError('aggregatedMetrics', error);
        }
    }

    /**
     * Make HTTP request with proper error handling
     */
    async makeRequest(url) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

        try {
            // Get CSRF token from meta tag
            const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

            const headers = {
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            };

            // Add CSRF token if available
            if (csrfToken) {
                headers['X-CSRF-TOKEN'] = csrfToken;
            }

            const response = await fetch(url, {
                method: 'GET',
                headers: headers,
                credentials: 'same-origin', // Include cookies for session authentication
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            clearTimeout(timeoutId);
            throw error;
        }
    }

    /**
     * Handle fetch errors with retry logic
     */
    handleFetchError(type, error) {
        console.error(`Error fetching ${type}:`, error);

        // Show more detailed error information
        if (error.message.includes('401') || error.message.includes('Unauthenticated')) {
            console.warn('Authentication error - user may need to log in again');
            this.notificationSystem.showWarning('Session expired. Please refresh the page.', {
                title: 'Authentication Error',
                duration: 10000
            });
            this.connectionStatus = 'error';
            this.updateConnectionIndicator();
            return;
        }

        if (!this.retryAttempts[type]) {
            this.retryAttempts[type] = 0;
        }

        this.retryAttempts[type]++;

        if (this.retryAttempts[type] <= this.maxRetries) {
            // Exponential backoff
            const delay = Math.pow(2, this.retryAttempts[type]) * 1000;
            console.log(`Retrying ${type} in ${delay}ms (attempt ${this.retryAttempts[type]}/${this.maxRetries})`);
            setTimeout(() => {
                if (this.isActive) {
                    this[`fetch${type.charAt(0).toUpperCase() + type.slice(1)}`]();
                }
            }, delay);
        } else {
            this.notificationSystem.showError(`Failed to update ${type} after ${this.maxRetries} attempts`, {
                title: 'Update Error'
            });
            this.connectionStatus = 'error';
            this.updateConnectionIndicator();
        }
    }

    /**
     * Reset retry count for successful requests
     */
    resetRetryCount(type) {
        this.retryAttempts[type] = 0;
        if (this.connectionStatus === 'error') {
            this.connectionStatus = 'connected';
            this.updateConnectionIndicator();
        }
    }

    /**
     * Refresh all data immediately
     */
    refreshAll() {
        this.fetchAlerts();
        this.fetchActionQueue();
        this.fetchMetrics();
        this.fetchSummaryCards();
        this.fetchAggregatedMetrics();
    }

    /**
     * Update connection status indicator
     */
    updateConnectionIndicator() {
        const indicator = document.getElementById('admin-summary-status');
        if (!indicator) return;

        indicator.className = 'badge fs-8';

        switch (this.connectionStatus) {
            case 'connected':
                indicator.classList.add('badge-light-success');
                indicator.innerHTML = '<i class="ki-duotone ki-check-circle fs-7 text-success me-1"><span class="path1"></span><span class="path2"></span></i>Live';
                break;
            case 'offline':
                indicator.classList.add('badge-light-warning');
                indicator.innerHTML = '<i class="ki-duotone ki-disconnect fs-7 text-warning me-1"><span class="path1"></span><span class="path2"></span></i>Offline';
                break;
            case 'error':
                indicator.classList.add('badge-light-danger');
                indicator.innerHTML = '<i class="ki-duotone ki-cross-circle fs-7 text-danger me-1"><span class="path1"></span><span class="path2"></span></i>Error';
                break;
        }
    }

    /**
     * Update alerts display
     */
    updateAlertsDisplay(alerts) {
        // Update alert count in admin summary
        const alertsIndicator = document.getElementById('alerts-indicator');
        const alertsCountElement = document.querySelector('[data-metric="alerts_count"]');

        if (alerts.length === 0) {
            if (alertsIndicator) {
                alertsIndicator.style.display = 'none';
            }
        } else {
            if (alertsIndicator) {
                alertsIndicator.style.display = 'flex';
                if (alertsCountElement) {
                    alertsCountElement.textContent = alerts.length;
                }
            }
        }

        this.lastUpdateTimes.alerts = Date.now();
    }

    /**
     * Update action queue display
     */
    updateActionQueueDisplay(actionQueue) {
        console.log('Updating action queue display with data:', actionQueue);
        const container = document.getElementById('action-queue-container');
        if (!container) {
            console.error('Action queue container not found!');
            return;
        }

        console.log('Action queue container found, updating content...');

        // Ensure container has proper constraints
        this.ensureActionQueueContainerConstraints(container);

        if (actionQueue.length === 0) {
            container.innerHTML = `
                <div class="text-center py-10">
                    <i class="ki-duotone ki-check-circle fs-4x text-success mb-5">
                        <span class="path1"></span>
                        <span class="path2"></span>
                    </i>
                    <h3 class="text-gray-800 fw-bold mb-3">All caught up!</h3>
                    <p class="text-gray-400 fw-semibold fs-6">No urgent items require your attention right now.</p>
                </div>
            `;
        } else {
            let html = '';
            actionQueue.forEach(item => {
                html += this.generateActionQueueItemHtml(item);
            });
            container.innerHTML = html;
        }

        console.log('Action queue display updated successfully');
        this.lastUpdateTimes.actionQueue = Date.now();
    }

    /**
     * Ensure action queue container has proper constraints
     */
    ensureActionQueueContainerConstraints(container) {
        // Apply container constraints to prevent overflow
        container.style.maxWidth = '100%';
        container.style.overflowX = 'hidden';
        container.style.wordWrap = 'break-word';
        container.style.boxSizing = 'border-box';

        // Ensure parent card also has constraints
        const parentCard = container.closest('.card');
        if (parentCard) {
            parentCard.style.overflow = 'hidden';
            parentCard.style.maxWidth = '100%';
        }

        // Remove any existing min-w-750px classes that might be applied
        const existingItems = container.querySelectorAll('.min-w-750px, [class*="min-w-750px"]');
        existingItems.forEach(item => {
            item.classList.remove('min-w-750px');
            item.style.minWidth = 'auto';
            item.style.maxWidth = '100%';
            item.style.width = '100%';
        });
    }

    /**
     * Generate HTML for action queue item
     */
    generateActionQueueItemHtml(item) {
        let actionsHtml = '';
        item.actions.forEach(action => {
            actionsHtml += `<a href="${action.url}" class="btn ${action.class} btn-sm flex-fill flex-sm-grow-0">${action.text}</a>`;
        });

        return `
            <div class="action-queue-item d-flex flex-column flex-sm-row align-items-start align-items-sm-center border border-dashed border-gray-300 rounded px-4 py-3 mb-4">
                <!--begin::Priority indicator-->
                <div class="d-flex align-items-center mb-2 mb-sm-0 me-sm-3">
                    <div class="badge badge-light-${item.urgency_class} fw-bold fs-8 px-2 py-1">
                        P${item.priority}
                    </div>
                </div>
                <!--end::Priority indicator-->

                <!--begin::Info-->
                <div class="flex-grow-1 mb-3 mb-sm-0 me-sm-3">
                    <!--begin::Title-->
                    <a href="#" class="text-gray-800 text-hover-primary fs-6 fw-bold d-block">${item.title}</a>
                    <!--end::Title-->
                    <!--begin::Description-->
                    <span class="text-gray-400 fw-semibold d-block fs-7 mt-1">${item.description}</span>
                    <!--end::Description-->
                    <!--begin::User and time-->
                    <span class="text-gray-400 fw-semibold d-block fs-8 mt-1">
                        <span class="d-inline d-sm-none">${item.user}</span>
                        <span class="d-none d-sm-inline">${item.user} • ${this.formatTimeAgo(item.created_at)}</span>
                    </span>
                    <!--end::User and time-->
                </div>
                <!--end::Info-->

                <!--begin::Actions-->
                <div class="d-flex flex-wrap gap-2 w-100 w-sm-auto">
                    ${actionsHtml}
                </div>
                <!--end::Actions-->
            </div>
        `;
    }

    /**
     * Update metrics display
     */
    updateMetricsDisplay(metrics) {
        // Update admin summary metrics
        this.updateMetricElement('open_tickets', metrics.open_tickets);
        this.updateMetricElement('critical_tickets', metrics.critical_tickets);
        this.updateMetricElement('high_priority_tickets', metrics.high_priority_tickets);
        this.updateMetricElement('pending_payment_amount', this.formatCurrency(metrics.pending_payment_amount));
        this.updateMetricElement('pending_payments', metrics.pending_payments);
        this.updateMetricElement('pending_senders', metrics.pending_senders);
        this.updateMetricElement('new_users_today', metrics.new_users_today);

        this.updateTimestamp();
        this.lastUpdateTimes.metrics = Date.now();
    }

    /**
     * Update summary cards display
     */
    updateSummaryCardsDisplay(summaryCards) {
        // Update summary cards if needed
        // This would involve updating the summary cards component
        this.lastUpdateTimes.summaryCards = Date.now();
    }

    /**
     * Update aggregated metrics display
     */
    updateAggregatedMetricsDisplay(metrics) {
        // Update balance summary metrics
        this.updateMetricElement('total_balance', this.formatCurrency(metrics.total_balance));
        this.updateMetricElement('total_balance_msgs', this.formatNumber(metrics.total_balance_msgs));
        this.updateMetricElement('sms_last_week', this.formatNumber(metrics.sms_last_week));
        this.updateMetricElement('cost_last_week', this.formatCurrency(metrics.cost_last_week));
        this.updateMetricElement('sms_in_last_month', this.formatNumber(metrics.sms_in_last_month));
        this.updateMetricElement('cost_in_last_month', this.formatCurrency(metrics.cost_in_last_month));

        this.lastUpdateTimes.aggregatedMetrics = Date.now();
    }

    /**
     * Update a specific metric element
     */
    updateMetricElement(metricName, value) {
        const element = document.querySelector(`[data-metric="${metricName}"]`);
        if (element && element.textContent !== value.toString()) {
            // Add animation class
            element.classList.add('metric-updating');
            element.textContent = value;

            // Remove animation class after animation
            setTimeout(() => {
                element.classList.remove('metric-updating');
            }, 300);
        }
    }

    /**
     * Update timestamp display
     */
    updateTimestamp() {
        const timestampElement = document.querySelector('[data-timestamp]');
        if (timestampElement) {
            const now = new Date();
            timestampElement.textContent = now.toLocaleTimeString();
            timestampElement.setAttribute('data-timestamp', now.toISOString());
        }
    }

    /**
     * Check for new alerts and show notifications
     */
    checkForNewAlerts(currentAlerts) {
        if (!this.lastUpdateTimes.alerts) return; // Skip on first load

        const previousCount = this.previousAlertsCount || 0;
        const currentCount = currentAlerts.length;

        if (currentCount > previousCount) {
            const newAlertsCount = currentCount - previousCount;
            this.notificationSystem.showToast(
                `${newAlertsCount} new critical alert${newAlertsCount > 1 ? 's' : ''}`,
                'warning'
            );
        }

        this.previousAlertsCount = currentCount;
    }

    /**
     * Format currency
     */
    formatCurrency(amount) {
        return new Intl.NumberFormat('en-BD', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(amount || 0);
    }

    /**
     * Format number
     */
    formatNumber(number) {
        return new Intl.NumberFormat().format(number || 0);
    }

    /**
     * Format time ago
     */
    formatTimeAgo(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffInSeconds = Math.floor((now - date) / 1000);

        if (diffInSeconds < 60) return 'just now';
        if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
        if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
        return `${Math.floor(diffInSeconds / 86400)}d ago`;
    }
}

// Global function to refresh action queue
function refreshActionQueue() {
    if (window.adminDashboard) {
        console.log('Forcing action queue refresh...');
        window.adminDashboard.fetchActionQueue();

        // Also ensure container constraints are applied
        setTimeout(() => {
            const container = document.getElementById('action-queue-container');
            if (container && window.adminDashboard.ensureActionQueueContainerConstraints) {
                window.adminDashboard.ensureActionQueueContainerConstraints(container);
            }
        }, 100);
    }
}

// Global function to force fix action queue layout (for debugging)
function fixActionQueueLayout() {
    console.log('Forcing action queue layout fix...');
    const container = document.getElementById('action-queue-container');
    if (container) {
        // Remove any problematic classes
        const problematicItems = container.querySelectorAll('.min-w-750px, [class*="min-w-750px"]');
        problematicItems.forEach(item => {
            console.log('Fixing problematic item:', item);
            item.classList.remove('min-w-750px');
            item.style.minWidth = 'auto';
            item.style.maxWidth = '100%';
            item.style.width = '100%';

            // Add the correct responsive classes
            if (!item.classList.contains('action-queue-item')) {
                item.classList.add('action-queue-item');
                item.classList.add('d-flex', 'flex-column', 'flex-sm-row', 'align-items-start', 'align-items-sm-center');
            }
        });

        // Apply container constraints
        if (window.adminDashboard && window.adminDashboard.ensureActionQueueContainerConstraints) {
            window.adminDashboard.ensureActionQueueContainerConstraints(container);
        }

        console.log('Action queue layout fix completed');
    }
}
