<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="AI Model Pricing Calculator. Calculate cost by Tokens, Words, or Characters for various AI models like Anthropic Claude, GPT-4, and more.">
    <meta name="keywords" content="AI pricing, tokens, words, characters, API calls, Anthropic Claude, GPT-4">
    <meta name="author" content="Your Name">
    <title>AI Model Pricing Calculator | Tokens, Words, Characters</title>
    <script src="https://cdn.jsdelivr.net/npm/alpinejs" defer></script>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script>
        document.addEventListener('alpine:init', () => {
            Alpine.data('pricingCalculator', () => ({
                calculationType: 'tokens',
                inputTokens: 0,
                outputTokens: 0,
                apiCalls: 0,
                selectedModel: 'GPT-4 Turbo',
                models: [
                    {
                        provider: "OpenAI",
                        model: "GPT-4o (omni)",
                        input_per_1k_tokens: 0.005,
                        output_per_1k_tokens: 0.015,
                        per_call: 0.002
                    },
                    {
                        provider: "OpenAI / Azure",
                        model: "GPT-4 Turbo",
                        input_per_1k_tokens: 0.01,
                        output_per_1k_tokens: 0.03,
                        per_call: 0.004
                    },
                    {
                        provider: "OpenAI / Azure",
                        model: "GPT-3.5 Turbo",
                        input_per_1k_tokens: 0.0005,
                        output_per_1k_tokens: 0.0015,
                        per_call: 0.0002
                    }
                ],

                get selectedModelData() {
                    return this.models.find(model => model.model === this.selectedModel);
                },

                calculatePrice() {
                    const inputCost = (this.inputTokens / 1000) * this.selectedModelData.input_per_1k_tokens;
                    const outputCost = (this.outputTokens / 1000) * this.selectedModelData.output_per_1k_tokens;
                    const apiCost = this.apiCalls * this.selectedModelData.per_call;
                    return (inputCost + outputCost + apiCost).toFixed(4);
                }
            }));
        });
    </script>
</head>
<body class="bg-gray-100 font-sans">

<!-- Main Container -->
<div class="container mx-auto py-8">
    <div class="bg-white shadow-lg rounded-lg p-6 max-w-lg mx-auto" x-data="pricingCalculator">
        <h1 class="text-2xl font-bold mb-4 text-center">AI Model Pricing Calculator</h1>
        <p class="text-gray-600 mb-6 text-center">Calculate cost by Tokens, Words, or Characters</p>

        <!-- Model Selection -->
        <div class="mb-4">
            <label class="block font-semibold mb-2">Select Model:</label>
            <select x-model="selectedModel" class="w-full p-2 border rounded-lg bg-gray-50">
                <template x-for="model in models" :key="model.model">
                    <option :value="model.model" x-text="model.model"></option>
                </template>
            </select>
        </div>

        <!-- Calculation Type Selection -->
        <div>
            <label class="block mb-2 font-semibold">Calculate By:</label>
            <select x-model="calculationType" class="mb-4 w-full p-2 border rounded-lg bg-gray-50">
                <option value="tokens">Tokens</option>
                <option value="words">Words</option>
                <option value="characters">Characters</option>
            </select>

            <!-- Input Tokens Field -->
            <div class="mb-4">
                <label class="block font-semibold mb-2">Input <span x-text="calculationType.charAt(0).toUpperCase() + calculationType.slice(1)"></span>:</label>
                <input type="number" x-model="inputTokens" class="w-full p-2 border rounded-lg bg-gray-50" placeholder="Enter Input">
            </div>

            <!-- Output Tokens Field -->
            <div class="mb-4">
                <label class="block font-semibold mb-2">Output <span x-text="calculationType.charAt(0).toUpperCase() + calculationType.slice(1)"></span>:</label>
                <input type="number" x-model="outputTokens" class="w-full p-2 border rounded-lg bg-gray-50" placeholder="Enter Output">
            </div>

            <!-- API Calls Field -->
            <div class="mb-4">
                <label class="block font-semibold mb-2">API Calls:</label>
                <input type="number" x-model="apiCalls" class="w-full p-2 border rounded-lg bg-gray-50" placeholder="Enter API Calls">
            </div>

            <!-- Calculate Button -->
            <button @click="alert(`Total Cost: $${calculatePrice()}`)" class="w-full bg-blue-600 text-white py-2 rounded-lg mt-4 hover:bg-blue-700">Calculate Price</button>
        </div>
    </div>
</div>

</body>
</html>
