# SMSFrom - Multi-Tenant SMS Service Platform

<div align="center">

[![Laravel](https://img.shields.io/badge/Laravel-10.x-red.svg)](https://laravel.com)
[![PHP](https://img.shields.io/badge/PHP-8.0%2B-blue.svg)](https://php.net)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![MySQL](https://img.shields.io/badge/MySQL-8.0-orange.svg)](https://mysql.com)

*A comprehensive multi-tenant SaaS platform for SMS services with advanced features for businesses and resellers*

</div>

## 📋 Table of Contents

- [Overview](#overview)
- [Key Features](#key-features)
- [Architecture](#architecture)
- [Prerequisites](#prerequisites)
- [Installation](#installation)
- [Configuration](#configuration)
- [API Documentation](#api-documentation)
- [SMS Providers](#sms-providers)
- [Payment Gateways](#payment-gateways)
- [Usage Examples](#usage-examples)
- [Multi-Tenancy](#multi-tenancy)
- [Testing](#testing)
- [Deployment](#deployment)
- [Contributing](#contributing)
- [Support](#support)
- [License](#license)

## 🚀 Overview

SMSFrom is a powerful, multi-tenant SMS service platform built with Laravel 10. It enables businesses to provide SMS services to their clients with features like bulk messaging, scheduled SMS, dynamic content, contact management, and comprehensive reporting. The platform supports multiple SMS providers, payment gateways, and offers a complete white-label solution for SMS resellers.

### What Makes SMSFrom Special?

- **Multi-Tenant Architecture**: Isolated data and customizable branding per tenant
- **Multiple SMS Providers**: Support for RouteMobile, ElitBuzz, and extensible architecture
- **Advanced Messaging**: Bulk SMS, scheduled messages, dynamic content with variables
- **Payment Integration**: Multiple payment gateways including bKash, SSLCommerz, Stripe, PayPal
- **Comprehensive API**: RESTful API for seamless integration
- **Real-time Reporting**: Detailed analytics and delivery reports
- **Contact Management**: Groups, imports, and advanced contact organization

## ✨ Key Features

### 🔧 Core SMS Features
- **Bulk SMS Sending** - Send to thousands of recipients simultaneously
- **Scheduled Messaging** - Schedule SMS for future delivery
- **Dynamic SMS** - Personalized messages with variable substitution
- **Unicode Support** - Send messages in multiple languages
- **Message Templates** - Pre-defined message templates for quick sending
- **Delivery Reports** - Real-time delivery status and reporting

### 👥 Contact Management
- **Contact Groups** - Organize contacts into manageable groups
- **Excel Import/Export** - Bulk contact management via spreadsheets
- **Contact Validation** - Automatic phone number validation
- **Operator Detection** - Automatic mobile operator identification

### 💰 Business Features
- **Multi-Tenant Support** - Complete isolation between different businesses
- **Reseller Management** - Hierarchical company structure with parent-child relationships
- **Credit System** - Prepaid balance management with automatic deductions
- **Pricing Management** - Flexible pricing per operator and message type
- **Transaction History** - Comprehensive financial reporting

### 🔌 Integration & API
- **RESTful API** - Complete API for third-party integrations
- **Webhook Support** - Real-time notifications for message status
- **Multiple Authentication** - API key and username/password authentication
- **Rate Limiting** - Configurable API rate limiting per tenant

### 💳 Payment Processing
- **Multiple Gateways** - bKash, SSLCommerz, Stripe, PayPal, Manual payments
- **Automatic Recharge** - Seamless balance top-up process
- **Gateway Fees** - Configurable fees per payment method
- **Transaction Tracking** - Complete payment audit trail

## 🏗️ Architecture

SMSFrom follows a modern, scalable architecture:

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Web Interface │    │   RESTful API    │    │  Admin Panel    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │              Laravel Application              │
         │  ┌─────────────────────────────────────────┐  │
         │  │         Multi-Tenant Manager           │  │
         │  └─────────────────────────────────────────┘  │
         │  ┌─────────────┐  ┌─────────────┐  ┌────────┐ │
         │  │   Message   │  │   Payment   │  │  User  │ │
         │  │   Service   │  │   Service   │  │Service │ │
         │  └─────────────┘  └─────────────┘  └────────┘ │
         └─────────────────────────────────────────────────┘
                                 │
    ┌────────────────────────────┼────────────────────────────┐
    │                            │                            │
┌───▼────┐              ┌───────▼────────┐              ┌────▼────┐
│  SMS   │              │    Payment     │              │  MySQL  │
│Providers│              │   Gateways     │              │Database │
│        │              │                │              │         │
│RouteMobile│            │ • bKash        │              │         │
│ ElitBuzz  │            │ • SSLCommerz   │              │         │
│ Custom    │            │ • Stripe       │              │         │
└───────────┘            │ • PayPal       │              └─────────┘
                         │ • Manual       │
                         └────────────────┘
```

## 📋 Prerequisites

Before installing SMSFrom, ensure your system meets these requirements:

### System Requirements
- **PHP**: 8.0 or higher
- **Web Server**: Apache/Nginx
- **Database**: MySQL 8.0+ or MariaDB 10.3+
- **Memory**: Minimum 512MB RAM (2GB+ recommended)
- **Storage**: At least 1GB free space

### Required PHP Extensions
```bash
php -m | grep -E "(openssl|pdo|mbstring|tokenizer|xml|ctype|json|bcmath|curl|fileinfo|gd)"
```

### Development Tools
- **Composer**: Latest version for dependency management
- **Node.js**: 16+ and npm for asset compilation
- **Git**: For version control

### Optional but Recommended
- **Redis**: For caching and session storage
- **Supervisor**: For queue processing
- **SSL Certificate**: For production deployment

## 🚀 Installation

### Method 1: Standard Installation

1. **Clone the Repository**
   ```bash
   git clone https://github.com/MamunHoque/SMSFrom.git
   cd SMSFrom
   ```

2. **Install PHP Dependencies**
   ```bash
   composer install --optimize-autoloader --no-dev
   ```

3. **Install Node.js Dependencies**
   ```bash
   npm install
   ```

4. **Environment Configuration**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

5. **Database Setup**
   ```bash
   # Create database
   mysql -u root -p -e "CREATE DATABASE smsfrom CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

   # Run migrations and seeders
   php artisan migrate --seed
   ```

6. **Build Assets**
   ```bash
   npm run build
   ```

7. **Set Permissions**
   ```bash
   chmod -R 755 storage bootstrap/cache
   chown -R www-data:www-data storage bootstrap/cache
   ```

8. **Start the Application**
   ```bash
   php artisan serve
   ```

### Method 2: Docker Installation

1. **Clone and Setup**
   ```bash
   git clone https://github.com/MamunHoque/SMSFrom.git
   cd SMSFrom
   cp .env.example .env
   ```

2. **Start with Docker Compose**
   ```bash
   docker-compose up -d
   ```

3. **Install Dependencies**
   ```bash
   docker-compose exec laravel.test composer install
   docker-compose exec laravel.test php artisan key:generate
   docker-compose exec laravel.test php artisan migrate --seed
   ```

### Default Login Credentials

After installation, you can login with:
- **Username**: `superadmin`
- **Password**: `superadmin123`
- **Email**: `<EMAIL>`

> ⚠️ **Security Note**: Change default credentials immediately after first login!

## ⚙️ Configuration

### Environment Variables

Configure your `.env` file with the following essential settings:

```bash
# Application
APP_NAME="SMSFrom"
APP_ENV=production
APP_DEBUG=false
APP_URL=https://yourdomain.com

# Database
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=smsfrom
DB_USERNAME=your_db_user
DB_PASSWORD=your_db_password

# Mail Configuration
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_app_password
MAIL_ENCRYPTION=tls

# Queue Configuration (Recommended for production)
QUEUE_CONNECTION=redis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379
```

### SMS Provider Configuration

#### RouteMobile Setup
```bash
# Add to your servers table via admin panel
Name: RouteMobile
API Link: http://apibd.rmlconnect.net/bulksms/personalizedbulksms
Username: YOUR_USERNAME
Password: YOUR_PASSWORD
```

#### ElitBuzz Setup
```bash
# Add to your servers table via admin panel
Name: ElitBuzz
API Link: http://panel.smsfrom.net/smsapi
API Key: YOUR_API_KEY
```

### Payment Gateway Configuration

#### bKash Configuration
```bash
BKASH_SANDBOX=false
BKASH_APP_KEY=your_app_key
BKASH_APP_SECRET=your_app_secret
BKASH_USERNAME=your_username
BKASH_PASSWORD=your_password
BKASH_CALLBACK_URL=https://yourdomain.com/payment/success
```

#### SSLCommerz Configuration
```bash
SSLCZ_TESTMODE=false
SSLCZ_STORE_ID=your_store_id
SSLCZ_STORE_PASSWORD=your_store_password
```

#### Stripe Configuration
```bash
STRIPE_KEY=pk_live_your_publishable_key
STRIPE_SECRET=sk_live_your_secret_key
```

### Queue Configuration for Production

1. **Install Supervisor**
   ```bash
   sudo apt-get install supervisor
   ```

2. **Create Supervisor Configuration**
   ```bash
   sudo nano /etc/supervisor/conf.d/smsfrom-worker.conf
   ```

3. **Add Configuration**
   ```ini
   [program:smsfrom-worker]
   process_name=%(program_name)s_%(process_num)02d
   command=php /path/to/smsfrom/artisan queue:work --sleep=3 --tries=3
   autostart=true
   autorestart=true
   user=www-data
   numprocs=8
   redirect_stderr=true
   stdout_logfile=/path/to/smsfrom/storage/logs/worker.log
   ```

4. **Start Supervisor**
   ```bash
   sudo supervisorctl reread
   sudo supervisorctl update
   sudo supervisorctl start smsfrom-worker:*
   ```

## 📡 API Documentation

For complete API documentation, please refer to [API_DOCUMENTATION.md](./API_DOCUMENTATION.md)

### Quick Start

SMSFrom provides a comprehensive RESTful API for sending SMS and managing your account.

#### Authentication
The API supports two authentication methods:
- **API Key Authentication**: Use your generated API key
- **Username/Password Authentication**: Use your login credentials

#### Send SMS Example
```bash
POST /api/sms-send
Content-Type: application/json

{
    "api_key": "YOUR_API_KEY",
    "sender_id": "YourSender",
    "phone_numbers": "**********,**********",
    "type": "text",
    "message": "Hello from SMSFrom API!"
}
```

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `api_key` | string | Yes* | Your API key |
| `username` | string | Yes* | Your username (alternative to api_key) |
| `password` | string | Yes* | Your password (required with username) |
| `sender_id` | string | Yes | Sender ID or phone number |
| `phone_numbers` | string | Yes | Comma-separated phone numbers |
| `message` | string | Yes | SMS content |
| `type` | string | No | Message type: `text`, `unicode`, `flash` (default: text) |
| `scheduled_date_time` | string | No | Schedule SMS (format: Y-m-d H:i:s) |

*Either `api_key` OR (`username` + `password`) is required

#### Example Requests

**GET Request:**
```bash
curl "https://yourdomain.com/api/sms-send?api_key=**********abcdef&sender_id=MyCompany&phone_numbers=**********,**********&type=text&message=Hello%20World"
```

**POST Request:**
```bash
curl -X POST https://yourdomain.com/api/sms-send \
  -H "Content-Type: application/json" \
  -d '{
    "api_key": "**********abcdef",
    "sender_id": "MyCompany",
    "phone_numbers": "**********,**********",
    "type": "text",
    "message": "Hello World"
  }'
```

**Scheduled SMS:**
```bash
curl -X POST https://yourdomain.com/api/sms-send \
  -H "Content-Type: application/json" \
  -d '{
    "api_key": "**********abcdef",
    "sender_id": "MyCompany",
    "phone_numbers": "**********",
    "type": "text",
    "message": "Scheduled message",
    "scheduled_date_time": "2024-12-25 10:00:00"
  }'
```

#### Response Format

**Success Response:**
```json
{
  "status": "success",
  "message": "SMS sent successfully",
  "data": [
    {
      "phone_number": "**********",
      "status": "sent",
      "message_id": "msg_123456",
      "cost": 0.50
    }
  ]
}
```

**Error Response:**
```json
{
  "status": "error",
  "error_code": "1001",
  "message": "Invalid API key"
}
```

#### Error Codes

| Code | Description |
|------|-------------|
| 1001 | Invalid API key or credentials |
| 1002 | Invalid sender ID |
| 1003 | Company not found |
| 1016 | Invalid scheduled date format |

## 📱 SMS Providers

SMSFrom supports multiple SMS providers with an extensible architecture:

### Supported Providers

#### 1. RouteMobile
- **Type**: HTTP API
- **Features**: Bulk SMS, Unicode support, Delivery reports
- **Authentication**: Username/Password
- **Supported Types**: Text, Flash, Unicode, Reserved

#### 2. ElitBuzz
- **Type**: HTTP API
- **Features**: High-speed delivery, Unicode support
- **Authentication**: API Key
- **Supported Types**: Text, Unicode

### Adding Custom SMS Providers

1. **Create Provider Class**
   ```php
   <?php
   namespace App\Http\Services\SmsServers\Servers;

   use App\Http\Services\SmsServers\SmsSendingBase;
   use App\Http\Services\SmsServers\SmsSendingInterface;

   class YourProvider extends SmsSendingBase implements SmsSendingInterface
   {
       public function send(array|string $to, string $message, ?string $sender = null, ?string $type = null)
       {
           // Your implementation here
           $response = Http::post($this->serverApiLink, [
               'to' => $to,
               'message' => $message,
               'from' => $sender
           ]);

           $this->response = $response;
           return $this->checkResponse($response);
       }

       private function checkResponse($response)
       {
           if (!$response->successful()) {
               $this->error = true;
           }
           return $this;
       }
   }
   ```

2. **Add to CommonHandler**
   ```php
   protected static $smsProviders = [
       'RouteMobile',
       'ElitBuzz',
       'YourProvider', // Add your provider
   ];
   ```

3. **Configure via Admin Panel**
   - Go to Admin → Servers
   - Add new server with your provider name
   - Configure API credentials

## 💳 Payment Gateways

SMSFrom supports multiple payment gateways for automatic balance recharge:

### Supported Gateways

#### 1. bKash (Bangladesh)
- **Type**: Mobile Financial Service
- **Features**: Instant payment, Webhook support
- **Currencies**: BDT
- **Test Mode**: Available

#### 2. SSLCommerz (Bangladesh)
- **Type**: Payment Gateway
- **Features**: Card payments, Mobile banking, Internet banking
- **Currencies**: BDT, USD, EUR, GBP
- **Test Mode**: Available

#### 3. Stripe (International)
- **Type**: Credit Card Processing
- **Features**: Global coverage, Strong security
- **Currencies**: 135+ currencies
- **Test Mode**: Available

#### 4. PayPal (International)
- **Type**: Digital Wallet
- **Features**: Global coverage, Buyer protection
- **Currencies**: 25+ currencies
- **Test Mode**: Available

#### 5. Manual Payment
- **Type**: Offline Payment
- **Features**: Bank transfer, Cash payment
- **Process**: Admin approval required

### Gateway Configuration

Each gateway can be configured with:
- **Credentials**: API keys, secrets, usernames
- **Test Mode**: Enable/disable sandbox mode
- **Gateway Fee**: Percentage or fixed fee
- **Status**: Enable/disable gateway

### Adding Custom Payment Gateways

1. **Create Gateway Class**
   ```php
   <?php
   namespace App\Http\Services\PaymentGateways\Gateways;

   use App\Http\Services\PaymentGateways\GatewayBindingInterface;

   class YourGateway implements GatewayBindingInterface
   {
       public function pay(Payment $payment)
       {
           // Redirect to payment gateway
           return redirect()->away($paymentUrl);
       }

       public function success()
       {
           // Handle successful payment
           return response()->json(['status' => 'success']);
       }

       public function ipn()
       {
           // Handle instant payment notification
           return $this->success();
       }
   }
   ```

2. **Register in CommonHandler**
   ```php
   protected static $paymentProviders = [
       'Ssl',
       'BkashPay',
       'Manual',
       'YourGateway', // Add your gateway
   ];
   ```








